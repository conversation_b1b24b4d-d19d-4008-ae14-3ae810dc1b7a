{"name": "bika.ai", "version": "1.9.1-alpha.22", "description": "Bika.ai Official JavaScript API Client", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.cjs.js"}}, "files": ["dist", "README.md"], "scripts": {}, "repository": {"type": "git", "url": "git+https://github.com/bika-ai/bika.js.git"}, "keywords": ["bika.ai", "bika", "apitable", "aitable", "airtable", "notion"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/bika-ai/bika.js/issues"}, "homepage": "https://github.com/bika-ai/bika.js#readme", "dependencies": {"axios": "^1.9.0"}}