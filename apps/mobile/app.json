{"expo": {"name": "<PERSON><PERSON>", "slug": "bika", "scheme": "bika", "version": "0.1.2", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "ai.bika.bika", "buildNumber": "4529", "usesIcloudStorage": true, "infoPlist": {"NSMicrophoneUsageDescription": "This app uses the microphone to record audio.", "NSPhotoLibraryUsageDescription": "This app uses the photo library to pick photos."}, "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff", "dark": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#000000"}}, "privacyManifests": {"NSPrivacyAccessedAPITypes": [{"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryDiskSpace", "NSPrivacyAccessedAPITypeReasons": ["E174.1"]}, {"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategorySystemBootTime", "NSPrivacyAccessedAPITypeReasons": ["35F9.1"]}, {"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryFileTimestamp", "NSPrivacyAccessedAPITypeReasons": ["C617.1"]}, {"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryUserDefaults", "NSPrivacyAccessedAPITypeReasons": ["CA92.1"]}]}, "associatedDomains": ["applinks:bika.ai"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "ai.bika.bika", "googleServicesFile": "./google-services.json", "permissions": ["android.permission.RECORD_AUDIO"], "userInterfaceStyle": "automatic"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["@react-native-voice/voice", {"microphonePermission": "$(PRODUCT_NAME) access the microphone to record audio.", "speechRecognitionPermission": "$(PRODUCT_NAME) to securely recognize user speech."}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera for video calls and taking photos."}], ["expo-image-picker", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos to let you share as avatar or attachment."}], "expo-font", "expo-localization", "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "4e87a6bc-ecc1-4154-a52d-9b0e0162cda8"}, "updates": {"assetPatternsToBeBundled": ["**/*"]}}, "owner": "apitable"}}