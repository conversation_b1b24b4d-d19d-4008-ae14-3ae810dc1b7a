import { adminRouter } from '@bika/domains/admin/apis/admin-router';
import { aiRouter } from '@bika/domains/ai/apis';
import { attachmentRouter } from '@bika/domains/attachment/apis';
import { authRouter } from '@bika/domains/auth/apis/auth-router';
import { automationRouter } from '@bika/domains/automation/apis';
import { trashRouter } from '@bika/domains/change/apis';
import { dashboardRouter } from '@bika/domains/dashboard/apis';
import { databaseRouter } from '@bika/domains/database/apis';
import { emailRouter } from '@bika/domains/email/api';
import { integrationRouter } from '@bika/domains/integration/apis';
import { missionRouter } from '@bika/domains/mission/apis';
import { nodeRouter, nodeShareRouter } from '@bika/domains/node/apis';
import { notificationRouter, smsRouter } from '@bika/domains/notification/apis';
import { pricingRouter, billingRouter, giftCodeRouter, appsumoRouter } from '@bika/domains/pricing/apis';
import { reportRouter } from '@bika/domains/report/apis';
import { Logger } from '@bika/domains/shared/server';
import { linkInvitationRouter, emailInvitationRouter, myRouter, spaceRouter } from '@bika/domains/space/apis';
import { remoteStorageRouter, systemRouter, unsplashRouter } from '@bika/domains/system/apis';
import { talkTrpcRouter } from '@bika/domains/talk/apis/talk-trpc-router';
import { templateRouter } from '@bika/domains/template/apis';
import { memberRouter, guestRouter, roleRouter, teamRouter, unitRouter } from '@bika/domains/unit/apis';
import { userRouter } from '@bika/domains/user/apis';
import { createCallerFactory, protectedProcedure, publicProcedure, router } from '@bika/server-orm/trpc';
import { ApiFetchRequestContext } from '@bika/types/user/vo';

export const appRouter = router({
  greeting: protectedProcedure.query(({ ctx }: { ctx: ApiFetchRequestContext }) => {
    Logger.info(`hello session id: ${ctx.session!.id}; BikaUserId: ${ctx.session!.userId}`);
    return { a: 'hello tRPC v10!' };
  }),
  heartbeat: publicProcedure.query(() => ({ datetime: new Date() })),
  attachment: attachmentRouter,
  admin: adminRouter,
  email: emailRouter,
  space: spaceRouter,
  talk: talkTrpcRouter,
  node: nodeRouter,
  share: nodeShareRouter,
  member: memberRouter,
  guest: guestRouter,
  database: databaseRouter,
  dashboard: dashboardRouter,
  automation: automationRouter,
  mission: missionRouter,
  notification: notificationRouter,
  sms: smsRouter,
  linkInvitation: linkInvitationRouter,
  emailInvitation: emailInvitationRouter,
  report: reportRouter,
  auth: authRouter,
  team: teamRouter,
  unit: unitRouter,
  role: roleRouter,
  user: userRouter,
  ai: aiRouter,
  my: myRouter,
  system: systemRouter,
  remoteStorage: remoteStorageRouter,
  template: templateRouter,
  pricing: pricingRouter,
  unsplash: unsplashRouter,
  integration: integrationRouter,
  billing: billingRouter,
  giftCode: giftCodeRouter,
  trash: trashRouter,
  appsumo: appsumoRouter,
});

export const createCaller = createCallerFactory(appRouter);
