import { trpcServer } from '@hono/trpc-server';
import type { FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { SiteAdminAPIs } from '@bika/domains/admin/apis';
import { AuthAPIs, SamlAPIs } from '@bika/domains/auth/apis';
import { AutomationAPIs } from '@bika/domains/automation/apis';
import { apis as edgeCallbackAPIs } from '@bika/domains/edge/apis';
import EmailAPIs from '@bika/domains/email/api/email-apis';
import twitterAPIs from '@bika/domains/integration/apis/twitter-api';
import BikaOpenAPIs from '@bika/domains/openapi/apis/bika/index';
import VikaOpenAPIs from '@bika/domains/openapi/apis/vika/index';
import { stripeAPIs, appsumoAPIs } from '@bika/domains/pricing/apis';
import { CronAPIs, sseApis } from '@bika/domains/system/apis';
import { createFetchRequestContext } from '@bika/server-orm/trpc';
import jsonSchemasAPIs from './json-schemas-api';
import systemAPIs from './system-apis';
import { appRouter } from './trpc-apis';

const app = new Hono();

app.use(logger());

// tRPC
app.use(
  '/api/trpc/*',
  cors({
    origin: ['http://localhost:3030', 'tauri://localhost'],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: [
      'Content-Type',
      'Authorization',
      'x-bika-locale',
      'x-bika-timezone',
      'x-bika-version',
      'x-trpc-source',
    ],
    exposeHeaders: ['Content-Length', 'X-Kuma-Revision'],
    maxAge: 600,
    credentials: true,
  }),
  trpcServer({
    endpoint: '/api/trpc',
    router: appRouter,
    createContext: async (opts: FetchCreateContextFnOptions) => {
      const context = await createFetchRequestContext(opts);

      return context as unknown as Record<string, unknown>;
    },
  }),
);

// 添加 CORS 中间件 for SSE
app.use(
  '/api/sse/*',
  cors({
    origin: ['http://localhost:3030', 'tauri://localhost'],
    allowMethods: ['GET', 'OPTIONS'],
    allowHeaders: [
      // 根据 SSE 客户端需要调整
      'Content-Type',
      'Authorization',
      'x-bika-locale',
      'x-bika-timezone',
      'x-bika-version',
      'x-trpc-source',
    ],
    exposeHeaders: ['Content-Length', 'X-Kuma-Revision'],
    maxAge: 600,
    credentials: true,
  }),
);

// 添加 CORS 中间件 for SSE
app.use(
  '/api/sse',
  cors({
    origin: ['http://localhost:3030', 'tauri://localhost'], // 允许所有来源，生产环境建议设置具体的域名
    allowMethods: ['GET', 'OPTIONS'], // SSE 通常只需要 GET
    allowHeaders: [
      // 根据 SSE 客户端需要调整
      'Content-Type',
      'Authorization',
      'x-bika-locale',
      'x-bika-timezone',
      'x-bika-version',
    ],
    exposeHeaders: ['Content-Length', 'X-Kuma-Revision'],
    maxAge: 600,
    credentials: true,
  }),
);

// 其余的所有api应用这个middleware， trpc不被应用
app.use('/*', async (_c, next) =>
  // sendAuditLog(c.req.raw, undefined, undefined, 'http', c.req.url, c.req.raw.body);
  next(),
);

export const apiRoutes = app
  // Home, System
  .route('/', systemAPIs)
  .route('/api', systemAPIs)
  .route('/api/automation', AutomationAPIs)
  .route('/api/sse', sseApis)
  .route('/api/schema', jsonSchemasAPIs)
  // 站点级API
  .route('/api/site-admin', SiteAdminAPIs)
  // Saml SP 租户开放API
  .route('/api/saml', SamlAPIs)
  // Cron
  .route('/api/cron', CronAPIs)
  // Edge Server Callback
  .route('/api/edge', edgeCallbackAPIs)
  .route('/api/stripe', stripeAPIs)
  .route('/api/appsumo', appsumoAPIs)
  .get('/api/openapi/bika', async (c) => c.json({ message: 'Bika OpenAPI Entry' }))
  .route('/api/openapi/bika', BikaOpenAPIs)
  .route('/api/openapi/vika/fusion/v1', VikaOpenAPIs)
  .route('/api/openapi/aitable/fusion/v1', VikaOpenAPIs)
  .route('/api/openapi/apitable/fusion/v1', VikaOpenAPIs)
  // Auth Callback
  .route('/api/auth', AuthAPIs)
  .route('/api/email', EmailAPIs)
  .route('/api/integration/twitter', twitterAPIs);

// Non tRPC APIs Audit Log (tRPC middleware里另外做了audit log)

// tRPC Adapter
// TODO: Fetch Context

export default app;
