import { Hono } from 'hono';
import { getReleaseDate } from 'sharelib/app-env';
import { getMeta } from '@bika/domains/system/apis/system-controller';

// System / Health Check

const app = new Hono();

app.get('/', async (c) =>
  c.json({
    version: process.env.VERSION,
    hostname: process.env.APP_HOSTNAME,
    releaseDate: getReleaseDate(),
  }),
);

// base64解码，并redirect
app.get('/redirect', async (c) => {
  const { r } = c.req.query();
  const url = atob(r);
  return c.redirect(url);
});

app.get('/meta', async (c) => c.json(await getMeta(c)));

app.get('/cache', async (c) =>
  c.json({
    hostname: process.env.APP_HOSTNAME,
    releaseDate: getReleaseDate(),
  }),
);

export default app;
