import { adminRouter } from '@bika/domains/admin/apis/admin-router';
import Logger from '@bika/domains/shared/server/utils/logger/logger';
import { systemRouter } from '@bika/domains/system/apis';
import { createCallerFactory, protectedProcedure, router } from '@bika/server-orm/trpc';
import { ApiFetchRequestContext } from '@bika/types/user/vo';

export const localEditorTRPCRouter = router({
  greeting: protectedProcedure.query(({ ctx }: { ctx: ApiFetchRequestContext }) => {
    Logger.info(`hello session id: ${ctx.session!.id}; BikaUserId: ${ctx.session!.userId}`);
    return { a: 'hello tRPC v10!' };
  }),
  //   heartbeat: publicProcedure.query(() => ({ datetime: new Date() })),
  //   attachment: attachmentRouter,
  admin: adminRouter,
  //   email: emailRouter,
  //   space: spaceRouter,
  //   node: nodeRouter,
  //   share: nodeShareRouter,
  //   form: formRouter,
  //   member: memberRouter,
  //   database: databaseRouter,
  //   dashboard: dashboardRouter,
  //   automation: automationRouter,
  //   mission: missionRouter,
  //   notification: notificationRouter,
  //   sms: smsRouter,
  //   invitation: invitationRouter,
  //   report: reportRouter,
  //   auth: authRouter,
  //   team: teamRouter,
  //   unit: unitRouter,
  //   role: roleRouter,
  //   user: userRouter,
  //   ai: aiRouter,
  //   my: myRouter,
  system: systemRouter,
  //   remoteStorage: remoteStorageRouter,
  //   template: templateRouter,
  //   pricing: pricingRouter,
  //   unsplash: unsplashRouter,
  //   integration: integrationRouter,
});

export const createCaller = createCallerFactory(localEditorTRPCRouter);
