import { trpcServer } from '@hono/trpc-server';
import type { FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';
import { Hono } from 'hono';
import { compress } from 'hono/compress';
import { logger } from 'hono/logger';
// import { apis as edgeCallbackAPIs } from '@bika/domains/edge/apis';
// import { sendAuditLog } from '@bika/server-orm/utils';
import { createFetchRequestContext } from '@bika/server-orm/trpc';
import { localEditorTRPCRouter } from './editor-trpc-only';
import systemAPIs from '../../apis/system-apis';
// import { appRouter } from './trpc-apis';

const app = new Hono();

app.use(logger());
app.use(compress());

// tRPC
app.use(
  '/api/trpc/*',
  trpcServer({
    endpoint: '/api/trpc',
    router: localEditorTRPCRouter,
    createContext: async (opts: FetchCreateContextFnOptions) => {
      const context = await createFetchRequestContext(opts);

      return context as unknown as Record<string, unknown>;
    },
  }),
);

// 其余的所有api应用这个middleware， trpc不被应用
app.use('/*', async (c, next) =>
  // sendAuditLog(c.req.raw, undefined, undefined, 'http', c.req.url, c.req.raw.body);
  next(),
);

export const apiRoutes = app
  // Home, System
  // .route('/', systemAPIs)
  .route('/api', systemAPIs);

export default app;
