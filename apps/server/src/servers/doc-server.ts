import { Database } from '@hocuspocus/extension-database';
import { Logger } from '@hocuspocus/extension-logger';
import { Redis, Configuration } from '@hocuspocus/extension-redis';
import { Hocuspocus } from '@hocuspocus/server';
import { ConnectionString } from 'connection-string';
import { getAppEnv } from 'sharelib/app-env';
import * as Y from 'yjs';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { DocSO } from '@bika/domains/doc/server/doc-so';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { NodeSO } from '@bika/domains/node/server';
import { SpaceAttachmentSO } from '@bika/domains/space/server/space-attachment-so';
import { db, MongoTransactionCB, PrismaPromise } from '@bika/server-orm';

function getRedisConf(): Partial<Configuration> {
  const url = process.env.REDIS_URL || 'redis://default:@redis-master:6379';
  const cs = new ConnectionString(url);
  return {
    host: cs.hostname!,
    port: cs.port!,
    options: {
      username: cs.user,
      password: cs.password,
    },
  };
}

function stateToDoc(state?: Uint8Array): Y.Doc {
  const doc = new Y.Doc();
  if (state) {
    Y.applyUpdate(doc, state);
  }
  return doc;
}

/**
 * 提取文档中的媒体元素
 */
function extractMediaElement(document: Y.Doc): Y.XmlElement[] {
  const xmlFragment = document.get('default', Y.XmlFragment);
  const images: Y.XmlElement[] = xmlFragment
    .slice(0, xmlFragment.length)
    .filter(
      (element): element is Y.XmlElement =>
        element instanceof Y.XmlElement && (element.nodeName === 'image' || element.nodeName === 'video'),
    );
  return images;
}

function diffMediaElement(current: Y.XmlElement[], previous: Y.XmlElement[]): { added: string[]; deleted: string[] } {
  const added: string[] = [];
  const deleted: string[] = [];

  // 计算 currentImages 中每个图像的出现次数
  const currentImageCounts = current.reduce<{ [key: string]: number }>(
    (counts, image) => {
      const id = image.getAttribute('id');
      if (id) {
        return {
          ...counts,
          [id]: (counts[id] || 0) + 1,
        };
      }
      return counts;
    },
    {} as Record<string, number>,
  );

  // 计算 previousImages 中每个图像的出现次数
  const previousImageCounts = previous.reduce<{ [key: string]: number }>(
    (counts, image) => {
      const id = image.getAttribute('id');
      if (id) {
        return {
          ...counts,
          [id]: (counts[id] || 0) + 1,
        };
      }
      return counts;
    },
    {} as Record<string, number>,
  );

  // 计算新增的图像
  for (const [id, count] of Object.entries(currentImageCounts)) {
    const previousCount = previousImageCounts[id] || 0;
    if (count > previousCount) {
      for (let i = 0; i < count - previousCount; i++) {
        added.push(id);
      }
    }
  }

  // 计算删除的图像
  for (const [id, count] of Object.entries(previousImageCounts)) {
    const currentCount = currentImageCounts[id] || 0;
    if (count > currentCount) {
      for (let i = 0; i < count - currentCount; i++) {
        deleted.push(id);
      }
    }
  }

  return { added, deleted };
}

// Configure the doc server …
const server = new Hocuspocus({
  name: `bika-doc-server-${getAppEnv()}-${Date.now()}`.toLowerCase(),
  port: 3366,
  extensions: [
    new Logger({
      log: (message) => {
        console.log(message);
      },
    }),
    new Redis(getRedisConf()),
    new Database({
      // Return a Promise to retrieve data …
      fetch: async ({ documentName }) => {
        const docData = await DocSO.getDocumentData(documentName);

        let uint8Array: Uint8Array | null;

        if (docData !== undefined) {
          uint8Array = docData ? new Uint8Array(docData) : null;
        } else {
          uint8Array = null; // or handle the undefined case as needed
        }
        console.log('Document data fetch: ', documentName, uint8Array?.length);
        return uint8Array;
      },

      // … and a Promise to store data:
      store: async ({ socketId, documentName, document: currentDocument, state, requestParameters }) => {
        console.log(`Document: ${documentName} data store, socket id: ${socketId}`);
        // 用户标识
        const userId = requestParameters.get('userId');
        // 当前文档对象
        const docSO = await DocSO.init(documentName);
        // 之前的文档状态
        const previousState = docSO.data && new Uint8Array(docSO.data);
        const previousDocument = stateToDoc(previousState ?? undefined);
        const previousMediaElements = extractMediaElement(previousDocument);
        // previousMediaElements.forEach((media) => {
        //   console.log(
        //     `previous = src: ${media.getAttribute('src')} id: ${media.getAttribute('id')} -- json: ${media.toJSON()}`,
        //   );
        // });

        // 现在的文档状态
        const currentMediaElements = extractMediaElement(currentDocument);
        // currentMediaElements.forEach((media) => {
        //   console.log(
        //     `current = src: ${media.getAttribute('src')} id: ${media.getAttribute('id')} -- json: ${media.toJSON()}`,
        //   );
        // });

        // 比较两个媒体元素数组, 生成差异, add, delete 分别是多少
        const { added, deleted } = diffMediaElement(currentMediaElements, previousMediaElements);
        // console.log('added:', added);
        // console.log('deleted:', deleted);

        const addedAttachments = await AttachmentSO.findMany(added);
        const space = await docSO.getSpace();
        if (addedAttachments.length > 0) {
          // 有新增的附件, 必须校验空间容量
          const entitlement = await space.getEntitlement();
          const validateSize = addedAttachments.reduce((size, attachment) => size + attachment.size, 0);
          await entitlement.checkUsageExceed({ feature: 'STORAGES', value: validateSize });
        }

        // 收集新增附件的操作
        const addSessions: [MongoTransactionCB, PrismaPromise<unknown>][] = await Promise.all(
          addedAttachments.map(async (attachment) => {
            const attachmentRefSession = await SpaceAttachmentSO.createSession(
              userId ?? '',
              space,
              {
                id: attachment.id,
                size: attachment.size,
              },
              {
                type: 'RESOURCE',
                id: docSO.id,
              },
            );
            const attachmentRefAdjustOperation = AttachmentSO.adjustRefCountOperation(attachment.id, 1);
            return [attachmentRefSession, attachmentRefAdjustOperation];
          }),
        );

        // 收集删除附件的操作
        const toDeletedAttachments = await AttachmentSO.findMany(deleted);
        const deleteSessions: [MongoTransactionCB, PrismaPromise<unknown>][] = await Promise.all(
          toDeletedAttachments.map(async (attachment) => {
            const attachmentRefSession = await SpaceAttachmentSO.deleteOneSession({
              spaceId: docSO.model.spaceId,
              attachmentId: attachment.id,
              attachmentRef: {
                type: 'RESOURCE',
                id: docSO.id,
              },
            });
            const attachmentRefAdjustOperation = AttachmentSO.adjustRefCountOperation(attachment.id, -1);
            return [attachmentRefSession, attachmentRefAdjustOperation];
          }),
        );

        // 附件引用的操作
        const mongoSessions: MongoTransactionCB[] = [...deleteSessions, ...addSessions].map(([session]) => session);
        // 附件引用数量调整的操作
        const operations = [...deleteSessions, ...addSessions].map(([, operation]) => operation);
        // 更新文档数据状态的操作
        const updateStateOperation = DocSO.updateDocumentData(userId ?? '', docSO.id, state);

        await db.mongo.transaction(async (session) => {
          for (const mongoSession of mongoSessions) {
            await mongoSession(session);
          }
          await db.prisma.$transaction([...operations, updateStateOperation]);
        });
        try {
          const updatedDoc = await NodeSO.init(docSO.id);
          await EventSO.node.onUpdate(updatedDoc);
        } catch (e) {
          console.log(e);
        }
      },
    }),
  ],
});

// … and run it!
server.listen();
