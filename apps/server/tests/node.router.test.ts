import { sleep } from 'sharelib/sleep';
import { describe, test, expect, afterEach } from 'vitest';
import { TRPCError } from '@bika/server-orm/trpc';
import { createCaller } from '../src/apis/trpc-apis';

describe(
  'Node Router',
  () => {
    afterEach(async () => {
      await sleep(1000);
    });
    test.sequential('rate limit--root--1 count', async () => {
      const trpc = createCaller({
        req: {
          headers: new Headers({
            'x-forwarded-for': '127.0.0.1',
          }),
        } as Request,
        session: {
          id: '1',
          userId: '1',
          expiresAt: new Date().toISOString(),
          fresh: true,
        },
        locale: 'en',
      });
      try {
        await trpc.node.root({
          spaceId: '1',
        });
      } catch (err) {
        if (err instanceof TRPCError) {
          expect(err.code).not.toBe('TOO_MANY_REQUESTS');
        }
      }
    });

    test.sequential('rate limit--root--5 count', async () => {
      const trpc = createCaller({
        req: {
          headers: new Headers({
            'x-forwarded-for': '127.0.0.1',
          }),
        } as Request,
        session: {
          id: '1',
          userId: '1',
          expiresAt: new Date().toISOString(),
          fresh: true,
        },
        locale: 'en',
      });
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          trpc.node.root({
            spaceId: '1',
          }),
        );
      }
      const results = await Promise.allSettled(promises);
      for (const result of results) {
        if (result.status === 'rejected') {
          expect(result.reason instanceof TRPCError).toBe(true);
          expect(result.reason.code).not.toBe('TOO_MANY_REQUESTS');
        }
      }
    });

    test.sequential('rate limit--root--6 count--false', async () => {
      const trpc = createCaller({
        req: {
          headers: new Headers({
            'x-forwarded-for': '127.0.0.1',
          }),
        } as Request,
        session: {
          id: '1',
          userId: '1',
          expiresAt: new Date().toISOString(),
          fresh: true,
        },
        locale: 'en',
      });
      const promises = [];
      for (let i = 0; i < 6; i++) {
        promises.push(
          trpc.node.root({
            spaceId: '1',
          }),
        );
      }
      const results = await Promise.allSettled(promises);
      const frequentResult = results[5];
      expect(frequentResult.status).toBe('rejected');
      if (frequentResult.status === 'rejected') {
        expect(frequentResult.reason instanceof TRPCError).toBe(true);
        expect(frequentResult.reason.code).toBe('TOO_MANY_REQUESTS');
      }
    });
  },
  {
    skip: true,
  },
);
