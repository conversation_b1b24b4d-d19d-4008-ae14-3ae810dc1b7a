import { AuthController } from '@bika/domains/auth/apis/auth-controller';
import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { SpaceError } from '@bika/domains/space/client/error';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { TemplateSeoMeta } from '@bika/domains/template/client/template-seo-meta';
import { TemplateDetailPage } from '@bika/domains/website/client/template/detail';
import { type Locale, iStringParse } from '@bika/types/i18n/bo';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import React from 'react';

interface RouteParams {
  lang: string;
  templateId: string | string[];
}

export interface PageProps {
  params: Promise<RouteParams>;
}

// SEO 临时处理
function recursiveTransform(data: any, lang: Locale): any {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  if (lang in data || 'en' in data) {
    return iStringParse(data, lang);
  }

  if (Array.isArray(data)) {
    return data.map((item) => recursiveTransform(item, lang));
  }

  const newObj: Record<string, unknown> = {};

  for (const key in data) {
    newObj[key] = recursiveTransform(data[key], lang);
  }
  return newObj;
}

export default async function Page(props: PageProps) {
  const authVO = await AuthController.getAuthByNextRequest(await headers());
  const params = await props.params;

  const templateId =
    typeof params.templateId === 'string' ? params.templateId.toLowerCase() : params.templateId.join('/');
  const storeTemplateSO = await StoreTemplateSO.init(decodeURIComponent(templateId));
  try {
    const templateRepoVO = await storeTemplateSO.toVO(authVO?.user.id);
    // @ts-expect-error 模板详情不需要releases 前端动态获取的
    delete templateRepoVO.releases;
    const data = recursiveTransform(templateRepoVO, params.lang as Locale);

    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'Bika.ai',
              url: process.env.APP_HOSTNAME,
              logo: `${process.env.APP_HOSTNAME}/assets/icons/logo/bika-logo-icon.png`,
            }),
          }}
        />
        <TemplateSeoMeta template={data} locale={params.lang as Locale} />
        <TemplateDetailPage hiddenIncludeResources={false} templateRepo={data} />
      </>
    );
  } catch (_e) {
    return <SpaceError errorType={'NoAccess'} />;
  }
}

export async function generateMetadata(props: PageProps): Promise<Metadata> {
  try {
    const params = await props.params;
    const templateId = typeof params.templateId === 'string' ? params.templateId : params.templateId.join('/');

    const storeTemplateSO = await StoreTemplateSO.init(decodeURIComponent(templateId));

    return {
      title: iStringParse(storeTemplateSO.name, params.lang as Locale),
      description: iStringParse(storeTemplateSO.description, params.lang as Locale),
      keywords: iStringParse(storeTemplateSO.keywords, params.lang as Locale),

      openGraph: {
        title: iStringParse(storeTemplateSO.name, params.lang as Locale),
        description: iStringParse(storeTemplateSO.description, params.lang as Locale),
        type: 'website',
        locale: params.lang,
      },
      twitter: {
        card: 'summary_large_image',
        site: '@bika_ai',
        title: iStringParse(storeTemplateSO.name, params.lang as Locale),
        description: iStringParse(storeTemplateSO.description, params.lang as Locale),
        creator: '@bika_ai',
      },
      alternates: {
        languages: await generateLanguageUrls(`template/${templateId}`),
      },
    };
  } catch (e) {
    return notFound();
  }
}
