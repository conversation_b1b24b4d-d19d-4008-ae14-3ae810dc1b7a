import type { Locale } from '@bika/contents/i18n';
import { getServerDictionary } from '@bika/contents/i18n/server';
import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { LandingPage } from '@bika/domains/website/client/landing-page/index';
import { postprocessPageMetadata } from '@bika/domains/website/server/utils';
import { LocalContentLoader, db } from '@bika/server-orm';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';

interface PageProps {
  params: Promise<{
    lang: Locale;
  }>;
}

export default async function HomePage(props: PageProps) {
  db.log.write({
    kind: 'KV_LOG',
    key: 'WEBSITE_HOME_AB_TEST',
    value: 'LANDING_PAGE_HOME',
  });

  const { lang } = await props.params;
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: '<PERSON>ika.ai',
    url: process.env.APP_HOSTNAME,
    logo: `${process.env.APP_HOSTNAME}/assets/icons/logo/bika-logo-icon.png`,
    sameAs: ['https://x.com/bika_ai', 'https://www.linkedin.com/company/bika-ai'],
    contactPoint: [
      {
        '@type': 'ContactPoint',
        // telephone: '+86-xxx-xxxx-xxxx',
        contactType: 'Customer service',
        areaServed: 'Global',
      },
    ],
  };

  try {
    const page = await LocalContentLoader.landingPage.autoLandingPage(lang, ['index']);
    return (
      <>
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }} />
        <LandingPage lang={lang} data={page!} />
      </>
    );
  } catch (error) {
    console.error(error);
    return notFound();
  }
}

export async function generateMetadata(params: PageProps): Promise<Metadata> {
  const { lang } = await params.params;
  const page = await LocalContentLoader.landingPage.autoLandingPage(lang, ['index']);
  if (!page) {
    return {};
  }
  const dict = getServerDictionary(lang);

  const sloganTitle = dict.slogan.slogan_title;
  const metadata = postprocessPageMetadata(page!.metadata, lang, false);

  // 首页，强制 title
  metadata.title = {
    absolute: sloganTitle,
  };
  if (metadata.openGraph) {
    metadata.openGraph.title = sloganTitle;
  }

  return {
    ...metadata,
    alternates: {
      languages: await generateLanguageUrls(`blog`),
    },
  };
}

// export async function generateStaticParams() {
//   // eg. { lang: 'en' } | { lang: 'zh-TW' } | { lang: 'zh-CN' } | { lang: 'ja' }
//   return i18n.locales.map((lang) => ({ lang }));
// }
