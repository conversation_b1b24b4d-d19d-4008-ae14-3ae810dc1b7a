import type { Locale } from '@bika/contents/i18n';
import { getServerDictionary, getServerLocaleContext } from '@bika/contents/i18n/server';
import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { BlogHome } from '@bika/domains/website/client/blog/index';
import { LocalContentLoader } from '@bika/server-orm';
import type { PageProps } from '@bika/types/content/bo';
// import { getAppEnv } from 'sharelib/app-env';
import { ExtendLocale } from 'basenext/i18n/config';
import { Metadata } from 'next';
import React from 'react';
import { getAllBlogUrlsProps } from '../../sitemap/[lang]/blog-sitemap/[page]/route';

export default async function Page(props: {
  params: Promise<{ lang: string }>;
  searchParams: Promise<{ p: string | undefined }>;
}) {
  const params = await props.params;
  const { p } = await props.searchParams;
  // const appEnv = getAppEnv();
  let blogs: PageProps[];
  const showAllBlogs = true; // appEnv !== 'PRODUCTION';

  if (showAllBlogs) {
    blogs = await getAllBlogUrlsProps(params.lang as ExtendLocale, true, undefined, true);
  } else {
    blogs = await LocalContentLoader.blog.autoBlogsList(params.lang, true);
  }

  const localeContext = await getServerLocaleContext(params.lang as Locale);

  return (
    <BlogHome
      localeContext={localeContext}
      currentPage={p ? parseInt(p, 10) : 1}
      data={blogs}
      // defaultPage={searchParams.p ? parseInt(searchParams.p, 10) : 1}
    />
  );
  // return (
  //   <>
  //     <div className="flex justify-center">
  //       <h1 className="text-4xl font-bold">Blog</h1>
  //     </div>
  //     {files.map((file) => {
  //       const url = `/${file.lang}/blog/${file.slugs.join('/')}`;
  //       return (
  //         <div className="flex" key={url}>
  //           <Link href={url as Route}>{file.metadata?.title || url}</Link>
  //         </div>
  //       );
  //     })}
  //   </>
  // );
}

export async function generateMetadata(props: { params: Promise<{ lang: string }> }): Promise<Metadata> {
  const params = await props.params;

  const t = getServerDictionary(params.lang as Locale);

  return {
    title: t.website.blog,
    alternates: {
      languages: await generateLanguageUrls(`blog`),
    },
  };
}
// export async function generateMetadata(): Promise<Metadata> {
//   return {
//     title: frontMatter.title,
//     description: frontMatter.description,
//     keywords: frontMatter.keywords,
//     openGraph: {
//       title: frontMatter.title,
//       type: 'article',
//       description: frontMatter.description,
//       images: [{ url: frontMatter.cover }],
//     },
//     twitter: {
//       card: 'summary_large_image',
//       site: '@bika_ai',
//       title: frontMatter.title,
//       description: frontMatter.description,
//       images: frontMatter.cover,
//       creator: '@bika_ai',
//     },
//     alternates: {
//       languages: generateLanguageUrls(`blog/${slug.join('/')}`),
//     },
//   };
// }
