import type { Locale } from '@bika/contents/i18n';
import { getServerDictionary } from '@bika/contents/i18n/server';
import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { ArticleContent } from '@bika/domains/website/client/article';
import { AIGCSO } from '@bika/domains/website/server/aigc/aigc-so';
import { extractTitlesFromMarkdown } from '@bika/domains/website/server/utils';
import { LocalContentLoader } from '@bika/server-orm';
import { iStringParse } from '@bika/types/i18n/bo';
import type { TemplateCardInfoVO } from '@bika/types/template/vo';
import { FlowEditor } from '@bika/ui/editor/flow-editor/flow-editor';
import { MarkdownBody, Markdown } from '@bika/ui/markdown';
import { TemplateInfoCard } from '@bika/ui/template-card';
import { TextH3Component } from '@bika/ui/texts';
import Box from '@mui/joy/Box';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import React from 'react';
import style from '../../[...slugs]/index.module.css';

/**
 * 一篇博客文章，展示Template的README
 */
export default async function BlogForTemplate(props: {
  params: Promise<{ lang: string; templateId: string | string[] }>;
}) {
  try {
    const params = await props.params;
    const templateId = (
      typeof params.templateId === 'string' ? params.templateId : params.templateId.join('/')
    ).toLowerCase();

    const templateRepo = await LocalContentLoader.template.autoLoadTemplateRepo(templateId);
    const locale = params.lang as Locale;
    let author = 'Bika.ai';
    if (templateRepo.author) {
      if (typeof templateRepo.author === 'string') {
        author = templateRepo.author;
      } else if (templateRepo.author.display === 'SPACE') author = templateRepo.author.spaceId;
      else author = templateRepo.author.userId;
    }
    let cover = '';
    if (typeof templateRepo.cover === 'string') {
      cover = templateRepo.cover;
    }

    const name = iStringParse(templateRepo.name, locale);
    const desc = iStringParse(templateRepo.description, locale);
    const readme = iStringParse(templateRepo.readme, locale);
    const templateCardInfo = (
      <TemplateInfoCard
        href={`/${locale}/template/${templateId}`}
        templateInfo={{
          name,
          templateId: templateRepo.templateId,
          category: templateRepo.category,
          description: desc,
          cover: typeof templateRepo.cover === 'string' ? templateRepo.cover : '',
          stars: undefined,
          verified: undefined,
          visibility: undefined,
        }}
        buttinText={''}
      />
    );

    const titles = extractTitlesFromMarkdown(readme);

    const randomTemplatesSOs = await StoreTemplateSO.getRandomTemplates();
    const templateVOs: TemplateCardInfoVO[] = [];
    for (const tplSO of randomTemplatesSOs) {
      const tplVO = await tplSO.toInfoVO(locale);
      templateVOs.push(tplVO);
    }

    const relatedLinks = await AIGCSO.getRandomBlogsLinks(locale as Locale);

    return (
      <div className={style.blog}>
        <ArticleContent
          relatedLinks={relatedLinks}
          templateInfos={templateVOs}
          locale={locale}
          meta={{
            title: name,
            date: undefined,
            author,
            cover,
          }}
          toc={titles.map((title) => ({ name: title.title, href: '#', level: title.level as 1 | 2 | 3 | 4 }))}
          beforeContent={
            <>
              <MarkdownBody>
                <blockquote>
                  <p>{desc}</p>
                </blockquote>
                <>{templateCardInfo}</>
                <TextH3Component>Overview</TextH3Component>
              </MarkdownBody>
              <Box style={{ width: '100%', height: 500 }}>
                <FlowEditor
                  readonly
                  showControl
                  openNewWindowButtonAndUrl={`/${locale}/template/${templateId}`}
                  data={{
                    type: 'node-resources',
                    resources: templateRepo.current.data.resources,
                  }}
                ></FlowEditor>
              </Box>
            </>
          }
          afterContent={<>{templateCardInfo}</>}
        >
          <Markdown markdown={readme} />
        </ArticleContent>
      </div>
    );
  } catch (e) {
    return notFound();
  }
}

export async function generateMetadata(props: {
  params: Promise<{ lang: string; templateId: string | string[] }>;
}): Promise<Metadata> {
  try {
    const params = await props.params;
    const templateId = (
      typeof params.templateId === 'string' ? params.templateId : params.templateId.join('/')
    ).toLowerCase();

    const storeTemplateSO = await StoreTemplateSO.init(decodeURIComponent(templateId));
    const t = getServerDictionary(params.lang as Locale);
    return {
      title: `${iStringParse(storeTemplateSO.name, params.lang as Locale)} - ${t.template.readme}`,
      description: iStringParse(storeTemplateSO.description, params.lang as Locale),
      keywords: iStringParse(storeTemplateSO.keywords, params.lang as Locale),

      openGraph: {
        title: iStringParse(storeTemplateSO.name, params.lang as Locale),
        description: iStringParse(storeTemplateSO.description, params.lang as Locale),
        type: 'website',
        locale: params.lang,
      },
      twitter: {
        card: 'summary_large_image',
        site: '@bika_ai',
        title: iStringParse(storeTemplateSO.name, params.lang as Locale),
        description: iStringParse(storeTemplateSO.description, params.lang as Locale),
        creator: '@bika_ai',
      },
      alternates: {
        languages: await generateLanguageUrls(`template/${templateId}`, 'en', async (locale) => {
          try {
            const ret = await StoreTemplateSO.init(decodeURIComponent(templateId));
            return true;
          } catch (e) {
            return false;
          }
        }),
      },
    };
  } catch (e) {
    return notFound();
  }
}
