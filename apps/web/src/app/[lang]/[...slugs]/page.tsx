import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { LandingPage } from '@bika/domains/website/client/landing-page/index';
import { postprocessPageMetadata } from '@bika/domains/website/server/utils';
import { LocalContentLoader } from '@bika/server-orm';
import { type Locale, i18n } from '@bika/types/i18n/bo';
import _ from 'lodash';
import type { Metadata } from 'next';
import { notFound, redirect, RedirectType } from 'next/navigation';

interface RouteParams {
  slugs: string[];
  lang: Locale;
}

export interface PageProps {
  params: Promise<RouteParams>;
}

// 自动fallback处理地加载落地页
// 1. 首先找对应语言的landingPage
// 2. 如果没有，遍历其它所有语言找，找到就redirect过去
// 3. 如果还是没有，返回404
async function fallbackLoadLandingPage(lang: string, slugs: string[]) {
  // try {
  const lp = await LocalContentLoader.landingPage.autoLandingPage(lang, slugs);
  if (!lp) {
     
    console.warn(`[WARN]请及时补充多语言模板，start fallback landing page, ${lang}/${slugs.join('/')}`);

    // 找不到想要的语言的落地页
    // 遍历其它语言找一遍，增强有些网页没有配置多语言，进行fallback
    for (const otherLang of _.without(i18n.locales, lang)) {
      const otherLandingPage = await LocalContentLoader.landingPage.autoLandingPage(otherLang, slugs);
      if (otherLandingPage) {
        redirect(`/${otherLang}/${slugs.join('/')}`, RedirectType.replace);
        return otherLandingPage;
      }
    }

    return null;
  }

  return lp;
}

export default async function Page(props: PageProps) {
  const { slugs: slug, lang } = await props.params;
  const landingPage = await fallbackLoadLandingPage(lang, slug);
  if (landingPage) return <LandingPage lang={lang} data={landingPage} />;

  // 都没有啊？404
  return notFound();
}

export async function generateMetadata(props: PageProps): Promise<Metadata> {
  const { slugs: slug, lang } = await props.params;
  const landingPage = await fallbackLoadLandingPage(lang, slug);

  // post 处理metadata，如标题
  const metadata: Metadata = {
    ...landingPage?.metadata,
    openGraph: {
      title: landingPage?.metadata.title,
      description: landingPage?.metadata.description,
      type: 'website',
      locale: lang,
    },
    twitter: {
      card: 'summary_large_image',
      site: '@bika_ai',
      title: landingPage?.metadata.title || '',
      description: landingPage?.metadata.description || '',
      creator: '@bika_ai',
    },
    alternates: {
      languages: await generateLanguageUrls(slug.join('/')),
    },
    robots: 'index, follow',
  };

  if (landingPage) {
    return postprocessPageMetadata(metadata, lang, false);
  }
  return {
    title: 'Cannot found page',
  };
}
