import { SITEMAP_HOSTNAME } from '@bika/contents/config/client/sitemap';
import { ConnectionString } from '@bika/server-orm';
import { getAppEnv } from 'sharelib/app-env';
import type { MetadataRoute } from 'next';
import { headers } from 'next/headers';

export default async function robots(): Promise<MetadataRoute.Robots> {
  const h = await headers();
  const cs = new ConnectionString(process.env.APP_HOSTNAME);
  if (getAppEnv() !== 'PRODUCTION' || h.get('host') !== cs.hostname) {
    // 全部禁止
    return {
      rules: {
        userAgent: '*',
        disallow: '/',
      },
    };
  }

  return {
    rules: [
      {
        userAgent: '*',
        disallow: ['/space', '/auth', '/api'],
        crawlDelay: 10, // 默认中价值页面抓取间隔
        allow: '/',
      },
      // 高价值内容区（产品详情/博客文章）
      {
        userAgent: '*',
        allow: ['/template/', '/blog/', '/help/'],
        crawlDelay: 2, // 高频抓取
      },
      // 针对Google的特殊配置
      {
        userAgent: 'Googlebot',
        allow: ['/template/', '/blog/', '/help/'],
        disallow: ['/auth/'],
        crawlDelay: 1, // Googlebot可更快抓取高价值内容
      },
    ],
    // In production, your generated sitemaps will be available at /.../sitemap/[id].xml. For example, /product/sitemap/1.xml.
    // In development, you can view the generated sitemap on /.../sitemap.xml/[id]. For example, /product/sitemap.xml/1. This difference is temporary and will follow the production format.
    // sitemap: [`${SITEMAP_HOSTNAME}/sitemap.xml`, ...localLandingPagesSiteMap, ...localBlogSiteMap, ...localNewsSiteMap],
    sitemap: [`${SITEMAP_HOSTNAME}/sitemap.xml`],
  };
}
