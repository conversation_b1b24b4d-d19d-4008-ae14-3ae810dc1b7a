'use client';

import { getStoryComponent } from '@bika/domains/story-components/config';
import React from 'react';
import type { FeatureType } from '@bika/types/website/bo';

const StorybookListComponent = ({ list }: { list: [string, string, string][] }) => {
  const componentList = list.map((page) => (
    <div key={page[1] + page[2]}>{getStoryComponent(page[1] as FeatureType, page[2])}</div>
  ));

  return <div>{componentList}</div>;
};

export { StorybookListComponent };
