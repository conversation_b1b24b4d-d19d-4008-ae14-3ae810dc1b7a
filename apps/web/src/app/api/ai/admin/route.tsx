import { AISO } from '@bika/domains/ai/server/ai-so';
import { UserSO } from '@bika/domains/user/server/user-so';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages, option } = await req.json();
  console.log('AI Option', option);

  if (option === 'reasoning-chat') {
    const { result: stream } = await AISO.streamText(
      {
        user: await UserSO.admin(),
        messages,
        // tools: {
        // 'intent-ui': IntentUITool,
        // },
      },
      {
        model: 'deepseek/deepseek-r1',
      },
    );
    return stream.toDataStreamResponse({
      sendReasoning: true,
    });
  }
  throw new Error(`Invalid option: ${option}`);

  // custom streaming logic
  // immediately start streaming (solves RAG issues with status, etc.)
  // return createDataStreamResponse({
  //   execute: async (dataStream) => {
  //     dataStream.write(`f:{"messageId":"${generateNanoID('msg-')}"}\n`);

  //     const text = 'Hello, world!';
  //     for (const c of text) {
  //       dataStream.write(`0:"${c}"\n`);
  //       await sleep(10);
  //     }
  //     // 写 data 也不 影响
  //     dataStream.writeData('initialized call');
  //     dataStream.writeData('call started');

  //     // 写tool , Tool Call Part
  //     const intentUIResolve = AIIntentUIResolveDTOSchema.parse({ type: 'CONFIRM', confirm: true });
  //     const toolCallId = 'call_mvklr6ky7k8gc7egrxfl89md';
  //     dataStream.write(
  //       `9:{"toolCallId":"${toolCallId}","toolName":"intent-ui","args":${JSON.stringify(intentUIResolve)}}\n`,
  //     );
  //     // tool result
  //     const intentUIVO = AIIntentUIVOSchemas.parse({ type: 'CONFIRM' } as AIIntentUIVO);
  //     dataStream.write(`a:{"toolCallId":"${toolCallId}","result":${JSON.stringify(intentUIVO)}}\n`);

  //     // 结束了
  //     dataStream.write(
  //       'e:{"finishReason":"stop","usage":{"promptTokens":null,"completionTokens":null},"isContinued":false}\n',
  //     );
  //     dataStream.write('d:{"finishReason":"stop","usage":{"promptTokens":null,"completionTokens":null}}\n');

  //     // const result = streamText({
  //     //   model: openai('gpt-4o'),
  //     //   messages,
  //     //   onChunk() {
  //     //     dataStream.writeMessageAnnotation({ chunk: '123' });
  //     //   },
  //     //   onFinish() {
  //     //     // message annotation:
  //     //     dataStream.writeMessageAnnotation({
  //     //       id: generateId(), // e.g. id from saved DB record
  //     //       other: 'information',
  //     //     });

  //     //     // call annotation:
  //     //     dataStream.writeData('call completed');
  //     //   },
  //     // });

  //     // result.mergeIntoDataStream(dataStream);
  //   },
  //   onError: (error) =>
  //     // Error messages are masked by default for security reasons.
  //     // If you want to expose the error message to the client, you can do so here:
  //     error instanceof Error ? error.message : String(error),
  // });
}
