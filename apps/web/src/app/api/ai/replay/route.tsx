import assert from 'assert';
import { AIChatSO } from '@bika/domains/ai/server';
import { DataStreamUtils } from '@bika/domains/ai/server/ai-chat/data-stream-utils';
import { AuthController } from '@bika/domains/auth/apis';
// import { createFetchRequestContext } from '@bika/server-orm/trpc';
import { createDataStreamResponse } from 'ai';
import { sleep } from 'sharelib/sleep';
import { headers } from 'next/headers';

// Allow streaming responses up to 180 seconds
export const maxDuration = 180;

/**
 * replay wizard
 *
 * @param req
 * @returns
 */
export async function POST(req: Request) {
  const { wizardId, messageIndex, forceLocale } = await req.json();
  const auth = await AuthController.getAuthByNextRequest(await headers());
  assert(auth);

  //   // 获取请求的用户
  const locale = forceLocale || auth.user.settings?.locale || 'en';

  //   const ctx = await createFetchRequestContext({ req, resHeaders: new Headers() });

  const wizardSO = await AIChatSO.get(wizardId);
  assert(wizardSO.user.id === auth.user.id, 'Wizard not found or not belong to user');

  return createDataStreamResponse({
    headers: {
      'Content-Type': 'text/event-stream',
    },
    execute: async (dataStream) => {
      const msgs = await wizardSO.getMessages();
      const msg = msgs[messageIndex];

      // 已经 written 过了，这里理解是只写 data 了
      const startGen = DataStreamUtils.messageStart();
      for await (const c of startGen) {
        dataStream.write(c);
      }

      const msgGenerator = DataStreamUtils.message(msg, locale);
      for await (const c of msgGenerator) {
        dataStream.write(c);
        await sleep(10);
      }

      const endGen = DataStreamUtils.messageEnd();
      for await (const c of endGen) {
        dataStream.write(c);
      }
    },
    onError: (error) =>
      // Error messages are masked by default for security reasons.
      // If you want to expose the error message to the client, you can do so here:
      error instanceof Error ? error.message : String(error),
  });
  // return stream;
}
