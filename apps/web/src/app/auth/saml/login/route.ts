import { Saml } from '@bika/domains/auth/server/integrations/saml/saml';
import { extractRequestUrl } from '@bika/server-orm/utils';
import { redirect } from 'next/navigation';
import { NextRequest } from 'next/server';

/**
 * Saml 登录入口, 有会话则直接登录, 没有会话则重定向到 IdP SSO URL
 */
export async function GET(req: NextRequest) {
  // 是否开启站点级别SAML登录
  // 未开启则直接返回提示信息
  // 开启则直接重定向到 IdP SSO URL
  const samlIdP = await Saml.getSiteIdP();
  if (!samlIdP) {
    redirect('/auth');
  } else {
    // 获取SP对象
    const baseUrl = extractRequestUrl(req.headers);
    let hostname: string;
    if (!process.env.APP_HOSTNAME) {
      hostname = baseUrl;
    } else {
      hostname = process.env.APP_HOSTNAME;
    }
    const searchParams = req.nextUrl.searchParams;
    const redirectPath = searchParams.get('redirect');
    const samlSP = Saml.getSiteSP(hostname);
    // 获取Idp授权登录地址
    const authorizeUrl = samlSP.getAuthorizeUrl(samlIdP);
    redirect(`${authorizeUrl}&redirect=${redirectPath}`);
  }
}
