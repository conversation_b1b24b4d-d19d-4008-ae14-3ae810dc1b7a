import { randomInt } from 'node:crypto';
import { Saml } from '@bika/domains/auth/server/integrations/saml/saml';
import { UserSO } from '@bika/domains/user/server';
import { SessionSO } from '@bika/server-orm/session';
import { extractRequestUrl, parseAttributesFromRequest } from '@bika/server-orm/utils';
import { LOCALE_HEADER } from '@bika/types/shared';
import { LocaleSchema, SamlSPNameIdMatchField } from '@bika/types/system';
import { NextRequest, NextResponse } from 'next/server';
import { getLocaleByHeaders } from 'sharelib/next-utils/get-locale-from-headers';

async function fetchUser(matchAgainst: SamlSPNameIdMatchField, nameID: string): Promise<UserSO | null> {
  if (matchAgainst === 'USER_ID') {
    return UserSO.findById(nameID);
  }
  if (matchAgainst === 'EMAIL_ADDRESS') {
    return UserSO.findByEmail(nameID);
  }
  // 一般不会走到这里, 如果走到这里, 说明你没有匹配对应的字段
  throw new Error(`Unsupported account matchAgainst field: ${matchAgainst}`);
}

/**
 * 接受IdP授权成功回调
 */
export async function POST(req: NextRequest) {
  const headers = req.headers;
  const locale = headers.get(LOCALE_HEADER) || getLocaleByHeaders(headers);
  const formData = await req.formData();
  // IdP授权成功断言签名
  const samlResponse = formData.get('SAMLResponse');
  const relayState = formData.get('RelayState');

  const samlIdP = await Saml.getSiteIdP();
  if (!samlIdP) {
    // 没配置Saml IdP, 不能解析
    return new Response('Saml IdP not configured', { status: 400 });
  }

  if (!samlResponse) {
    // 没有SAML响应
    return new Response('SAML Response not found', { status: 400 });
  }

  const baseUrl = extractRequestUrl(req.headers);

  try {
    let hostname: string;
    if (!process.env.APP_HOSTNAME) {
      hostname = baseUrl;
    } else {
      hostname = process.env.APP_HOSTNAME;
    }
    const samlSP = Saml.getSiteSP(hostname);
    // 解析SAML响应
    const samlAssertResponse = samlResponse as string;
    const nameID = await samlSP.handleSamlResponse(samlIdP, {
      SAMLResponse: samlAssertResponse,
      RelayState: relayState as string,
    });
    console.log(`SAML登录成功, nameID: ${nameID}`);
    // 获取IdP的账户匹配规则配置
    const idpAccountMatchRuleConfig = samlIdP.getAccountMatchRuleConfig();
    const { matchAgainst, notMatchUserAction } = idpAccountMatchRuleConfig;
    console.log(`matchAgainst: ${matchAgainst}, notMatchUserAction: ${notMatchUserAction}`);
    // IdP用户系统的唯一标识, 根据标识查找用户, 根据我们的规范, nameID必须是IdP用户系统的唯一标识
    let user = await fetchUser(matchAgainst, nameID);
    if (!user) {
      if (notMatchUserAction === 'CREATE_USER') {
        // 创建用户
        console.log('Create user');
        const email = matchAgainst === 'EMAIL_ADDRESS' ? nameID : undefined;
        const username = matchAgainst === 'EMAIL_ADDRESS' ? nameID : undefined;
        const name = email ? email.split('@')[0] : `user${randomInt(100000)}`;
        user = await UserSO.createUser(
          { username, email, name },
          { locale: locale ? LocaleSchema.safeParse(locale).data : undefined },
        );
      } else {
        // 拒绝
        console.log('Reject access');
        // return new Response('forbidden access', { status: 403 });
        const nextUrl = new URL('/auth/saml/login', baseUrl);
        // GET /space 重定向
        const response = NextResponse.redirect(nextUrl, { status: 303 });
        return response;
      }
    }
    // 解析成功, 生成用户会话
    const attributes = parseAttributesFromRequest(headers);
    // console.log(`attributes: ${JSON.stringify(attributes)}`);
    const session = await SessionSO.create(user.id, attributes);

    const nextUrl = new URL('/space', baseUrl);
    // GET /space 重定向
    const response = NextResponse.redirect(nextUrl, { status: 303 });
    response.headers.append('Set-Cookie', session.toCookie().serialize());
    return response;
  } catch (e) {
    console.error('Fail to Parse SAML Response sent from IdP', e);
    return new Response('Fail to Parse SAML Response sent from IdP', { status: 400 });
  }
}
