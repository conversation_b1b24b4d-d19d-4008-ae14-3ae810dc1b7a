// import { env } from "~/env";
// import { getLastMod } from "~/lib/sitemap";
// import { generateSitemaps as generateNewsSitemap } from '../sitemap/news-sitemap.xml/sitemap'
import { i18n } from '@bika/types/i18n/bo';
import { getReleaseDate } from 'sharelib/app-env';
import { generateSitemaps as generateBlogsSitemap } from '../sitemap/[lang]/blog-sitemap/[page]/route';
// import { generateSitemaps as generateHomeAndTemplatesSitemap } from '../sitemap/templates/sitemap';

// import { generateSitemaps as getEventSitemaps } from "../api/sitemaps/events/sitemap";
// import { generateSitemaps as getVenueSitemaps } from "../api/sitemaps/venues/sitemap";

// 不进行预编译
// export const dynamic = 'force-dynamic';

export const revalidate = 3600; // cache for 1 hour

function getLastMod() {
  return getReleaseDate().toISOString();
}

function getFileName(id?: number) {
  if (id == null) {
    return 'sitemap.xml';
  }
  /**
   * https://nextjs.org/docs/app/api-reference/file-conventions/metadata/sitemap#generating-multiple-sitemaps
   * In production, your generated sitemaps will be available at /.../sitemap/[id].xml. For example, /product/sitemap/1.xml.
   * In development, you can view the generated sitemap on /.../sitemap.xml/[id]. For example, /product/sitemap.xml/1. This difference is temporary and will follow the production format.
   * See the generateSitemaps API reference for more information.
   */
  // return getAppEnv() === 'LOCAL' ? `sitemap.xml/${id}` : `sitemap/${id}.xml`;
  return `sitemap/${id}.xml`;
}
function getLoc(path: string, id?: number) {
  return `${process.env.APP_HOSTNAME}/${path}-${getFileName(id)}`;
}
function getSitemap(path: string, id?: number) {
  return /* XML */ `<sitemap><loc>${getLoc(path, id)}</loc><lastmod>${getLastMod()}</lastmod></sitemap>`;
}
function getSitemaps(ids: { id: number }[], path: string) {
  return ids.map(({ id }) => getSitemap(path, id)).join('');
}
// function getSitemapsWithLangs(ids: { id: number }[], path: string, langs: string[]) {
//   return langs.map((lang) => getSitemaps(ids, path ? [lang, path].join('/') : lang)).join('');
// }

//   ${getSitemap('cities')}
//   ${getSitemapsWithLangs(await generateHomeAndTemplatesSitemap(), 'template', i18n.locales)}
// ${getSitemaps(await generateLangPagesSitemap(), 'sitemap/pages')}
// ${getSitemaps(await generateBlogsSitemap(), 'sitemap/blog')}
// ${getSitemaps(await generateHelpSitemap(), 'sitemap/help')}
// ${getSitemaps(await generateNewsSitemap(), 'sitemap/news')}
//  暂时不显示 ai news${getSitemap('sitemap/news')}
// 杂项网页，比如/api/docs等，放到help或pages
export async function GET() {
  const xml = /* XML */ `<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="/sitemap/sitemap-index-stylesheet.xsl"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${getSitemap('sitemap/engagement')}
  ${i18n.locales.map((lang) => getSitemap(`sitemap/${lang}/templates`))}
  ${i18n.locales.map((lang) => getSitemap(`sitemap/${lang}/help`))}
  ${await Promise.all(i18n.extendLocales.map(async (lang) => getSitemaps(await generateBlogsSitemap(lang), `sitemap/${lang}/blog`)))}
  ${i18n.locales.map((lang) => getSitemap(`sitemap/${lang}/pages`))}
  ${i18n.locales.map((lang) => getSitemap(`sitemap/${lang}/features`))}
  ${getSitemap('sitemap/content')}
</sitemapindex>
  `;

  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}
