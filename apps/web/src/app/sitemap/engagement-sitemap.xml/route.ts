// 读取aigc config策略，生成对应的sitemap
import assert from 'assert';
import { genSitemapXml } from '@bika/domains/website/server/sitemap-utils';
import { Bika } from 'bika.ai';
import type { MetadataRoute } from 'next';
import { getReleaseDate } from 'sharelib/app-env';

// 强制sitemap是动态的，不会被缓存
// export const dynamic = 'force-dynamic';

export async function sitemap(): Promise<MetadataRoute.Sitemap> {
  assert(Bika);
  // production bika.ai
  const bika = new Bika({
    apiKey: 'bktNG0bOSKIRaLkLNYATrVyG0w5TtR9G28k',
    baseURL: 'https://staging.bika.ai/api/openapi/bika',
  });
  const space = await bika.space.get('spcW24DmHJwAmMLlm62waIbz');
  const databaseNode = await space.nodes.get('date9WPz6vWv5dDZDCvpNnDz');
  const databaseResource = await databaseNode.asDatabase();
  const records = await databaseResource.records.list();

  const retSitemap: MetadataRoute.Sitemap = [];
  for (const rec of records.records) {
    console.log(rec.fields);
    retSitemap.push({
      url: rec.fields['网址'] as string,
      lastModified: getReleaseDate(),
      changeFrequency: 'yearly',
      priority: 99,
    });
  }
  return retSitemap;
}

export async function GET() {
  const xml = genSitemapXml(await sitemap());
  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}
