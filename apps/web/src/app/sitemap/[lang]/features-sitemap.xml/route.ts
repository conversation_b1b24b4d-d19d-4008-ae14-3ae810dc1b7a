import { SITEMAP_HOSTNAME } from '@bika/contents/config/client/sitemap';
import { type Locale } from '@bika/contents/i18n';
import { genSitemapXml } from '@bika/domains/website/server/sitemap-utils';
import { getReleaseDate } from 'sharelib/app-env';
import type { MetadataRoute } from 'next';

interface Props {
  params: Promise<{
    lang: Locale;
  }>;
}
// 强制sitemap是动态的，不会被缓存
// export const dynamic = 'force-dynamic';

// export async function getAllHelps(langFilter?: Locale): Promise<PageProps[]> {
//   let allPaths: PageProps[] = [];
//   const helpPaths = await LocalContentLoader.help.autoHelpsList(langFilter);
//   //  移除 README，仅用于排序 metadata 的没法打开页面
//   const removeREADMEhelpPaths = helpPaths.filter((page) => page.slugs[page.slugs.length - 1] !== 'README');

//   allPaths = [...allPaths, ...removeREADMEhelpPaths];

//   // const locales = langFilter ? [langFilter] : i18n.locales;

//   // TIPS: /reference/XXX 系列，只提供英文版，暂时
//   // for (const locale of locales) {
//   const autoHelps = AutoHelpGenSO.getMenuItems('en');
//   allPaths = [...allPaths, ...autoHelps];
//   // }

//   const filteredHelpPaths = allPaths.map((help) => {
//     const filterHelp = { ...help };
//     filterHelp.slugs = ['help', ...filterHelp.slugs];
//     return filterHelp;
//   });
//   return filteredHelpPaths;
// }

// export async function generateSitemaps() {
//   const helpPaths = await getAllHelps();

//   // 分割2个一组 输出 [{ page: 0 }, { page: 1 }, { page: 2 }, { page: 3 }]
//   const ret: { id: number }[] = [];
//   for (let i = 0; i < Math.ceil(helpPaths.length / SITEMAP_URL_SPLIT); i++) {
//     ret.push({ id: i });
//   }
//   return ret;
// }

// export async function sitemap({ id }: { id: number }): Promise<MetadataRoute.Sitemap> {
//   // 2个一组
//   const helpPaths = await await getAllHelps();

//   const start = id * SITEMAP_URL_SPLIT;
//   const end = start + SITEMAP_URL_SPLIT;
//   const data = helpPaths.slice(start, end);
//   const langPages: MetadataRoute.Sitemap = pagesPropsToSitemaps(data, true);

//   return langPages;
// }

export async function sitemapAll(langFilter?: Locale): Promise<MetadataRoute.Sitemap> {
  // help的网页
  const langPages: MetadataRoute.Sitemap = [
    // skillsets
    {
      url: `${SITEMAP_HOSTNAME}/${langFilter ? `${langFilter}/` : ''}skillset`,
      lastModified: getReleaseDate(),
      changeFrequency: 'weekly',
      priority: 1,
    },
    {
      url: `${SITEMAP_HOSTNAME}/${langFilter ? `${langFilter}/` : ''}ai-models`,
      lastModified: getReleaseDate(),
      changeFrequency: 'weekly',
      priority: 1,
    },
  ];

  return langPages;
}

export async function GET(_request: Request, props: Props) {
  const { lang } = await props.params;

  const xml = genSitemapXml(await sitemapAll(lang));
  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}
