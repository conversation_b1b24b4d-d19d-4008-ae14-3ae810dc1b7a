import { SITEMAP_HOSTNAME } from '@bika/contents/config/client/sitemap';
import { i18n } from '@bika/contents/i18n';
import { StoreTemplatesIterator } from '@bika/domains/store/server/store-templates-iterator';
import { genSitemapXml } from '@bika/domains/website/server/sitemap-utils';
import { type Locale } from '@bika/types/i18n/bo';
import { getReleaseDate } from 'sharelib/app-env';
import type { MetadataRoute } from 'next';

interface Props {
  params: Promise<{
    lang: Locale;
  }>;
}
// 强制sitemap是动态的，不会被缓存
// 这是因为templates是可以用户动态生成，影响静态页面多寡，要注意配置CDN规则
// export const dynamic = 'force-dynamic';
// export const revalidate = 3600; // cache for 1 hour

// export async function generateSitemaps() {
//   return [{ id: 0 }];
// }

/**
 * sitemap，包括：首页、模板中心
 *
 * @returns
 */
export async function sitemap(langFilter?: Locale): Promise<MetadataRoute.Sitemap> {
  const locales = langFilter ? [langFilter] : i18n.locales;

  const index: MetadataRoute.Sitemap = locales.map((locale) => ({
    url: `${SITEMAP_HOSTNAME}/${locale}`,
    lastModified: getReleaseDate(),
    changeFrequency: 'weekly',
    priority: 1,
  }));

  const sites: MetadataRoute.Sitemap = [];

  // 数据库以模板中心的为准
  for (const locale of locales) {
    const templatesCount = await StoreTemplatesIterator.getMax();
    const templatesIterator = new StoreTemplatesIterator(templatesCount);
    while (templatesIterator.hasNext()) {
      const nextTemplates = await templatesIterator.next();
      const localStr = locale === 'en' ? '' : `${locale}/`;
      for (const tpl of nextTemplates) {
        const urls = [
          {
            // 模板中心市场页
            url: `${SITEMAP_HOSTNAME}/${localStr}template/${tpl.templateId}`,
          },
          // TODO: 暂时都隐藏下面的页面，从 sitemap 上
          // {
          //   // 没有sidebar独立页
          //   url: `${SITEMAP_HOSTNAME}/${localStr}template-detail/${tpl.templateId}`,
          // },
          // {
          //   // 全屏架构图
          //   url: `${SITEMAP_HOSTNAME}/${localStr}template-workflow/${tpl.templateId}`,
          // },
          // {
          //   // 同上，都是流程图
          //   url: `${SITEMAP_HOSTNAME}/${localStr}template-architecture/${tpl.templateId}`,
          // },
          // {
          //   // 解决方案，落地页landing page形式
          //   url: `${SITEMAP_HOSTNAME}/${localStr}solution/${tpl.templateId}`,
          // },
          // {
          //   // blog 博客文章,README展示，架构图展示
          //   url: `${SITEMAP_HOSTNAME}/${localStr}blog/template-info/${tpl.templateId}`,
          // },
        ];

        // 收集/public/assets目录下，模板相关的图片进入sitemap
        // TODO:

        for (const url of urls) {
          sites.push({
            url: url.url,
            lastModified: getReleaseDate(),
            changeFrequency: 'weekly',
            priority: 1,
          });
        }
      }
    }
  }

  return [...index, ...sites];
}

export async function GET(_request: Request, props: Props) {
  const { lang } = await props.params;

  const xml = genSitemapXml(await sitemap(lang));
  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}
