// This example protects all routes including api/trpc routes
// Please edit this to allow other routes to be public as needed.
// See https://clerk.com/docs/references/nextjs/auth-middleware for more information about configuring your Middleware
 
// import { webcrypto } from 'node:crypto';

import { LOCALE_HEADER } from '@bika/types/shared';
import { getAppEnv } from 'sharelib/app-env';
import { CheckLicenseVO, CheckLicenseVOSchema } from '@bika/types/system/license';
import { NextResponse } from 'next/server';
import type { NextRequest, NextFetchEvent } from 'next/server';
import { getLocaleByHeaders } from 'sharelib/next-utils/get-locale-from-headers';
import { parseAppPageRouting } from './routing/app-page-routing';
import { getRoutingMode } from './routing/routing-mode';
import { parseSubDomainRouting } from './routing/sub-domains-routing';
import { parseWebsiteRouting } from './routing/website-routing';

/**
 * next middleware 只能使用edge api, 不能使用nodejs api
 * https://nextjs.org/docs/app/building-your-application/routing/middleware#runtime
 */
async function checkLicense(request: NextRequest): Promise<CheckLicenseVO | null> {
  // 请求验证License
  const url = `${request.nextUrl.origin.replace('https', 'http')}/api/trpc/system.checkLicense`;

  try {
    // 当前地址加上检查路径
    const response = await fetch(url, {
      method: 'GET',
    });
    const data = await response.json();
    // console.log('checkLicense response', data);
    // return data.result.data;
    const parsed = CheckLicenseVOSchema.safeParse(data.result.data);
    if (parsed.success) {
      return parsed.data;
    }
    return null;
  } catch (error) {
    console.error('checkLicense error', error, url);
    return null;
  }
}

export async function middleware(request: NextRequest, _event: NextFetchEvent) {
  // const isCNIP = await isFromCNIP();
  // if (isCNIP && !isFromCNHost()) {
  //   // 检查cookie 是否存在 CN_FORCE
  //   // https://github.com/vikadata/bika/issues/5391
  //   const cookie = request.headers.get('cookie');
  //   if (cookie?.includes('CN_FORCE')) {
  //     // 如果存在 跳转到 bikaai.cn 域名 + 访问路径
  //     return NextResponse.redirect(`https://bikaai.cn${request.nextUrl.pathname}${request.nextUrl.search}`, {
  //       status: 302,
  //     });
  //   }
  // }

  // 检查license
  if (getAppEnv() !== 'LOCAL') {
    const checkLicenseResult = await checkLicense(request);
    if (!checkLicenseResult?.hasLicense) {
      return NextResponse.rewrite(new URL('/license/activate', request.url));
    }
    if (checkLicenseResult.expired) {
      // license 过期了
      return NextResponse.redirect(new URL('/license/activate?expired', request.url));
    }
  }

  // 获取路由中转的模式
  const routingMode = getRoutingMode(request);
  const browserLocale = getLocaleByHeaders(request.headers) as string;
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-bika-home', routingMode === 'WEB_PAGE' ? '1' : '0');
  requestHeaders.set(LOCALE_HEADER, browserLocale); // 默认先记录浏览器locale，下面各种routing过程，如网站路由，会覆盖这个值

  // 子域名判断和处理
  const subDomainAction = parseSubDomainRouting(request, requestHeaders, browserLocale);
  if (subDomainAction) {
    return subDomainAction;
  }

  /**
   * 原本应该在这里完成对用户的一些操作，比如检查用户是否登录，跳转到对应的语言
   * 由于 nextjs 的设计导致这里无法做任何事情
   * @see https://www.reddit.com/r/nextjs/comments/18q7gyq/lucia_auth_in_middleware/
   * 并且由于在 Server Component 中无法获取到URL 所以只有一个选择 从 header 中传递 URL
   * @see https://github.com/vercel/next.js/issues/43704
   * nextjs 有自己的设计 但是对应用开发来说 这个设计是不合理的 从上面的讨论可以看出
   */
  // request.headers.set('x-nextjs-url', request.nextUrl.toString());

  if (routingMode === 'WEB_PAGE') {
    // 网页路由处理，网页通常就是”语言“开头的，如/en/XXXX, /zh-CN/XXXX
    const websiteRouting = await parseWebsiteRouting(request, requestHeaders, browserLocale);
    // 有特殊处理，就直接重定向
    if (websiteRouting) {
      return websiteRouting;
    }
  } else {
    // APP_PAGE
    const appPageRouting = parseAppPageRouting(request, requestHeaders, browserLocale);
    // 有特殊处理，就直接重定向
    if (appPageRouting) {
      return appPageRouting;
    }
  }

  // 正常的路由处理，交到 file-based router

  const ret = NextResponse.next({
    request: {
      // New request headers
      headers: requestHeaders,
    },
  });

  requestHeaders.forEach((value: string, key: string) => {
    ret.headers.set(key, value);
  });

  return ret;
}

export const config = {
  // r -> referral,  auth -> login
  matcher: [
    '/((?!api.*|r/|u/|roadmap|404|400|500|billing|auth|login|logout|storybook-dev-page|firebase-messaging-sw.js|assets|llms.txt|llms-full.txt|sitemap.xml|license|sitemap|robots.txt|_vercel|_next/static|_next/image|favicon.ico).*)',
  ],
};

// 这段代码定义了一个配置对象，用于指定中间件应该匹配哪些路由。
// matcher 数组包含一个正则表达式模式，用于匹配除了特定路径之外的所有路由。
// 具体来说，它会匹配所有路径，除了以下这些：
// - 以 'api' 开头的路径
// - 'r/' 路径（可能是用于引荐）
// - 'billing', 'auth', 'login', 'logout' 路径
// - 'firebase-messaging-sw.js' 文件
// - 'assets' 目录
// - 'sitemap.xml', 'license', 'sitemap', 'robots.txt' 文件
// - '_vercel' 和 '_next' 相关的路径（可能是与Next.js或Vercel部署相关）
// - 'favicon.ico' 文件
// 这样设置可以确保中间件只在需要的路由上运行，提高效率并避免不必要的处理。
