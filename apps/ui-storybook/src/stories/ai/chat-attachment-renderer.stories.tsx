import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';
import { Box, Stack } from '@mui/joy';
import { MessageAttachmentRenderer } from '@bika/domains/ai/client/chat/attachment/message-attachment-renderer';
import type { AttachmentData } from '@bika/domains/ai/client/chat/attachment/message-attachment-renderer';

// Mock data for different attachment types
const mockImageAttachment: AttachmentData = {
  name: 'sample-image.jpg',
  contentType: 'image/jpeg',
  url: 'http://dev.bika.ai/assets/template-photo/3day-outreach-emails/banner-en.png',
};

const mockPdfAttachment: AttachmentData = {
  name: 'document.pdf',
  contentType: 'application/pdf',
  url: 'https://s1-dev.bika.ai/ai/qq1rpP4nf7E8cJYCbMRh4.pdf?id=attbcuocY9wqbStIuFQAcEvi',
};

const mockWordAttachment: AttachmentData = {
  name: 'report.docx',
  contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  url: 'https://example.com/report.docx',
};

const mockExcelAttachment: AttachmentData = {
  name: 'spreadsheet.xlsx',
  contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  url: 'https://example.com/spreadsheet.xlsx',
};

const mockPowerPointAttachment: AttachmentData = {
  name: 'presentation.pptx',
  contentType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  url: 'https://example.com/presentation.pptx',
};

const mockZipAttachment: AttachmentData = {
  name: 'archive.zip',
  contentType: 'application/zip',
  url: 'https://example.com/archive.zip',
};

const mockTextAttachment: AttachmentData = {
  name: 'readme.txt',
  contentType: 'text/plain',
  url: 'https://example.com/readme.txt',
};

const mockVideoAttachment: AttachmentData = {
  name: 'video.mp4',
  contentType: 'video/mp4',
  url: 'https://example.com/video.mp4',
};

const mockAudioAttachment: AttachmentData = {
  name: 'audio.mp3',
  contentType: 'audio/mpeg',
  url: 'https://example.com/audio.mp3',
};

const mockUnknownAttachment: AttachmentData = {
  name: 'unknown-file.xyz',
  contentType: 'application/octet-stream',
  url: 'https://example.com/unknown-file.xyz',
};

// Mock attachment with very long filename
const mockLongNameAttachment: AttachmentData = {
  name: 'this-is-a-very-long-filename-that-should-be-truncated-with-ellipsis.pdf',
  contentType: 'application/pdf',
  url: 'https://example.com/long-filename.pdf',
};

// Mock attachment without name
const mockNoNameAttachment: AttachmentData = {
  contentType: 'image/png',
  url: 'https://picsum.photos/200/200?random=2',
};

// Mock attachment without contentType
const mockNoContentTypeAttachment: AttachmentData = {
  name: 'mystery-file',
  url: 'https://example.com/mystery-file',
};

const meta: Meta<typeof MessageAttachmentRenderer> = {
  title: '@bika/ai/MessageAttachmentRenderer',
  component: MessageAttachmentRenderer,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
ChatAttachmentRenderer is a component that displays file attachments in AI chat conversations. 
It supports various file types including images, documents, PDFs, and media files with appropriate 
icons and previews. The component handles different display modes for images (showing actual image) 
and other file types (showing file icon with filename).

**Features:**
- Image preview for supported image formats
- File type icons for documents, PDFs, media files, etc.
- Hover effects and click interactions
- Customizable dimensions and styling
- Responsive design with proper text truncation
- Support for various file formats (PDF, Word, Excel, PowerPoint, ZIP, etc.)
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    attachment: {
      description: 'Attachment data containing name, contentType, and url',
      control: { type: 'object' },
    },
    // width: {
    //   description: 'Width of the attachment renderer',
    //   control: { type: 'number', min: 32, max: 200, step: 8 },
    // },
    // height: {
    //   description: 'Height of the attachment renderer',
    //   control: { type: 'number', min: 32, max: 200, step: 8 },
    // },
    // borderRadius: {
    //   description: 'Border radius for the attachment container',
    //   control: { type: 'number', min: 0, max: 20, step: 2 },
    // },
    // marginBottom: {
    //   description: 'Bottom margin for the attachment',
    //   control: { type: 'number', min: 0, max: 20, step: 1 },
    // },
    // alignSelf: {
    //   description: 'Flex alignment for the attachment',
    //   control: { type: 'select' },
    //   options: ['flex-start', 'flex-end', 'center', 'stretch'],
    // },
    // onClick: {
    //   description: 'Click handler for the attachment',
    //   action: 'clicked',
    // },
    // onHover: {
    //   description: 'Hover handler for the attachment',
    //   action: 'hovered',
    // },
    className: {
      description: 'Additional CSS class name',
      control: { type: 'text' },
    },
  },
  args: {
    attachment: mockImageAttachment,
    // width: 64,
    // height: 64,
    // borderRadius: 4,
    // marginBottom: 1,
    // alignSelf: 'flex-end',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic stories for different file types
export const ImageAttachment: Story = {
  args: {
    attachment: mockImageAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Image attachment showing the actual image with hover effects.',
      },
    },
  },
};

export const PdfAttachment: Story = {
  args: {
    attachment: mockPdfAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'PDF attachment showing PDF icon with filename.',
      },
    },
  },
};

export const WordAttachment: Story = {
  args: {
    attachment: mockWordAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Microsoft Word document attachment.',
      },
    },
  },
};

export const ExcelAttachment: Story = {
  args: {
    attachment: mockExcelAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Microsoft Excel spreadsheet attachment.',
      },
    },
  },
};

export const PowerPointAttachment: Story = {
  args: {
    attachment: mockPowerPointAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Microsoft PowerPoint presentation attachment.',
      },
    },
  },
};

export const ZipAttachment: Story = {
  args: {
    attachment: mockZipAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'ZIP archive attachment.',
      },
    },
  },
};

export const TextAttachment: Story = {
  args: {
    attachment: mockTextAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Plain text file attachment.',
      },
    },
  },
};

export const VideoAttachment: Story = {
  args: {
    attachment: mockVideoAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Video file attachment showing media icon.',
      },
    },
  },
};

export const AudioAttachment: Story = {
  args: {
    attachment: mockAudioAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Audio file attachment showing media icon.',
      },
    },
  },
};

export const UnknownAttachment: Story = {
  args: {
    attachment: mockUnknownAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Unknown file type showing generic file icon.',
      },
    },
  },
};

// Edge cases and special states
export const LongFilename: Story = {
  args: {
    attachment: mockLongNameAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment with very long filename that gets truncated with ellipsis.',
      },
    },
  },
};

export const NoFilename: Story = {
  args: {
    attachment: mockNoNameAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment without filename, showing default "attachment" name.',
      },
    },
  },
};

export const NoContentType: Story = {
  args: {
    attachment: mockNoContentTypeAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment without content type, treated as generic file.',
      },
    },
  },
};

// Size variations
export const SmallSize: Story = {
  args: {
    attachment: mockPdfAttachment,
    // width: 48,
    // height: 48,
  },
  parameters: {
    docs: {
      description: {
        story: 'Small sized attachment renderer (48x48px).',
      },
    },
  },
};

export const LargeSize: Story = {
  args: {
    attachment: mockImageAttachment,
    // width: 128,
    // height: 128,
  },
  parameters: {
    docs: {
      description: {
        story: 'Large sized attachment renderer (128x128px).',
      },
    },
  },
};

export const CustomDimensions: Story = {
  args: {
    attachment: mockImageAttachment,
    // width: 100,
    // height: 60,
    // borderRadius: 8,
  },
  parameters: {
    docs: {
      description: {
        story: 'Custom dimensions with rectangular aspect ratio and custom border radius.',
      },
    },
  },
};

// Alignment variations
export const AlignLeft: Story = {
  args: {
    attachment: mockImageAttachment,
    // alignSelf: 'flex-start',
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment aligned to the left (flex-start).',
      },
    },
  },
};

export const AlignCenter: Story = {
  args: {
    attachment: mockImageAttachment,
    // alignSelf: 'center',
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment aligned to the center.',
      },
    },
  },
};

export const AlignStretch: Story = {
  args: {
    attachment: mockImageAttachment,
    // alignSelf: 'stretch',
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment stretched to fill container.',
      },
    },
  },
};

// Interactive states
export const WithClickHandler: Story = {
  args: {
    attachment: mockPdfAttachment,
    // onClick: (attachment) => {
    //   alert(`Clicked on: ${attachment.name}`);
    // },
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment with click handler - shows hover effects and cursor pointer.',
      },
    },
  },
};

export const WithHoverHandler: Story = {
  args: {
    attachment: mockImageAttachment,
    // onHover: (attachment) => {
    //   console.log(`Hovered on: ${attachment.name}`);
    // },
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment with hover handler for additional interactions.',
      },
    },
  },
};

// Multiple attachments showcase
function MultipleAttachmentsExample() {
  return (
    <Stack direction="row" spacing={2} sx={{ p: 2, flexWrap: 'wrap' }}>
      <MessageAttachmentRenderer attachment={mockImageAttachment} />
      <MessageAttachmentRenderer attachment={mockPdfAttachment} />
      <MessageAttachmentRenderer attachment={mockWordAttachment} />
      <MessageAttachmentRenderer attachment={mockExcelAttachment} />
      <MessageAttachmentRenderer attachment={mockPowerPointAttachment} />
      <MessageAttachmentRenderer attachment={mockZipAttachment} />
      <MessageAttachmentRenderer attachment={mockTextAttachment} />
      <MessageAttachmentRenderer attachment={mockVideoAttachment} />
      <MessageAttachmentRenderer attachment={mockAudioAttachment} />
      <MessageAttachmentRenderer attachment={mockUnknownAttachment} />
    </Stack>
  );
}

export const AllFileTypes: Story = {
  render: () => <MultipleAttachmentsExample />,
  parameters: {
    docs: {
      description: {
        story: 'Showcase of all supported file types in a single view.',
      },
    },
  },
};

// Chat conversation layout example
function ChatLayoutExample() {
  return (
    <Box sx={{ width: 400, p: 2 }}>
      <Stack spacing={2}>
        {/* User message with attachments */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
          <Box sx={{ mb: 1, p: 2, bgcolor: 'primary.100', borderRadius: 2, maxWidth: '80%' }}>
            Here are the files you requested:
          </Box>
          <Stack direction="row" spacing={1} sx={{ alignItems: 'flex-end' }}>
            <MessageAttachmentRenderer
              attachment={mockImageAttachment}
              // alignSelf="flex-end"
              // onClick={(attachment) => console.log('Open image:', attachment.name)}
            />
            <MessageAttachmentRenderer
              attachment={mockPdfAttachment}
              // alignSelf="flex-end"
              // onClick={(attachment) => console.log('Open PDF:', attachment.name)}
            />
          </Stack>
        </Box>

        {/* AI response */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
          <Box sx={{ p: 2, bgcolor: 'neutral.100', borderRadius: 2, maxWidth: '80%' }}>
            I can see the image and PDF you shared. Let me analyze them for you.
          </Box>
        </Box>
      </Stack>
    </Box>
  );
}

export const ChatLayout: Story = {
  render: () => <ChatLayoutExample />,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Example of how attachments appear in a chat conversation layout.',
      },
    },
  },
};

// Different image formats
const mockPngAttachment: AttachmentData = {
  name: 'image.png',
  contentType: 'image/png',
  url: 'https://picsum.photos/200/200?random=3',
};

const mockGifAttachment: AttachmentData = {
  name: 'animation.gif',
  contentType: 'image/gif',
  url: 'https://picsum.photos/200/200?random=4',
};

const mockWebpAttachment: AttachmentData = {
  name: 'modern-image.webp',
  contentType: 'image/webp',
  url: 'https://picsum.photos/200/200?random=5',
};

const mockSvgAttachment: AttachmentData = {
  name: 'vector-image.svg',
  contentType: 'image/svg+xml',
  url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5TVkc8L3RleHQ+PC9zdmc+',
};

export const PngImage: Story = {
  args: {
    attachment: mockPngAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'PNG image attachment.',
      },
    },
  },
};

export const GifImage: Story = {
  args: {
    attachment: mockGifAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'GIF image attachment.',
      },
    },
  },
};

export const WebpImage: Story = {
  args: {
    attachment: mockWebpAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'WebP image attachment.',
      },
    },
  },
};

export const SvgImage: Story = {
  args: {
    attachment: mockSvgAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'SVG image attachment.',
      },
    },
  },
};

// String dimensions
export const StringDimensions: Story = {
  args: {
    attachment: mockImageAttachment,
    // width: '80px',
    // height: '80px',
    // borderRadius: '12px',
  },
  parameters: {
    docs: {
      description: {
        story: 'Attachment with string-based dimensions and border radius.',
      },
    },
  },
};

// Error states
const mockBrokenImageAttachment: AttachmentData = {
  name: 'broken-image.jpg',
  contentType: 'image/jpeg',
  url: 'https://invalid-url-that-will-fail.com/broken-image.jpg',
};

export const BrokenImageUrl: Story = {
  args: {
    attachment: mockBrokenImageAttachment,
  },
  parameters: {
    docs: {
      description: {
        story: 'Image attachment with broken URL - browser will show broken image icon.',
      },
    },
  },
};

// Comprehensive showcase with different configurations
function ComprehensiveShowcase() {
  return (
    <Box sx={{ p: 3, maxWidth: 800 }}>
      <Stack spacing={4}>
        {/* Size variations */}
        <Box>
          <h3>Size Variations</h3>
          <Stack direction="row" spacing={2} sx={{ alignItems: 'flex-end' }}>
            <MessageAttachmentRenderer attachment={mockImageAttachment} />
            {/* width={32} height={32} /> */}
            <MessageAttachmentRenderer attachment={mockImageAttachment} />
            {/* width={48} height={48} /> */}
            <MessageAttachmentRenderer attachment={mockImageAttachment} />
            {/* width={64} height={64} /> */}
            <MessageAttachmentRenderer attachment={mockImageAttachment} />
            {/* width={96} height={96} /> */}
            <MessageAttachmentRenderer attachment={mockImageAttachment} />
            {/* width={128} height={128} /> */}
          </Stack>
        </Box>

        {/* File types grid */}
        <Box>
          <h3>File Types</h3>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: 2 }}>
            <MessageAttachmentRenderer attachment={mockImageAttachment} />
            <MessageAttachmentRenderer attachment={mockPdfAttachment} />
            <MessageAttachmentRenderer attachment={mockWordAttachment} />
            <MessageAttachmentRenderer attachment={mockExcelAttachment} />
            <MessageAttachmentRenderer attachment={mockPowerPointAttachment} />
            <MessageAttachmentRenderer attachment={mockZipAttachment} />
            <MessageAttachmentRenderer attachment={mockTextAttachment} />
            <MessageAttachmentRenderer attachment={mockVideoAttachment} />
            <MessageAttachmentRenderer attachment={mockAudioAttachment} />
            <MessageAttachmentRenderer attachment={mockUnknownAttachment} />
          </Box>
        </Box>

        {/* Interactive examples */}
        <Box>
          <h3>Interactive Examples</h3>
          <Stack direction="row" spacing={2}>
            <MessageAttachmentRenderer
              attachment={mockPdfAttachment}
              // onClick={(attachment) => alert(`Opening: ${attachment.name}`)}
            />
            <MessageAttachmentRenderer
              attachment={mockImageAttachment}
              // onClick={(attachment) => alert(`Viewing: ${attachment.name}`)}
              // onHover={(attachment) => console.log(`Hovering: ${attachment.name}`)}
            />
          </Stack>
        </Box>

        {/* Edge cases */}
        <Box>
          <h3>Edge Cases</h3>
          <Stack direction="row" spacing={2}>
            <MessageAttachmentRenderer attachment={mockLongNameAttachment} />
            <MessageAttachmentRenderer attachment={mockNoNameAttachment} />
            <MessageAttachmentRenderer attachment={mockNoContentTypeAttachment} />
          </Stack>
        </Box>
      </Stack>
    </Box>
  );
}

export const ComprehensiveDemo: Story = {
  render: () => <ComprehensiveShowcase />,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Comprehensive demonstration of all ChatAttachmentRenderer features and variations.',
      },
    },
  },
};
