'use client';

import { usePathname } from 'next/navigation';
import { Stack } from '@bika/ui/layouts';
// import { getAppEnv } from 'sharelib/app-env';
import Link from 'next/link';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
interface Props {
  children: React.ReactNode;
}

const sidebarItems = [
  {
    title: 'Developer',
    href: '/me/developer',
  },
  // {
  //   title: 'Actions',
  //   href: '/me/actions',
  // },
  // {
  //   title: 'Integrations',
  //   href: '/me/integrations',
  // },
];

// if (getAppEnv() !== 'PRODUCTION') {
//   sidebarItems.push(
//     {
//       title: 'Triggers(dev)',
//       href: '/me/triggers',
//     },
//     {
//       title: 'Widgets(dev)',
//       href: '/me/widgets',
//     },
//   );
// }
// sidebarItems.push({
//   title: 'My MCP Servers',
//   href: '/me/packages',
// });
// sidebarItems.push({
//   title: 'My Clients',
//   href: '/me/package-clients',
// });
sidebarItems.push({
  title: 'Favorites',
  href: '/me/favorites',
});

export default function MeSidebarLayout(props: Props) {
  const pathname = usePathname();
  const { isMobile } = useUIFrameworkContext();

  if (isMobile) {
    return props.children;
  }

  return (
    <Stack sx={{ flex: 1, background: 'var(--bg-controls)', display: 'flex', flexDirection: 'row', height: '100%' }}>
      <Stack sx={{ width: 280, borderRight: '1px solid var(--border-default)', p: 2 }}>
        {sidebarItems.map((item, idx) => (
          <Stack
            key={idx}
            sx={{
              px: 1,
              height: '32px',
              lineHeight: '32px',
              borderRadius: '4px',
              background: pathname === item.href ? 'var(--selected)' : 'transparent',
              color: pathname === item.href ? 'var(--brand)' : null,
              '&:hover': {
                background: 'var(--selected)',
              },
            }}
          >
            <Link href={item.href}>{item.title}</Link>
          </Stack>
        ))}
      </Stack>
      <Stack sx={{ flex: 1 }}>{props.children}</Stack>
    </Stack>
  );
}
