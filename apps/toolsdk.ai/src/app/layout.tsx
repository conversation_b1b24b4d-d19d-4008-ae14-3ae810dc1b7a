import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import type { Metada<PERSON> } from 'next';
import { cookies, headers } from 'next/headers';
import { LocaleProvider } from '@bika/contents/i18n/context';
import { getDictionary } from '@bika/contents/i18n/translate';
import type { Locale } from '@bika/types/i18n/bo';
import { NextUIFrameworkProvider } from '@bika/ui/framework/next-framework-provier';
import { AppProvider } from '@toolsdk.ai/domain/client/context/app';
import { Footer } from '@toolsdk.ai/domain/client/webpages/footer/index';
import { Header } from '@toolsdk.ai/domain/client/webpages/header/index';
// import localFont from 'next/font/local';
import { MockGlobalContextProvider } from '@toolsdk.ai/domain/client/mock-global-context-provider';
import '@bika/domains/shared/client/styles/globals.css';
import '@bika/domains/shared/client/styles/markdown.css';
import { GoogleAnalytics } from '@next/third-parties/google';
// import { getLocaleByHeaders } from 'sharelib/next-utils/get-locale-from-headers';
import { getServerToolSDKDictionary } from '@bika/contents/i18n/server';
import { RootHtml } from 'basenext/website/root-html.tsx';
import { ThemeMode, ThemeStyle } from '@bika/types/website/bo';
import { postprocessPageMetadata } from 'sharelib/next-utils/postprocess-metadata';
import { appHostName } from 'sharelib/app-env';

export const dynamic = 'force-dynamic';

// import { Footer } from '@bika/domains/website/client/footer';

// const geistSans = localFont({
//   src: './fonts/GeistVF.woff',
//   variable: '--font-geist-sans',
//   weight: '100 900',
// });
// const geistMono = localFont({
//   src: './fonts/GeistMonoVF.woff',
//   variable: '--font-geist-mono',
//   weight: '100 900',
// });

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const h = await headers();
  // const locale = getLocaleByHeaders(h) as Locale;
  const locale = 'en' as Locale;
  const dictionary = await getDictionary(locale, ['formapp']);
  const cookie = await cookies();
  const cookieThemeMode: ThemeMode = (cookie.get('app-theme-mode')?.value || 'dark') as ThemeMode;
  const cookieThemeStyle = cookie.get('app-theme-style')?.value || 'default';
  return (
    <RootHtml<Locale, ThemeStyle> locale={locale} themeMode={cookieThemeMode} themeStyle={cookieThemeStyle}>
      <body
      // className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextUIFrameworkProvider
          mode={'RSC'}
          initServerData={{
            hostname: process.env.APP_HOSTNAME!,
            storageHostname: process.env.STORAGE_PUBLIC_URL!,
            headers: h,
          }}
        >
          <AppProvider initData={{ themeMode: 'system' }}>
            <LocaleProvider defaultLocale={locale} defaultDictionary={dictionary} extensions={['formapp']}>
              <ClerkProvider>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    minHeight: '100vh',
                  }}
                >
                  <Header />
                  <div style={{ flex: 1, display: 'grid' }}>
                    <MockGlobalContextProvider>{children}</MockGlobalContextProvider>
                  </div>
                  <Footer />
                </div>
              </ClerkProvider>
            </LocaleProvider>
          </AppProvider>
        </NextUIFrameworkProvider>
      </body>
      <GoogleAnalytics gaId="G-0QS6NZ63J2" />
    </RootHtml>
  );
}

export async function generateMetadata() {
  const dict = await getServerToolSDKDictionary('en');
  const metadata: Metadata = {
    title: {
      template: `%s | ${dict.toolsdk.metadata.title}`,
      default: dict.toolsdk.metadata.title, // 'ToolSDK.ai | MCP Servers & Tools Hosting and SDK ',
    },
    description: dict.toolsdk.metadata.description,
    keywords: dict.toolsdk.metadata.keywords,
    metadataBase: process.env.APP_HOSTNAME ? new URL(appHostName()) : undefined,
    alternates: {
      canonical: './', // 使用相对路径，Next.js 会自动拼接 metadataBase
    },
  };
  return postprocessPageMetadata(metadata);
}
