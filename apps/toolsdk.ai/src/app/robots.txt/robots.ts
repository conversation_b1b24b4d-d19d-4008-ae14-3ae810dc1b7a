import { getAppEnv } from 'sharelib/app-env';
import type { MetadataRoute } from 'next';

export default async function robots(): Promise<MetadataRoute.Robots> {
  if (getAppEnv() !== 'PRODUCTION') {
    return {
      rules: {
        userAgent: '*',
        disallow: '/',
      },
    };
  }

  return {
    rules: {
      userAgent: '*',
      allow: '/',
    },
    sitemap: [`${process.env.APP_HOSTNAME}/sitemap.xml`],
  };
}
