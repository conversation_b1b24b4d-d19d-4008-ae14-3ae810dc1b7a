'use client';

import { useSearchParams } from 'next/navigation';
import React from 'react';
import { Stack } from '@bika/ui/layouts';
import { TextH1Component } from '@bika/ui/text-components';
// import { SubmitNewActionForm } from '@toolsdk.ai/domain/client/submit/submit-new-action-form';
// import { SubmitNewTriggerForm } from '@toolsdk.ai/domain/client/submit/submit-new-trigger-form';
import { SubmitNewIntegrationForm } from '@toolsdk.ai/domain/client/submit/submit-new-integration-form';
// import { SubmitNewWidgetForm } from '@toolsdk.ai/domain/client/submit/submit-new-widget-form';
import { getAppEnv } from 'sharelib/app-env';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { useUIFrameworkContext } from '@bika/ui/framework/context';

export default function SubmitPage() {
  const searchParams = useSearchParams();
  const { isMobile } = useUIFrameworkContext();
  const submitTypeSearchParam: string | null = searchParams.get('submitType');
  const [submitType, setSubmitType] = React.useState(() => submitTypeSearchParam || 'TOOL');
  const appEnv = getAppEnv();

  const options = [
    { label: 'New Action', value: 'TOOL', description: 'Submit a new action' },
    { label: 'New Integration', value: 'CONFIGURATION', description: 'Submit a new integration' },
  ];

  if (appEnv !== 'PRODUCTION') {
    options.push(
      { label: 'New Trigger(dev)', value: 'trigger', description: 'Submit a new trigger' },
      { label: 'New Widget(dev)', value: 'widget', description: 'Submit a new widget' },
      { label: 'New Resource(dev)', value: 'resource', description: 'Submit a new resource' },
    );
  }
  return (
    <Stack sx={isMobile ? { width: '100%', px: 2 } : { width: 1200, margin: '0 auto' }}>
      <TextH1Component>Submit a new:</TextH1Component>
      <Stack sx={{ mb: 2 }}>
        <SelectInput
          label={'Submit Type'}
          value={submitType}
          onChange={(newVal) => {
            setSubmitType(newVal!);
          }}
          options={options}
        />
      </Stack>

      {/* {submitType === 'TOOL' && <SubmitNewActionForm />} */}
      {submitType === 'CONFIGURATION' && <SubmitNewIntegrationForm />}
      {/* {submitType === 'trigger' && <SubmitNewTriggerForm />}
      {submitType === 'widget' && <SubmitNewWidgetForm />} */}
      {submitType === 'resource' && <>Resource New</>}
    </Stack>
  );
}
