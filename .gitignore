# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# editor
# .vscode
.idea
.cache

# live-plugin-manager
plugin_packages

.turbo

# dependencies
node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
dist

!/projects/bika/packages/bika-sdk-js/dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# local database (postgre + mongo) persistent
.data
/packages/web/.next/
apps/web/playwright-report/index.html
apps/web/test-results
scripts/__pycache__
.envrc

/airbyte

/bika-content
/awesome-mcp-registry

/contents/docs/blog-aigc
.prompts
.cursorignore

.venv

.cursor
.kiro

apps/editor-vite/src/App.d.ts
apps/editor-vite/src/main.d.ts
apps/editor-vite/src/App.js
apps/editor-vite/src/main.js
apps/editor-vite/src/pages/_routes.d.ts
apps/editor-vite/src/pages/_routes.js
apps/editor-vite/src/pages/editor.d.ts
apps/editor-vite/src/pages/editor.js
apps/editor-vite/vite.config.d.ts
apps/editor-vite/vite.config.js

logs

venv

# MCP Client Test
mcp-server.log
js-sandbox.log
mcp-log.log
terminal-security.json
terminal-tool.json
docs-config.json
.clinerules-*


