import assert from 'assert';
import type React from 'react';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { WidgetRenderVO } from '@bika/types/dashboard/vo';
import { LinearProgress } from '@bika/ui/progress';

interface Props {
  widget: WidgetRenderVO;
  locale: ILocaleContext;
}

function toPercent(used: number, total: number): number {
  if (total === -1) return 5;
  return Math.min((used / total) * 100, 100);
}

export function ProgressBarWidgetVORenderer({ widget, locale }: Props) {
  const { t, i } = locale;
  assert(widget.type === 'PROGRESS_BAR', 'Widget type must be progress-bar-widget');

  function customFormatTotal(value: number): string {
    return value === -1 || value === -2 ? t.pricing.features.unlimited : value.toString();
  }

  const formatUsedValue = widget?.formatter ? widget.formatter(widget.used) : widget.used;
  const formatTotalValue = widget?.formatter ? widget.formatter(widget.total) : customFormatTotal(widget.total);
  const formatRemainValue = widget?.formatter
    ? widget.formatter(widget.total - widget.used)
    : widget.total - widget.used;

  return (
    <div className={'border-[1px] border-[--border-default] rounded-[8px] py-[10px] px-4'}>
      {/* 第一部分 */}
      <div className={'flex items-center justify-between mb-[6px]'}>
        <div className={'text-b2'}>{i(widget.name)}</div>
        {widget.buttons?.map((btn, index) => (
          // biome-ignore lint/a11y/useKeyWithClickEvents: <explanation>
          <div key={index} className={'text-b4 text-[--brand] cursor-pointer'} onClick={btn.onClick}>
            {btn.name}
          </div>
        ))}
      </div>
      {/* 进度条 */}
      <div className={'mb-2'}>
        <div className={'text-[12px] mb-1'}>
          {widget.total === -1 ? t.pricing.features.unlimited : `${formatUsedValue} / ${formatTotalValue}`}
        </div>
        <div>
          <LinearProgress
            determinate
            value={toPercent(widget.used, widget.total)}
            sx={{
              color: `var(${widget.color})`,
              backgroundColor: 'var(--bg-controls)',
            }}
          />
        </div>
      </div>
      {/* 第三部分 */}
      <div className={'flex items-center justify-between'}>
        <div className={'text-[--text-secondary] flex items-center'}>
          <div className={`bg-[${widget.color}] w-2 h-2 rounded-sm mr-1`} />
          <div className={'text-b4 mr-1'}>{t.settings.billing.usages.used}: </div>
          <div className={'font-[12px]'}>{formatUsedValue}</div>
        </div>
        <div className={'text-[--text-secondary] flex items-center'}>
          <div className={'w-2 h-2 rounded-sm	 bg-[--bg-controls] mr-1'} />
          <div className={'text-b4 mr-1'}>{t.settings.billing.usages.unused}: </div>
          <div className={'font-[12px]'}>
            {widget.total === -1 || widget.total === -2 ? t.pricing.features.unlimited : formatRemainValue}
          </div>
        </div>
      </div>
    </div>
  );
}
