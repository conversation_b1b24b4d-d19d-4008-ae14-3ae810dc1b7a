import assert from 'assert';
import { DataStreamString } from '@ai-sdk/ui-utils';
import type { JSONValue, StreamTextResult, ToolSet, DataStreamWriter } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import {
  // AIMessageTool,
  AIMessageBO,
  AIMessagePartToolInvocation,
} from '@bika/types/ai/bo';
import { AIResolveVO, type AIIntentUIVO, type AISource, ResolutionMessage, AIMessageVO } from '@bika/types/ai/vo';
import { CONST_PREFIX_AI_MESSAGE } from '@bika/types/database/vo';
import { Locale } from '@bika/types/i18n/bo';
import { SkillsetSelectDTO } from '@bika/types/skill/dto';

const SPILT_CHARS_LENGTH = 5;
/**
 * DataStreamResponse 处理器，将 AI Wizard 的数据流转换为 Streaming Response
 */
export class DataStreamUtils {
  static splitStringByLength(str: string, length: number) {
    const parts = [];

    for (let i = 0; i < str.length; i += length) {
      const part = str.slice(i, i + length);
      parts.push(part);
    }

    return parts;
  }

  /**
   * AI SDK 的data stream -> AIMessage
   *
   * @deprecated 试用 appendResponseMessage, ai sdk 官方
   */
  public static async toAIMessageBO(
    result: StreamTextResult<ToolSet, unknown>,
    returnPrompts?: string[],
    skillsets?: SkillsetSelectDTO[],
  ): Promise<AIMessageBO> {
    const newMsg: AIMessageBO = {
      id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
      role: 'assistant',
      // text: await result.text,
      parts: [],
      // annotations: [],
      skillsets,
      prompts: returnPrompts,
    };
    // const resp = await result.response;
    const steps = await result.steps;
    for (const step of steps) {
      if (step.sources) {
        for (const source of step.sources) {
          newMsg.parts.push({
            type: 'source',
            source,
          });
        }
      }
      if (step.reasoning && step.reasoning !== '') {
        newMsg.parts.push({
          type: 'reasoning',
          reasoning: step.reasoning,
          details: step.reasoningDetails,
        });
      }

      if (step.text && step.text !== '') {
        newMsg.parts.push({
          type: 'text',
          text: step.text,
        });
      }

      if (step.toolResults) {
        for (const toolResult of step.toolResults) {
          const args = (toolResult as { args: any }).args;

          assert(args, `args is required of toolResult: ${JSON.stringify(toolResult)}`);

          newMsg.parts.push({
            type: 'tool-invocation',
            toolInvocation: {
              toolCallId: (toolResult as { toolCallId: string }).toolCallId,
              state: 'result',
              toolName: (toolResult as { toolName: string }).toolName,
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              args,
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              result: (toolResult as { result: any }).result,
            },
          });
        }
      }
    }

    return newMsg;
  }

  /**
   * 返回 intentUI，AIResolveVO  可能是 message resolve，也可能是 ui resolve，最终都返回 UI 响应
   *
   * @param resolve
   * @param intentUI
   */
  public static async *intentUI(resolve: AIResolveVO, intentUI: AIIntentUIVO): AsyncGenerator<DataStreamString> {
    // tool call
    const toolCallId = generateNanoID('call_'); // 'call_mvklr6ky7k8gc7egrxfl89md';
    yield `9:{"toolCallId":"${toolCallId}","toolName":"intent-ui","args":${JSON.stringify(resolve)}}\n`;
    // tool result
    // const intentUIVO = { type: 'CONFIRM' } as AIIntentUIVO;
    yield `a:{"toolCallId":"${toolCallId}","result":${JSON.stringify(intentUI)}}\n`;
  }

  public static async *tool(tool: AIMessagePartToolInvocation): AsyncGenerator<DataStreamString> {
    // tool call
    const toolCallId = generateNanoID('call_'); // 'call_mvklr6ky7k8gc7egrxfl89md';

    // 9: 一次性开始带 args
    yield `9:{"toolCallId":"${toolCallId}","toolName":"${tool.toolName}","args":${JSON.stringify(tool.args)}}\n`;

    // yield `b:{"toolCallId":"${toolCallId}","toolName":"${tool.toolName}"}\n`;

    // streaming 返回 args
    // const argsStr = JSON.stringify(tool.args);
    // yield `c:{"toolCallId":"${toolCallId}","argsTextDelta":"${argsStr}"}\n`;
    // for (const c of this.splitStringByLength(argsStr, 100)) {
    //   yield `c:{"toolCallId":"${toolCallId}","argsTextDelta":"${c}"}\n`;
    // }

    // tool result
    assert(tool.state === 'result');
    yield `a:{"toolCallId":"${toolCallId}","result":${JSON.stringify(tool.result)}}\n`;
  }

  public static async *source(sources: AISource[]): AsyncGenerator<DataStreamString> {
    for (const source of sources) {
      yield `h:${JSON.stringify(source)}\n`;
    }
  }

  public static async *messageStart(): AsyncGenerator<DataStreamString> {
    yield `f:{"messageId":"${generateNanoID('msg-')}"}\n`;
  }

  public static async *messageEnd(): AsyncGenerator<DataStreamString> {
    yield 'e:{"finishReason":"stop","usage":{"promptTokens":null,"completionTokens":null},"isContinued":false}\n';
    yield 'd:{"finishReason":"stop","usage":{"promptTokens":null,"completionTokens":null}}\n';
  }

  public static async writeMessage(
    dataStreamWriter: DataStreamWriter,
    msg: ResolutionMessage,
    locale: Locale,
    resolve?: AIResolveVO,
  ) {
    if (msg.skillsets) {
      dataStreamWriter.writeData({ type: 'skillsets', skillsets: msg.skillsets } as JSONValue);
    }
    const gen = this.message(
      {
        id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
        role: 'assistant',
        ...msg,
      },
      locale,
      resolve,
    );
    for await (const chunk of gen) {
      dataStreamWriter.write(chunk);
    }
  }

  /**
   * Bika AI Message -> AI SDK DataStreamString
   *
   * @param msg
   * @param resolve
   */
  public static async *message(
    msg: AIMessageVO,
    locale: Locale,
    resolve?: AIResolveVO,
  ): AsyncGenerator<DataStreamString> {
    // if (msg.sources) {
    //   const sourcesGenerator = await DataStreamUtils.source(msg.sources);
    //   for await (const s of sourcesGenerator) {
    //     yield s;
    //   }
    // }

    // if (msg.reasoning) {
    //   const reasoning = iStringParse(msg.reasoning, locale);
    //   const reasoningStreamGenerator = await this.reasoning(reasoning);
    //   for await (const c of reasoningStreamGenerator) {
    //     yield c;
    //   }
    // }
    // No text
    // const text = iStringParse(msg.text, locale);
    // const strStreamGenerator = await this.text(text);
    // for await (const c of strStreamGenerator) {
    //   yield c;
    // }

    // parts
    for (const part of msg.parts) {
      const ttt = part.type;
      if (part.type === 'text') {
        const textStreamGenerator = await this.text(part.text);
        for await (const c of textStreamGenerator) {
          yield c;
        }
      } else if (part.type === 'tool-invocation') {
        const toolStreamGenerator = await this.tool(part.toolInvocation);
        for await (const c of toolStreamGenerator) {
          yield c;
        }
      } else if (part.type === 'source') {
        const sourceStreamGenerator = await this.source([part.source]);
        for await (const c of sourceStreamGenerator) {
          yield c;
        }
      } else if (part.type === 'reasoning') {
        const reasoningStreamGenerator = await this.reasoning(part.reasoning);
        for await (const c of reasoningStreamGenerator) {
          yield c;
        }
      } else {
        console.warn('Unknown no handle AI message part type:', ttt);
      }
    }

    // if (msg.tools) {
    //   for (const tool of msg.tools) {
    //     const uiStreamGenerator = await this.tool(tool);
    //     for await (const c of uiStreamGenerator) {
    //       yield c;
    //     }
    //   }
    // }

    if (msg.ui) {
      assert(resolve);
      // if UI response
      const uiStreamGenerator = await this.intentUI(resolve, msg.ui);
      for await (const c of uiStreamGenerator) {
        yield c;
      }
    }
    if (msg.prompts) {
      for await (const c of this.prompts(msg.prompts)) {
        yield c;
      }
    }
    if (msg.annotations) {
      for (const ano of msg.annotations) {
        yield `8:[${JSON.stringify(ano)}]\n`;
      }
    }
    // if (msg.creator) {
    //   yield `8:[{"type":"creator", "creator": ${JSON.stringify(msg.creator)}}]\n`;
    // }

    if (msg.skillsets) {
      yield `8:[{"type":"skillsets", "skillsets": ${JSON.stringify(msg.skillsets)}}]\n`;
    }
  }

  public static async *prompts(prompts: string[]): AsyncGenerator<DataStreamString> {
    yield `8:[{"type":"prompts", "prompts": ${JSON.stringify(prompts)}}]\n`;
  }

  /**
   *  只写入部分
   *
   * @param text
   */
  public static async *reasoning(text: string): AsyncGenerator<DataStreamString> {
    for (const c of this.splitStringByLength(text, SPILT_CHARS_LENGTH)) {
      yield `g:"${c}"\n`;
    }
  }

  /**
   *  只写入部分
   *
   * @param text
   */
  public static async *text(text: string): AsyncGenerator<DataStreamString> {
    //
    // yield `f:{"messageId":"${generateNanoID('msg-')}"}\n`;

    for (const c of this.splitStringByLength(text, SPILT_CHARS_LENGTH)) {
      yield `0:"${c}"\n`;
      // 外部 sleep
    }
    // 写 data 也不 影响
    // yield 'initialized call';
    // yield 'call started';

    // 写tool , Tool Call Part
    // const intentUIResolve = AIIntentUIResolveDTOSchema.parse({ type: 'CONFIRM', confirm: true });
    // const toolCallId = 'call_mvklr6ky7k8gc7egrxfl89md';
    // yield `9:{"toolCallId":"${toolCallId}","toolName":"intent-ui","args":${JSON.stringify(intentUIResolve)}}\n`;
    // // tool result
    // const intentUIVO = AIIntentUIVOSchemas.parse({ type: 'CONFIRM' } as AIIntentUIVO);
    // yield `a:{"toolCallId":"${toolCallId}","result":${JSON.stringify(intentUIVO)}}\n`;

    // // 结束了
    // yield 'e:{"finishReason":"stop","usage":{"promptTokens":null,"completionTokens":null},"isContinued":false}\n';
    // yield 'd:{"finishReason":"stop","usage":{"promptTokens":null,"completionTokens":null}}\n';

    // const result = streamText({
    //   model: openai('gpt-4o'),
    //   messages,
    //   onChunk() {
    //     dataStream.writeMessageAnnotation({ chunk: '123' });
    //   },
    //   onFinish() {
    //     // message annotation:
    //     dataStream.writeMessageAnnotation({
    //       id: generateId(), // e.g. id from saved DB record
    //       other: 'information',
    //     });

    //     // call annotation:
    //     dataStream.writeData('call completed');
    //   },
    // });

    // result.mergeIntoDataStream(dataStream);
    // },
    //   onError: (error) =>
    //     // Error messages are masked by default for security reasons.
    //     // If you want to expose the error message to the client, you can do so here:
    //     error instanceof Error ? error.message : String(error),
    // });
    // return response;
  }

  /**
   * 拿到ai返回的切割好的 text stream, 直接返回,
   * @param textStream ai text stream
   */
  public static async *textStream(textStream: AsyncIterable<string>): AsyncGenerator<DataStreamString> {
    for await (const chunk of textStream) {
      // 替换 \r \n " \ 因为客户端会自动转义, 所以需要替换回来,
      const str = chunk.replace(/\\/g, '\\\\').replace(/\r/g, '\\r').replace(/\n/g, '\\n').replace(/"/g, '\\"');
      yield `0:"${str}"\n` as DataStreamString;
    }
  }
}
