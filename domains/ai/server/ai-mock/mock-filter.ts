import { LanguageModelV1, LanguageModelV1CallOptions, type LanguageModelV1StreamPart } from '@ai-sdk/provider';
import { simulateReadableStream } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';

export type IMockFilter = {
  check: (options: LanguageModelV1CallOptions) => Promise<boolean>;
  doStream: LanguageModelV1['doStream'] | string;
};

const findUserKeywordPrompt = (prompt: LanguageModelV1CallOptions['prompt']): string | undefined => {
  const userPrompt = 'cfo of a company that completed its ipo in 2025';
  // 返回包含用户提示的消息
  if (!prompt || !Array.isArray(prompt)) {
    return undefined;
  }
  for (const p of prompt) {
    if (p.role !== 'user') {
      continue;
    }
    for (const c of p.content) {
      if (c.type === 'text' && c.text.toLowerCase().includes(userPrompt)) {
        return c.text;
      }
    }
  }
  // 如果没有找到，返回 undefined
  return undefined;
};

const existsTool = (mode: LanguageModelV1CallOptions['mode'], toolName: string): boolean => {
  switch (mode.type) {
    case 'regular':
      return mode.tools?.some((tool) => tool.name === toolName) ?? false;
    case 'object-tool':
      return mode.tool.name === toolName;
    default:
      return false;
  }
};

export const datasetMockFilter: IMockFilter = {
  check: async (options) => {
    const { mode, prompt } = options;
    // check model tools exist companies or people
    const isExistTool = existsTool(mode, 'companies') || existsTool(mode, 'people');
    if (!isExistTool) {
      return false;
    }
    const userKeywordPrompt = findUserKeywordPrompt(prompt);
    if (!userKeywordPrompt) {
      return false;
    }
    return true;
  },
  doStream: async (options: LanguageModelV1CallOptions) => {
    const { mode, prompt } = options;
    const userKeywordPrompt = findUserKeywordPrompt(prompt);

    // 创建 tool call
    const chunks: LanguageModelV1StreamPart[] = [];
    if (existsTool(mode, 'companies')) {
      chunks.push({
        type: 'tool-call',
        toolCallId: generateNanoID('tool-call-'),
        toolName: 'companies',
        args: JSON.stringify({ query: userKeywordPrompt }),
        toolCallType: 'function',
      });
    }
    if (existsTool(mode, 'people')) {
      chunks.push({
        type: 'tool-call',
        toolCallId: generateNanoID('tool-call-'),
        toolName: 'people',
        args: JSON.stringify({ query: userKeywordPrompt }),
        toolCallType: 'function',
      });
    }

    chunks.push({
      type: 'finish',
      finishReason: 'stop',
      logprobs: undefined,
      usage: { completionTokens: 10, promptTokens: 3 },
    });

    return {
      stream: simulateReadableStream({
        chunks,
        initialDelayInMs: 100,
        chunkDelayInMs: 50,
      }),
      rawCall: { rawPrompt: null, rawSettings: {} },
    };
  },
};
