import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import { IEdgeCommandTelegramMessageResponse } from '@bika/api-caller/types';
import { db, AIIntegrationChatModel } from '@bika/server-orm';
import { CONST_PREFIX_AI } from '@bika/types/database/vo';
import { iStringParse } from '@bika/types/system';
import { ApiFetchRequestContext } from '@bika/types/user/vo';
import { AIChatSO } from '../ai-chat/ai-chat-so';
/**
 * AI集成入口, 接受TG、Whatsapp等消息来源
 */
export class AIIntegrationChatSO {
  private _model: AIIntegrationChatModel;

  private _wizard: AIChatSO;

  public get model() {
    return this._model;
  }

  private constructor(model: AIIntegrationChatModel, wizard: AIChatSO) {
    this._model = model;
    this._wizard = wizard;
  }

  public async chat(ctx: ApiFetchRequestContext, humanSay: string): Promise<IEdgeCommandTelegramMessageResponse> {
    // const response = await ChatAIIntentResolver.AIChat(humanLocale, chatHistories);
    const { message } = await this._wizard.message(ctx, humanSay);
    return {
      message: iStringParse(message.text),
    };
  }

  public static async getChat(userId: string, chatId: string, type: 'TELEGRAM') {
    // 查找更新时间小于5分钟的记录
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    let wizard: AIChatSO;
    let recentChat = await db.mongo.aiIntegrationChat.findOne({
      chatId,
      type,
      updatedAt: { $gt: fiveMinutesAgo },
    });
    if (!recentChat) {
      // 最近5分钟，没有过聊天，或，旧聊天超时了，新建一个
      // 新建wizard

      const newWizardSO = await AIChatSO.create('USER', userId, { type: 'CHAT' }); // TODO, 这里不应该传Chat ID，而是应该根据chatId和telegram user id匹配我们的userid，未定鉴权方案
      wizard = newWizardSO;

      recentChat = await db.mongo.aiIntegrationChat.create({
        id: generateNanoID(CONST_PREFIX_AI),
        chatId,
        type,
        aiWizardId: newWizardSO.model.id,
      });
    } else {
      // 有聊过天？找到集成chat，那么取出AI Wizard，这里是user role
      const oldWizardSO = await AIChatSO.get(recentChat.aiWizardId);
      assert(oldWizardSO);
      wizard = oldWizardSO;
      // 肯定有的，不可能没有
    }
    // AI Wizard，绑定
    return new AIIntegrationChatSO(recentChat, wizard);
  }
}
