import { StreamObjectResult, StreamTextResult, ToolSet, DataStreamWriter } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { z } from 'zod';
import { IAIWriterContext } from '@bika/contents/config/client/ai/ai-writer/ai-writer';
import { aiWriterServerConfigs } from '@bika/contents/config/server/ai-writer-server';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import type { UserSO } from '@bika/domains/user/server/user-so';
import { db } from '@bika/server-orm';
import type { AIWriter, AIWriterResponse, AICompletionKind, AICompletionBO, AIUsage } from '@bika/types/ai/bo';
import { AISO } from './ai-so';
import { newAIWriter } from './ai-writer/ai-writer-factory';

export class AIWriterSO {
  public static async complete(
    userPrompt: string,
    writer: AIWriter,
    user: UserSO,
  ): Promise<
    | {
        type: 'OBJECT';
        result: StreamObjectResult<object, object, never>;
      }
    | {
        type: 'TEXT';
        result: StreamTextResult<ToolSet, never>;
      }
  > {
    const config = aiWriterServerConfigs[writer.type];
    if (config.type === 'OBJECT') {
      const result = await AISO.streamObject(
        {
          user,
          system: config.systemPrompt,
          schema: config.schema!,
          prompt: userPrompt,
        },
        {
          // model: 'deepseek-v3',
        },
      );
      return { type: 'OBJECT', result };
    }
    if (config.type === 'TEXT') {
      const { result } = await AISO.streamText(
        {
          user,
          system: config.systemPrompt,
          prompt: userPrompt,
        },
        {
          // model: 'deepseek-v3',
        },
      );
      return { type: 'TEXT', result };
    }

    throw new Error('Unsupported AI writer type');
  }

  public static async test(user: UserSO) {
    const result = AISO.streamObject(
      {
        user,
        // model: openai('gpt-4-turbo'),
        schema: z.object({
          name: z.string(),
          description: z.string(),
        }),
        prompt: `Generate 3 random notifications for a messages app`,
      },
      {},
    );
    return result;
  }

  /**
   * 快速根据预设的业务场景prompt进行写作
   */
  public static async quickWrite(
    writer: AIWriter,
    userPrompt: string,
    context: IAIWriterContext,
    user: UserSO,
    dataStreamWriter?: DataStreamWriter,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): Promise<AIWriterResponse> {
    const writeType = writer.type;
    const writerCls = newAIWriter(writeType, writer, context, user);

    const result = await writerCls.write(userPrompt, dataStreamWriter);

    if (dataStreamWriter) {
      dataStreamWriter.writeData({
        type: 'writer-response',
        response: result,
      });
    }

    // cost space credit
    if (context.spaceId && result.usages?.length) {
      const space = await SpaceSO.init(context.spaceId);
      const coinsAccount = await space.billing.getCoinsAccount();
      const totalCost = result.usages.reduce((acc, usage) => acc + usage.costCredit, 0);

      const completionBO: AICompletionBO = {
        kind: 'writer',
        writer,
      };

      const aiUsage: AIUsage = {
        // model: imageBOConfig.imageModel,
        costCredit: totalCost,
      };

      const completionId = generateNanoID('aic');

      // 记录 AI Completion 和 Log，可异步
      await Promise.all([
        coinsAccount.redeem(
          totalCost,
          {
            reason: 'cost-ai-completion-credit',
            completionId,
          },
          undefined,
          undefined,
          user.id,
        ),
        db.mongo.aiCompletion.create({
          id: completionId,
          kind: 'writer' as AICompletionKind,
          bo: completionBO,
          usage: aiUsage,
        }),
        db.log.write({
          kind: 'AI_LOG',
          data: JSON.stringify({
            type: 'AI_COMPLETION',
            completion: completionBO,
          }),
          usage: JSON.stringify(aiUsage),
        }),
      ]);
    }

    const message = result?.success === true ? '' : result.message || 'AI quickWrite error';
    return {
      data: result.data,
      value: result.value,
      success: result.success ?? false,
      message,
      usages: result.usages,
    };
  }
}
