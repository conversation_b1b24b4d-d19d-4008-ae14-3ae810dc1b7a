import {
  type Message,
  ToolSet,
  StreamTextOnChunkCallback,
  StreamTextOnStepFinishCallback,
  Schema,
  DataStreamWriter,
  LanguageModelUsage,
  Attachment,
  type StreamTextResult,
  type StreamObjectResult,
} from 'ai';
import OpenAI from 'openai/index';
import { z } from 'zod';
import type { SpaceSO } from '@bika/domains/space/server/space-so';
import type { UserSO } from '@bika/domains/user/server/user-so';
import { PresetLanguageAIModelDef, AIIntentParams, AIMessageBO, AIUsage, toAISDKMessage } from '@bika/types/ai/bo';
import type { IAIModelSelectBO } from '@bika/types/ai/bo';
import type { ResolutionMessage, IntentResolutionStatus, AIMessageVO } from '@bika/types/ai/vo';
import {
  DatabaseFieldWithId,
  DatabaseFieldConfigSelectOption,
  CellValue,
  AttachmentCellData,
} from '@bika/types/database/bo';
import { type SkillsetSelectDTO } from '@bika/types/skill/dto';
import { iStringParse, LocaleType } from '@bika/types/system';

export type ChatMessage = Message; // | Omit<Message, 'id'>;
export type ChatMessages = Array<Message>; // | Array<Omit<Message, 'id'>>;

export async function convertAttachmentToBase64(attachment: Attachment): Promise<Attachment> {
  if (attachment.url.startsWith('data:')) {
    return attachment;
  }
  const response = await fetch(attachment.url);
  const buffer = await response.arrayBuffer();
  const base64 = Buffer.from(buffer).toString('base64');
  return { ...attachment, url: `data:${attachment.contentType};base64,${base64}` };
}

export async function convertAIMessageBOToChatMessage(aiMessage: AIMessageBO): Promise<Message> {
  const message: Message = toAISDKMessage(aiMessage);
  return message;
}

export async function convertAIMessageBOArrayToChatMessages(aiMessage: AIMessageBO[]): Promise<ChatMessages> {
  const messages: Array<Message> = [];

  for (const message of aiMessage) {
    const aiSDKMsg = await convertAIMessageBOToChatMessage(message);
    messages.push(aiSDKMsg);
  }
  return messages;
}

/**
 *  用于创建 ResumableStream
 */
export type IStreamResult<OBJECT_SCHEMA = never> =
  | {
      type: 'object';
      objectResult: StreamObjectResult<Partial<OBJECT_SCHEMA>, OBJECT_SCHEMA, never>;
    }
  | {
      type: 'text';
      textResult: StreamTextResult<ToolSet, never>;
    }
  | {
      // 返回给 dataStreamWriter 使用的 Yield Generator
      type: 'data-stream';
      // *dataStreamFunc: {func: 'writeData', data: JSONValue} | {func:'write', data: string}[] for dataStreamWriter.write(), return a yield generator
    };

export function getArtifactIdsFromToolInvocation(message: AIMessageBO): string[] {
  return (
    message
      .parts!.filter((p) => {
        if (p.type !== 'tool-invocation') return false;
        return (
          p.toolInvocation.state === 'result' &&
          p.toolInvocation.result &&
          typeof p.toolInvocation.result === 'object' &&
          'artifactId' in p.toolInvocation.result &&
          typeof p.toolInvocation.result.artifactId === 'string'
        );
      })
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .map((p: any) => p.toolInvocation.result.artifactId as string)
  );
}

/**
 * Request Prompt
 */
export interface IPrompt {
  prompt?: string;
  system?: string;
  messages?: AIMessageBO[];
  /**
   * Preset Skillsets (multiple tools inside)
   */
  skillsets?: SkillsetSelectDTO[];
  /**
   * Custom tools, final tools = Toolset's tools + Custom tools
   */
  tools?: ToolSet;
  maxSteps?: number;
  // Request User
  user: UserSO;
  // eslint-disable-next-line no-undef
  space?: SpaceSO;
}
export type IPromptWithSchema<OBJECT> = IPrompt & {
  // Object Schema 指定格式
  schema: z.Schema<OBJECT, z.ZodTypeDef, any> | Schema<OBJECT>;
};

export type IStreamTextReturn = { result: StreamTextResult<ToolSet, never>; prompt: IPrompt; options: AIModelOptions };
export type IStreamTextPrompt = Omit<IPrompt, 'skillsets'>;
export type IStreamChatReturn = { message: AIMessageBO; usage: AIUsage } & IStreamTextReturn;
export type IStreamChatPrompt = Omit<IPrompt, 'prompt'> & { messages: AIMessageBO[] };

export interface AIModelOptions {
  // 如果直接传入 AIModelDef，则理解为 preset 模型
  model?: PresetLanguageAIModelDef | IAIModelSelectBO;

  /**
   * @deprecated use model selector instead
   */
  apiKey?: string;
  /**
   * @deprecated use model selector instead
   */
  baseUrl?: string;
  /**
   * @deprecated use model selector instead
   */
  organizationId?: string;
  /**
   * @deprecated use model selector instead
   */
  apiVersion?: string; // For Azure OpenAI, default is '2024-10-01-preview'
  /**
   * @deprecated use model selector instead
   */
  region?: string; // 用于指定模型所在的区域，默认是 global
  /**
   * @deprecated use model selector instead
   */
  secretAccessKey?: string; // 用于 Amazon Bedrock 的访问密钥

  /**
   * @todo 是否应该移到 IPrompt，跟随请求，而不是跟随模型选择
   */
  temperature?: number;
  // 是否开启json模式，或传入schema模式？只有OpenAI gpt-4以上才严谨支持Zod Schema模式哦，其它的模型要在prompt里说好
  json?: true | z.ZodType | OpenAI.ResponseFormatJSONSchema;
  onFinish?: (data: { usage: LanguageModelUsage }) => void;
  onChunk?: StreamTextOnChunkCallback<ToolSet>;
  onStepFinish?: StreamTextOnStepFinishCallback<ToolSet>;
  onError?: (error: unknown) => void;
  dataStreamWriter?: DataStreamWriter;
}

/**
 * OpenAI(AI SDK) streaming yield value
 */
export interface IAIStreamYield {
  isReasoning: boolean;
  // reasongContent or Content delta
  chunkContent?: string;
  // full reasoning content
  reasoningContent: string;
  // full content
  content: string;
}

export type ResolutionResultInfo =
  // | DataStreamString
  {
    message: ResolutionMessage;
    status: IntentResolutionStatus;
    intentParam: AIIntentParams; // 经过resolve后的新的意图参数对象

    // 强制required，因为考虑到避免漏写导致的没有扣钱（成本啊）
    usage: AIUsage;
    // 返回计算得到的 SkillsetSelectDTO 数组
    prompt?: IPrompt;
    options?: AIModelOptions;
  };

export type WizardResolutionResult = AIMessageVO;

/**
 * 通过 DatabaseFieldDTO 数组生成一份关于字段的 JSON Schema 供 LLM 调用。
 * 例：通过 copilot 创建记录，使用 JSON Schema 来描述字段可以让 AI 更容易生成准确的记录内容。
 */
export function generateFieldsJSONSchemaByFieldDTO(
  fields: DatabaseFieldWithId[],
  locale: LocaleType,
): OpenAI.ResponseFormatJSONSchema {
  const schema: {
    type: string;
    properties: Record<string, unknown>;
    additionalProperties: boolean;
    required: string[];
  } = {
    type: 'object',
    properties: {},
    additionalProperties: false,
    required: [],
  };

  const _getJSTypeDefinitionByFieldType = (field: DatabaseFieldWithId): object | null => {
    if (['LONG_TEXT', 'SINGLE_TEXT', 'EMAIL', 'URL', 'PHONE'].includes(field.type)) {
      return { type: field.required ? 'string' : ['string', null] };
    }

    if (field.type === 'CHECKBOX') {
      return { type: field.required ? 'boolean' : ['boolean', null] };
    }

    if (['NUMBER', 'CURRENCY', 'PERCENT'].includes(field.type)) {
      return { type: field.required ? 'number' : ['number', null] };
    }

    if (field.type === 'RATING') {
      // openAI does not support `max` property in JSON schema, so we will just use description for providing the range
      const description =
        field.property && 'max' in field.property
          ? `Rating from 0 to ${field.property.max}. if over ${field.property.max}, reduce to ${field.property.max}`
          : '';
      return { type: field.required ? 'integer' : ['integer', null], description };
    }

    if (field.type === 'DATETIME') {
      return {
        type: field.required ? 'boolean' : ['boolean', null],
        description: 'ISO 8601 date-time format, with UTC timezone. Example: 2022-01-01T12:00:00Z',
      };
    }

    if (field.type === 'SINGLE_SELECT' || field.type === 'MULTI_SELECT') {
      if (field.property && 'options' in field.property) {
        // Ensure options are available and limit to 20 for performance
        const enumValues = field.property.options.slice(0, 20).map((option) => iStringParse(option.name, locale));

        return {
          type: field.required ? 'array' : ['array', null],
          items: {
            type: 'string',
            enum: enumValues,
            description: field.type === 'SINGLE_SELECT' ? 'Single selection' : 'one or more selections',
          },
          additionalProperties: false,
        };
      }
    }

    // TODO: MEMBER, DATERANGE and ATTACHMENT field types are not supported temporarily

    return null;
  };

  fields.forEach((field) => {
    const fieldName = iStringParse(field.name, locale);
    const keywordsForField = _getJSTypeDefinitionByFieldType(field);
    if (keywordsForField) {
      schema.properties![fieldName] = keywordsForField;

      if (field.required) {
        schema.required!.push(fieldName);
      }
    }
  });

  return {
    type: 'json_schema',
    json_schema: {
      name: 'fields_in_datasheet',
      schema,
      strict: true,
    },
  };
}

// AI 根据 Fields JSON Schema 生成的记录内容需要转换为 Cell 格式，才能存储到数据库中
export function convertFieldValuesToCellFormat(
  fieldsSchema: DatabaseFieldWithId[],
  fieldValues: Record<string, unknown>,
  locale: LocaleType,
): Record<string, unknown> {
  const cells: Record<string, unknown> = {};
  const _getCellValueByFieldType = (field: DatabaseFieldWithId, fieldValue: unknown): unknown => {
    if (field.type === 'LONG_TEXT' || field.type === 'SINGLE_TEXT') {
      return String(fieldValue);
    }

    if (field.type === 'CHECKBOX') {
      return Boolean(fieldValue);
    }

    if (['NUMBER', 'CURRENCY', 'PERCENT'].includes(field.type)) {
      return Number(fieldValue);
    }

    if (field.type === 'RATING') {
      return Number(fieldValue);
    }

    if (field.type === 'DATETIME') {
      return new Date(String(fieldValue)).toISOString();
    }

    if (field.type === 'SINGLE_SELECT' || field.type === 'MULTI_SELECT') {
      if (Array.isArray(fieldValue)) {
        const validOptionIds = fieldValue
          .map((optionName: string) => {
            const option = (field.property?.options as DatabaseFieldConfigSelectOption[])?.find(
              (opt: DatabaseFieldConfigSelectOption) => opt.name === optionName,
            );
            return option ? option.id : null;
          })
          .filter((id): id is string => id !== null);

        if (field.type === 'SINGLE_SELECT' && validOptionIds.length > 0) {
          return [validOptionIds[0]];
        }

        return validOptionIds;
      }
      if (typeof fieldValue === 'string') {
        const option = (field.property?.options as DatabaseFieldConfigSelectOption[])?.find(
          (opt: DatabaseFieldConfigSelectOption) => opt.name === fieldValue,
        );
        return option ? [option.id] : [];
      }
    }

    return fieldValue;
  };

  fieldsSchema.forEach((fieldSchema) => {
    const fieldName = iStringParse(fieldSchema.name, locale);
    const fieldValue = fieldValues[fieldName];
    if (fieldValue !== undefined) {
      cells[fieldName] = _getCellValueByFieldType(fieldSchema, fieldValue);
    }
  });

  return cells;
}

export type SimpleColumnType = 'boolean' | 'string' | 'number' | 'stringArray';
export interface GridArtifactField {
  name: string;
  type: SimpleColumnType;
}

// 重命名  取自cnvertCellDataToPureString
export type GridValue = string | string[] | number | boolean | Date | CellValue;

export interface ListRecordToolResult {
  isError: boolean;
  fields: GridArtifactField[];

  records: {
    data: Record<string, GridValue>;
  }[];
}

// 根据convertCellDataToPureString  映射
export function convertFieldTypeToSimpleGridType(fieldSchema: DatabaseFieldWithId): SimpleColumnType {
  if (fieldSchema.type === 'LONG_TEXT' || fieldSchema.type === 'SINGLE_TEXT') {
    return 'string';
  }

  if (fieldSchema.type === 'CHECKBOX') {
    return 'boolean';
  }

  if (['NUMBER', 'CURRENCY', 'PERCENT'].includes(fieldSchema.type)) {
    return 'number';
  }

  if (fieldSchema.type === 'RATING') {
    return 'number';
  }

  if (fieldSchema.type === 'DATETIME') {
    return 'string';
  }

  if (fieldSchema.type === 'SINGLE_SELECT' || fieldSchema.type === 'MULTI_SELECT') {
    return 'stringArray';
  }

  if (fieldSchema.type === 'ATTACHMENT') {
    return 'stringArray';
  }

  return 'string';
}

// 从数据库读取出来的记录数据，每个字段的值的结构都不同，
// 此函数将其转换为纯字符串格式，方便AI理解与减少token消耗
export function convertCellDataToPureString(
  fieldSchema: DatabaseFieldWithId,
  cellValue: CellValue,
  locale: LocaleType,
): GridValue {
  if (cellValue === null || cellValue === undefined) {
    return '';
  }

  if (fieldSchema.type === 'LONG_TEXT' || fieldSchema.type === 'SINGLE_TEXT') {
    return String(cellValue);
  }

  if (fieldSchema.type === 'CHECKBOX') {
    return Boolean(cellValue);
  }

  if (['NUMBER', 'CURRENCY', 'PERCENT'].includes(fieldSchema.type)) {
    return Number(cellValue);
  }

  if (fieldSchema.type === 'RATING') {
    return Number(cellValue);
  }

  if (fieldSchema.type === 'DATETIME') {
    return new Date(String(cellValue)).toISOString();
  }

  if (fieldSchema.type === 'SINGLE_SELECT' || fieldSchema.type === 'MULTI_SELECT') {
    if (Array.isArray(cellValue)) {
      const options = (fieldSchema.property?.options as DatabaseFieldConfigSelectOption[]) || [];
      return cellValue
        .map((id) => {
          const name = options.find((opt) => opt.id === id)?.name;
          return name !== undefined ? iStringParse(name, locale) : undefined;
        })
        .filter((name): name is string => name !== undefined);
    }
  }

  if (fieldSchema.type === 'ATTACHMENT') {
    // 如果是附件类型，cellValue是数组，以markdown的文本超链接格式返回附件的名称和URL组成的数组
    if (Array.isArray(cellValue)) {
      return (cellValue as AttachmentCellData[]).map((attachment) => `[${attachment.name}](${attachment.path})`);
    }
  }

  return cellValue;
}
