import { createDataStream, type DataStreamWriter } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { after } from 'next/server';
import { createResumableStreamContext, type ResumableStreamContext } from 'resumable-stream';
import { isInUnitTest } from 'sharelib/app-env';
import { sleep } from 'sharelib/sleep';
import { db, RedisResultKeyValue } from '@bika/server-orm';

// interface IStreamContext {
//   streamContext: ResumableStreamContext | null;
//   streamId: string;
//   chatId: string;
//   userId?: string;
// }

const chatStreamCache = {
  key: (chatId: string, userId?: string) => {
    if (userId) {
      return `ai:streamId:${chatId}:${userId}`;
    }
    return `ai:streamId:${chatId}`;
  },
  storeStreamId: (chatId: string, streamId: string, userId?: string): Promise<RedisResultKeyValue> =>
    db.redis.set(chatStreamCache.key(chatId, userId), streamId, 15 * 60),
  getStreamId: (chatId: string, userId?: string): Promise<string | null> =>
    db.redis.get(chatStreamCache.key(chatId, userId)),
};

let globalStreamContext: ResumableStreamContext | null = null;

export function getStreamContext(): ResumableStreamContext | null {
  if (!globalStreamContext) {
    try {
      globalStreamContext = createResumableStreamContext({
        waitUntil: !isInUnitTest() ? after : () => {},
        keyPrefix: 'ai:stream',
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error.message.includes('REDIS_URL')) {
        console.log(' > Resumable streams are disabled due to missing REDIS_URL');
      } else {
        console.error(error);
      }
    }
  }
  return globalStreamContext;
}

export async function getResumableStreamId(chatId: string, userId?: string): Promise<string | null> {
  const streamContext = getStreamContext();
  if (!streamContext) {
    return null;
  }
  return chatStreamCache.getStreamId(chatId, userId);
}

export async function getResumableStream(streamId: string): Promise<ReadableStream<string> | null> {
  const streamContext = getStreamContext();
  if (!streamContext) {
    return null;
  }

  const emptyDataStream = createDataStream({
    execute: () => {},
  });

  return streamContext.resumableStream(streamId, () => emptyDataStream);
}

/**
 * 创建可续流数据流
 * @returns
 */
export async function createResumableDataStream(opts: {
  chatId: string;
  execute: (dataStream: DataStreamWriter) => Promise<void> | void;
  streamId?: string;
  userId?: string;
  onError?: (error: unknown) => string;
}): Promise<ReadableStream<string> | null> {
  const streamContext = getStreamContext();

  const streamId = opts.streamId || generateNanoID('crs_');
  if (streamContext) {
    await chatStreamCache.storeStreamId(opts.chatId, streamId, opts.userId);
  }
  const stream = createDataStream({
    execute: opts.execute,
    onError: opts.onError,
  });
  if (streamContext) {
    // pub stream to redis
    return streamContext.resumableStream(streamId, () => stream);
  }
  return stream;
}

// lock，确保该异步， 每个stream id只能被执行一次，否则其它异步等待
// 避免，多个 stream 同时并发，因为异步等待，导致 has === false，导致重复生成 stream
// 可参考单测 ai-artifact.test.ts
const existingStreamLocks: Set<string> = new Set();

export async function hasExistingStream(streamId: string) {
  const streamContext = getStreamContext();
  if (streamContext) {
    try {
      while (existingStreamLocks.has(streamId)) {
        await sleep(100);
      }

      existingStreamLocks.add(streamId);
      const has = await streamContext.hasExistingStream(streamId);
      return has;
    } finally {
      existingStreamLocks.delete(streamId);
    }
  }
  return false;
}

/**
 * 创建续流数据流
 * @returns
 */
export async function createNewResumableStream(opts: {
  chatId: string;
  streamId: string;
  userId: string;
  makeStream: () => ReadableStream<string>;
}): Promise<ReadableStream<string> | null> {
  const streamContext = getStreamContext();
  const streamId = opts.streamId;
  if (streamContext) {
    await chatStreamCache.storeStreamId(opts.chatId, streamId, opts.userId);
  }
  if (streamContext) {
    // pub stream to redis
    return streamContext.createNewResumableStream(streamId, opts.makeStream);
  }

  return opts.makeStream();
}
/**
 * 创建 或 获取流
 *
 * @param opts
 * @returns
 */
export async function resumableStream(opts: {
  chatId: string;
  streamId: string;
  userId: string;
  makeStream: () => ReadableStream<string>;
}): Promise<ReadableStream<string> | null> {
  const streamContext = getStreamContext();
  const streamId = opts.streamId;
  if (streamContext) {
    await chatStreamCache.storeStreamId(opts.chatId, streamId, opts.userId);
  }
  if (streamContext) {
    // pub stream to redis
    return streamContext.resumableStream(streamId, opts.makeStream);
  }

  return opts.makeStream();
}

/**
 * 续流
 * @returns
 */
export async function resumeExistingStream(streamId: string): Promise<ReadableStream<string> | null | undefined> {
  const streamContext = getStreamContext();
  if (streamContext) {
    // pub stream to redis
    return streamContext.resumeExistingStream(streamId);
  }

  return undefined;
}

export function streamResponse(stream: ReadableStream | null, status?: number) {
  return new Response(stream, { headers: { 'Content-Type': 'text/event-stream' }, status });
}
