import { generateNanoID } from 'basenext/utils/nano-id';
import { describe, it, expect } from 'vitest';
import { MockContext } from '../../__tests__/mock/mock.context';
import { AIController } from '../apis/ai-controller';
import { AISO } from '../server/ai-so';

describe('Get wizard history', () => {
  it('should get wizard list correctly-page 2', async () => {
    const { user } = await MockContext.initUserContext();
    const nodeId = generateNanoID();

    const mockWizard = async () => {
      const wizardSO = await AISO.newWizardByUser(user.id, {
        type: 'COPILOT',
        copilot: {
          type: 'node',
          nodeId,
        },
      });
      await wizardSO.doSaveHumanMessage(
        [],
        {
          type: 'MESSAGE',
          message: 'Hello',
        },
        user.id,
      );
      await wizardSO.doSaveAiResponse(
        {
          usage: {
            model: 'default',
            promptTokens: 100,
            completionTokens: 100,
            totalTokens: 1000,
            costCredit: 88,
          },
          message: {
            parts: [
              {
                type: 'text',
                text: 'World',
              },
            ],
            text: 'World',
          },
          status: 'DIALOG',
          intentParam: {
            type: 'COPILOT',
            copilot: {
              type: 'node',
              nodeId: '123',
            },
          },
        },

        wizardSO.intent,
        user.id,
        // [
        //   {
        //     type: 'HUMAN',
        //     text: 'Hello',
        //     createdBy: user.id,
        //   },
        //   {
        //     type: 'AI',
        //     text: 'World',
        //   },
        //   {
        //     type: 'HUMAN',
        //     text: 'test',
        //     createdBy: user.id,
        //   },
        // ],
      );
    };
    // 创建5个对话
    await Promise.all(Array.from({ length: 5 }, mockWizard));
    const wizards = await AIController.listWizard(user, {
      pageNo: 2,
      pageSize: 2,
      type: 'AI_COPILOT',
      nodeId,
    });
    expect(wizards.pagination.total).toBe(5);
    expect(wizards.data.length).toBe(2);
    const wizard = wizards.data[0];
    expect(wizard.id).toBeDefined();
    expect(wizard.title).toBe('Hello');
    expect(wizard.description).toBe('World');
    expect(wizard.createdAt).toBeDefined();
    expect(wizard.creator?.id).toBe(user.id);
  });
});

describe('Delete wizard', () => {
  it('should delete wizard correctly', async () => {
    const { user } = await MockContext.initUserContext();
    const nodeId = generateNanoID();
    const mockWizard = async () => {
      const wizardSO = await AISO.newWizardByUser(user.id, {
        type: 'COPILOT',
        copilot: {
          type: 'node',
          nodeId,
        },
      });
      await wizardSO.doSaveAiResponse(
        {
          usage: {
            model: 'default',
            promptTokens: 100,
            completionTokens: 100,
            totalTokens: 1000,
            costCredit: 88,
          },
          message: {
            parts: [
              {
                type: 'text',
                text: 'I am a copilot',
              },
            ],
            text: 'I am a copilot',
          },
          status: 'DIALOG',
          intentParam: {
            type: 'COPILOT',
            copilot: {
              type: 'node',
              nodeId: '123',
            },
          },
        },
        wizardSO.intent,
        // [
        //   {
        //     type: 'HUMAN',
        //     text: 'Hello',
        //     createdBy: user.id,
        //   },
        //   {
        //     type: 'AI',
        //     text: 'World',
        //   },
        //   {
        //     type: 'HUMAN',
        //     text: 'test',
        //     createdBy: user.id,
        //   },
        // ],
        user.id,
      );
      return wizardSO;
    };
    const wizard = await mockWizard();
    const wizards = await AIController.listWizard(user, {
      pageNo: 1,
      pageSize: 5,
      type: 'AI_COPILOT',
      nodeId,
    });
    expect(wizards.data.length).toBe(1);

    await AIController.deleteWizard(user, wizard.model.id);
    const wizards2 = await AIController.listWizard(user, {
      pageNo: 1,
      pageSize: 5,
      type: 'AI_COPILOT',
      nodeId,
    });
    expect(wizards2.data.length).toBe(0);
  });
});
