import { createOpenAI } from '@ai-sdk/openai';
import { streamObject } from 'ai';
import { describe, expect, test } from 'vitest';
import { PresetLanguageModelServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import {
  AutomationCreatePrompt,
  SimpleAutomationSchema,
  AutomationArrayCreatePrompt,
} from '@bika/contents/config/server/ai-prompts/ai-consulting/ai-object-automation.prompt';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AutomationSchema } from '@bika/types/automation/bo';
import { AISO } from '../server/ai-so';

const TEST_AI = process.env.TEST_AI && process.env.TEST_AI === 'true';

describe('AI Create Automation', async () => {
  test('Test AI Create Automation--trigger-deepseek-v3', async () => {
    if (!TEST_AI) {
      return;
    }
    const model = PresetLanguageModelServerConfig['deepseek/deepseek-v3']!;
    const openai = createOpenAI({
      apiKey: model.apiKey,
      baseURL: model.baseUrl,
    });
    const aiModel = openai(model.modelId);

    // 使用更简短的提示
    const shortPrompt = `
触发器-有记录满足条件时

首先，自动化流程需要一个开始的开关，也就是触发器。在这个自动化流程中，我们选择的是【有记录满足条件时】。
也就是，当订单状态变更为「采购中」时触发。

执行器-运行脚本

有了订单，也有了对应的供应商之后，我们就应该创建生产任务。这个过程应该是自动化的，但在此之前，我们需要用脚本结合订单的信息，自动生成一个SKU编号，以便用于唯一标识商品或产品的代码，在库存管理、销售系统等场景中发挥作用。

执行器-创建记录

从上面的流程中，我们获取了订单信息、供应商信息以及SKU编码，那么一个生产任务的基本信息就有了。接下来，我们要做的，就是利用这些基础信息，在订单任务表中自动化创建一个新的记录，便于做任务管理。

这里使用的执行器是「创建记录」，只要选择了要新建记录的表格，然后将上面的触发器和执行器中获取的变量信息引用到对应的表格字段中，就可以实现记录的自动化创建
    `;

    const { partialObjectStream } = streamObject({
      model: aiModel,
      schema: SimpleAutomationSchema,
      temperature: 0.3,
      messages: [
        {
          role: 'system',
          content: AutomationCreatePrompt,
        },
        {
          role: 'user',
          content: shortPrompt,
        },
        {
          role: 'user',
          content: 'use locale zh-CN',
        },
      ],
      onError(event) {
        console.error('Error:', event);
      },
      onFinish(data: any) {
        console.log('response-data', JSON.stringify(data, null, 2));
      },
    });
    const partialObjects = [];
    for await (const partialObject of partialObjectStream) {
      partialObjects.push(partialObject);
    }
    const finalObject = partialObjects[partialObjects.length - 1];
    console.log('finalObject', JSON.stringify(finalObject, null, 2));
    const { data, success } = AutomationSchema.safeParse(finalObject);
    expect(success).toBe(true);
    expect(data?.resourceType).toBe('AUTOMATION');
  }, 100000);

  test('Test AI Create Automation--no-schema-deepseek-v3', async () => {
    if (!TEST_AI) {
      return;
    }
    const { user } = await MockContext.initUserContext();
    const model = PresetLanguageModelServerConfig['deepseek/deepseek-v3']!;
    const openai = createOpenAI({
      apiKey: model.apiKey,
      baseURL: model.baseUrl,
    });
    const aiModel = openai(model.modelId);
    const { result } = await AISO.streamText(
      {
        user,
        system: AutomationArrayCreatePrompt,
        prompt: `
Resources:

订单设计中心(DATABASE): 集中管理客户订单与设计流程

字段:
订单编号: 自动编号 - 唯一订单标识
客户需求文档: 附件 - 客户提供的设计参考文件
设计稿版本: 版本控制 - 存储不同设计迭代版本
设计师分配: 人员选择 - 指定专属设计师
确认状态: 单选(待确认/已确认/需修改)
关系:
设计确认流程: 触发自动化
生产追踪系统: 数据同步
设计确认流程(AUTOMATION): 自动化设计确认与部门流转

触发条件: 设计状态变更为"客户确认"
执行动作:
生成带水印确认书(PDF自动生成)
通知生产部门(企业微信/邮件通知)
创建供应商任务单
审批流程: 部门主管二次确认机制
异常处理: 超时未确认自动提醒
生产追踪系统(DATABASE): 监控生产进度与物流信息

核心字段:
生产阶段: 进度条(原料采购/生产中/质检完成)
物流对接人: 外联人员信息
物流单号: 扫码录入(支持主流快递公司)
实时位置: API集成物流追踪
视图配置:
延误预警视图(预计交付时间<3天)
供应商绩效看板
客户门户(FORM): 设计确认与反馈收集

安全特性:
动态访问密码(每次生成唯一链接)
手写签名确认区
交互功能:
多版本设计对比查看器
圈注批注工具
修改要求分级提交(紧急/普通)
跨部门协作看板(DASHBOARD):

核心组件:
设计-生产时效热力图
部门交接时效计数器
物流异常事件时间轴
数据联动:
点击设计稿直接跳转原始订单
物流单号实时查询嵌入
关系网络: 订单设计中心 → (通过自动化) → 触发生产流程 生产追踪系统 ← (数据镜像) → 供应商端口 客户门户 ↔ (双向同步) → 订单设计中心 协作看板 ⬅ (聚合数据) ⬅ 所有数据库

这个方案特点:

设计版本追溯机制确保修改可回溯
双重确认流程避免交接失误
物流数据API直连提升信息准确性
动态客户门户保障商业机密安全
时效可视化帮助优化业务流程
是否需要针对某个环节做更详细的字段设计或流程说明？`,
      },
      {},
    );
    const { textStream, text: textPromise } = result;
    for await (const chunk of textStream) {
      console.log('chunk', chunk);
    }
    const text = await textPromise;
    const data = JSON.parse(text);
    console.log('data--------', JSON.stringify(data));
    const { data: automations, error, success } = AutomationSchema.array().safeParse(data);
    if (!success) {
      console.error('error', error);
    }
    expect(success).toBe(true);
    expect(automations?.length).toBeGreaterThan(0);
    console.log('automations', automations);
  });
});
