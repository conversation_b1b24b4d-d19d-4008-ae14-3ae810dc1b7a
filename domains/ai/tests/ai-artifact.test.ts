import { generateNanoID } from 'basenext/utils/nano-id';
import { sleep } from 'sharelib/sleep';
import { test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AIArtifactSO } from '../../ai-artifacts/ai-artifact-so';

const TEST_LONG_AI = process.env.TEST_LONG_AI && process.env.TEST_LONG_AI === 'true';

/**
 * AI 生成CustomTemplate！
 */
test('Test Artifact Resumable Stream', { timeout: 5 * 60 * 1000 }, async () => {
  if (!TEST_LONG_AI) return;
  const { user, space, rootFolder } = await MockContext.initUserContext();
  const artifact1 = await AIArtifactSO.create(user, {
    type: 'text',
    prompt: {
      system:
        'You are 鲁迅, a famous Chinese writer and thinker. You are known for your sharp wit and insightful observations about society.',
      prompt: ' 写一段超短的，关于三国志 赵云 的故事，要求有趣且富有想象力。',
    },
    toolCallId: generateNanoID('test_artifact_'),
  });

  const artifact2 = await AIArtifactSO.getById(artifact1.id);
  const artifact3 = await AIArtifactSO.getById(artifact1.id);

  const [vo1, vo2, vo3] = await Promise.all([
    artifact1.getValue(user),
    artifact2.getValue(user),
    (async () => {
      await sleep(1000);
      return artifact3.getValue(user);
    })(),
  ]);

  expect(vo2).toStrictEqual(vo3);
  expect(vo1).toStrictEqual(vo2);

  // 完成后，再来
  const artifact4 = await AIArtifactSO.getById(artifact1.id);
  const vo4 = await artifact4.getValue(user);
  expect(vo4).toStrictEqual(vo3);
});
