import { expect, test } from 'vitest';
import z from 'zod';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AIAppsBuilderSO } from '../../ai-intents/ai-consulting/ai-apps-builder-so';
import { AISO } from '../server';

const ENABLE_AI_JSON_SCHEMA = false;

test('OpenAI JSON output strict Test', async () => {
  if (ENABLE_AI_JSON_SCHEMA) {
    const user = await MockContext.createUser();
    const schema = z.object({
      message: z.string(),
      test: z.literal('test'),
    });
    const res = await AISO.invokeByOpenAI('Please say anything', {
      model: 'openai/gpt-4o-mini',
      json: schema,
    });
    const jb = schema.parse(JSON.parse(res));
    expect(jb.test).toBe('test');

    const stream = await AISO.dangerStreamYield(
      { prompt: 'Please say anything' },
      {
        model: 'openai/gpt-4o-mini',
        json: schema,
      },
    );

    let jsonStr2 = '';
    for await (const chunk of stream) {
      jsonStr2 = chunk.content;
    }
    const json2 = schema.parse(JSON.parse(jsonStr2));
    expect(json2.test).toBe('test');
  }
});

const TEST_AI_BUILD_APP = false;

test('OpenAI Build App Test', async () => {
  if (TEST_AI_BUILD_APP) {
    const result = await AIAppsBuilderSO.build('Please build an app, crm system', undefined, undefined, () => {
      // console.log(chunk);
    });
    expect(result.aiApp).toBeDefined();
    console.log(result.aiApp);
  }
});
