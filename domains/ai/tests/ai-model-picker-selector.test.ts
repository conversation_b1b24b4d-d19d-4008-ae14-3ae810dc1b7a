import { expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AIModelPicker } from '../server/ai-model-picker';

test('test ai model picker', async () => {
  const { user, rootFolder, space } = await MockContext.initUserContext();

  const sdkModel2 = await AIModelPicker.selectLanguageModel({
    kind: 'auto',
  });

  expect(sdkModel2.modelId).toSatisfy(
    (v: string) => v.includes(AIModelPicker.getSystemAIModel()) || v.includes('mock-model-id'),
  );

  const m = 'openai/gpt-4.1';
  const sdkModel = await AIModelPicker.selectLanguageModel({
    kind: 'preset',
    model: m,
  });
  // expect(sdkModel.modelId).includes('gpt-3.5');
  expect(sdkModel.modelId).toSatisfy((v: string) => v.includes(m) || v.includes('mock-model-id'));

  const sdkModel3 = await AIModelPicker.selectLanguageModel({
    kind: 'custom',
    custom: {
      type: 'manual',
      provider: {
        type: 'OPENAI',
        apiKey: 'your-api-key',
        baseUrl: 'https://api.openai.com/v1',
        organizationId: 'your-organization-id',
      },
      modelId: 'gpt-4-custom',
    },
  });
  expect(sdkModel3.modelId).includes('gpt-4-custom');

  const sdkModel4 = await AIModelPicker.selectLanguageModel({
    kind: 'custom',
    custom: {
      type: 'manual',
      provider: {
        type: 'AMAZON_BEDROCK',
        apiKey: 'your-api-key',
        baseUrl: 'https://api.openai.com/v1',
        organizationId: 'your-organization-id',
        secretAccessKey: 'test',
      },
      modelId: 'amazon-bedrock-model',
    },
  });
  expect(sdkModel4.modelId).includes('amazon-bedrock-model');

  // const sdkModel5 = await AIModelPicker.selectSDKModel({
  //   kind: 'custom',
  //   custom: {
  //     type: 'manual',
  //     provider: {
  //       type: 'AMAZON_BEDROCK',
  //       apiKey: 'your-api-key',
  //       baseUrl: 'https://api.openai.com/v1',
  //       organizationId: 'your-organization-id',
  //       secretAccessKey: 'test',
  //     },
  //     modelId: 'amazon-bedrock-model',
  //   },
  // });
  // expect(sdkModel4.modelId).includes('amazon-bedrock-model');
});
