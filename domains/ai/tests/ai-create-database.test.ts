import assert from 'assert';
import { createOpenAI } from '@ai-sdk/openai';
import { streamObject } from 'ai';
import { describe, expect, test } from 'vitest';
import { z } from 'zod';
import { getFieldTypesConfig } from '@bika/contents/config/client/database/fields';
import { PresetLanguageModelServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import {
  DatabaseArrayCreatePrompt,
  DatabaseCreatePrompt,
  SimpleDatabaseSchema,
} from '@bika/contents/config/server/ai-prompts/ai-consulting/ai-object-database.prompt';
import { getServerLocaleContext } from '@bika/contents/i18n/server';
import { MockContext } from '@bika/domains/__tests__/mock';
import { Database, DatabaseSchema } from '@bika/types/database/bo';
import { AISO } from '../server/ai-so';

/**
 * AI Model Generation Parameters Guide
 *
 * temperature: 控制生成结果的随机性和创造性
 * - 值范围: 0.0 - 2.0 (大多数API默认为1.0)
 * - 低温度 (0.0-0.3): 输出更确定、可预测、一致，适合:
 *   · 结构化数据生成 (JSON, 数据库模式等)
 *   · 需要精确答案的场景 (数学、编程)
 *   · 遵循严格规则的任务
 *
 * - 中等温度 (0.3-0.7): 平衡确定性和创造性，适合:
 *   · 一般问答和描述
 *   · 业务内容生成
 *   · 大多数标准用例
 *
 * - 高温度 (0.7-2.0): 输出更多样、创意、有时不可预测，适合:
 *   · 创意写作和头脑风暴
 *   · 角色扮演和对话
 *   · 需要独特和多样化回答的场景
 *
 * 数据库结构生成通常建议使用0.2-0.3的低温度，确保生成的结构符合预期格式
 */

const TEST_AI = process.env.TEST_AI && process.env.TEST_AI === 'true';

describe('AI Create Database', async () => {
  const getAllFieldTypes = () => {
    const localeContext = getServerLocaleContext('en');
    const fields = getFieldTypesConfig(localeContext);
    const fieldTypes: string[] = [];
    for (const [key, value] of Object.entries(fields)) {
      if (value.display === 'SHOW') {
        fieldTypes.push(key);
      }
    }
    return fieldTypes;
  };
  test('Test AI Create Database--OpenAI--gpt-4o', async () => {
    if (!TEST_AI) {
      return;
    }
    const model = PresetLanguageModelServerConfig['openai/gpt-4o']!;
    assert(model.type === 'OPENAI', 'Only OPENAI model is supported for this test');
    const openai = createOpenAI({
      apiKey: model.apiKey,
      baseURL: model.baseUrl,
    });
    const aiModel = openai(model.modelId);

    // 使用更简短的提示
    const shortPrompt = `
客户订单表

首先，你需要在多维表格中新建一张数据表，它的作用是管理从电商平台上来的订单信息，包括订单号、平台、金额等，还能管理订单的处理状态。

总的来说，这张表格中最少需要包含以下的字段信息：

- 订单号：文本字段，记录订单编号信息。
- 电商平台：文本字段，记录订单来自哪一个平台。
- 金额：货币字段，记录订单的金额。
- 客户需要定制的产品类型：单选字段，将现有的产品品类进行选项标签化。内容人员可以手动进行订单种类的选择。
- 订单状态：单选字段，记录目前订单的处理状态，便于进度管理。
- 创建时间：日期字段，记录订单创建的时间。
- 格式化内容：公式字段，将【客户需要定制的产品类型】字段的选项进行内容格式化，便于后面自动化流程的信息读取与执行。
    `;

    const { partialObjectStream } = streamObject({
      model: aiModel,
      schema: SimpleDatabaseSchema,
      messages: [
        {
          role: 'system',
          content: DatabaseCreatePrompt,
        },
        {
          role: 'user',
          content: shortPrompt,
        },
      ],
      onError(event) {
        console.error('Error:', event);
      },
    });
    const partialObjects = [];
    for await (const partialObject of partialObjectStream) {
      partialObjects.push(partialObject);
    }
    const finalObject = partialObjects[partialObjects.length - 1];
    console.log('finalObject', JSON.stringify(finalObject, null, 2));
    const { data, success } = DatabaseSchema.safeParse(finalObject);
    expect(success).toBe(true);
    expect(data?.name).toBe('客户订单表');
    expect(data?.databaseType).toBe('DATUM');
    expect(data?.fields?.length).toBe(7);
  }, 100000);

  test('Test AI Create Database--deepseek-v3', async () => {
    if (!TEST_AI) {
      return;
    }
    const model = PresetLanguageModelServerConfig['deepseek/deepseek-v3']!;
    assert(model.type === 'DEEPSEEK', 'Only DEEPSEEK model is supported for this test');
    const openai = createOpenAI({
      apiKey: model.apiKey,
      baseURL: model.baseUrl,
    });
    const aiModel = openai(model.modelId);

    // 使用更简短的提示
    const shortPrompt = `
客户订单表

首先，你需要在多维表格中新建一张数据表，它的作用是管理从电商平台上来的订单信息，包括订单号、平台、金额等，还能管理订单的处理状态。

总的来说，这张表格中最少需要包含以下的字段信息：

- 订单号：文本字段，记录订单编号信息。
- 电商平台：文本字段，记录订单来自哪一个平台。
- 金额：货币字段，记录订单的金额。
- 客户需要定制的产品类型：单选字段，将现有的产品品类进行选项标签化。内容人员可以手动进行订单种类的选择。
- 订单状态：单选字段，记录目前订单的处理状态，便于进度管理。
- 创建时间：日期字段，记录订单创建的时间。
- 格式化内容：公式字段，将【客户需要定制的产品类型】字段的选项进行内容格式化，便于后面自动化流程的信息读取与执行。
    `;

    const { partialObjectStream } = streamObject({
      model: aiModel,
      schema: SimpleDatabaseSchema,
      messages: [
        {
          role: 'system',
          content: DatabaseCreatePrompt,
        },
        {
          role: 'user',
          content: shortPrompt,
        },
        {
          role: 'user',
          content: 'use locale zh-CN',
        },
      ],
      onError(event) {
        console.error('Error:', event);
      },
    });
    const partialObjects = [];
    for await (const partialObject of partialObjectStream) {
      partialObjects.push(partialObject);
    }
    const finalObject = partialObjects[partialObjects.length - 1];
    console.log('finalObject', JSON.stringify(finalObject, null, 2));
    const { data, success } = DatabaseSchema.safeParse(finalObject);
    expect(success).toBe(true);
    expect(data?.name).toBe('客户订单表');
    expect(data?.databaseType).toBe('DATUM');
    expect(data?.fields?.length).toBe(7);
  }, 100000);

  test('Test AI Create Database--all-fields-deepseek-v3', async () => {
    if (!TEST_AI) {
      return;
    }
    const model = PresetLanguageModelServerConfig['deepseek/deepseek-v3']!;
    assert(model.type === 'DEEPSEEK', 'Only DEEPSEEK model is supported for this test');
    const openai = createOpenAI({
      apiKey: model.apiKey,
      baseURL: model.baseUrl,
    });
    const aiModel = openai(model.modelId);
    const fieldTypes = getAllFieldTypes();
    // 使用更简短的提示
    const shortPrompt = `
全字段表
这张表格中需要包含所有字段类型: ${fieldTypes.join(', ')}，其中，首列是单行文本字段
    `;

    const { partialObjectStream } = streamObject({
      model: aiModel,
      schema: SimpleDatabaseSchema,
      messages: [
        {
          role: 'system',
          content: DatabaseCreatePrompt,
        },
        {
          role: 'user',
          content: shortPrompt,
        },
        {
          role: 'user',
          content: 'use locale zh-CN',
        },
      ],
      onError(event) {
        console.error('Error:', event);
      },
    });
    const partialObjects = [];
    for await (const partialObject of partialObjectStream) {
      partialObjects.push(partialObject);
    }
    const finalObject = partialObjects[partialObjects.length - 1];
    console.log('finalObject', JSON.stringify(finalObject, null, 2));
    const { data, success } = DatabaseSchema.safeParse(finalObject);
    expect(success).toBe(true);
    expect(data?.name).toBe('全字段表');
    expect(data?.databaseType).toBe('DATUM');
    expect(data?.fields?.length).toBe(fieldTypes.length);
  }, 100000);

  test('Test AI Create Database--two-tables-deepseek-v3', async () => {
    if (!TEST_AI) {
      return;
    }
    const model = PresetLanguageModelServerConfig['deepseek/deepseek-v3']!;
    assert(model.type === 'DEEPSEEK', 'Only DEEPSEEK model is supported for this test');
    const openai = createOpenAI({
      apiKey: model.apiKey,
      baseURL: model.baseUrl,
    });
    const aiModel = openai(model.modelId);

    const finalData: Database[] = [];

    const result = streamObject({
      model: aiModel,
      schema: SimpleDatabaseSchema,
      output: 'array',
      temperature: 0.2,
      messages: [
        {
          role: 'system',
          content: DatabaseCreatePrompt,
        },
        {
          role: 'user',
          content: `
多维表格-客户订单表

首先，你需要在多维表格中新建一张数据表，它的作用是管理从电商平台上来的订单信息，包括订单号、平台、金额等，还能管理订单的处理状态。

总的来说，这张表格中最少需要包含以下的字段信息：

- 订单号：文本字段，记录订单编号信息。
- 电商平台：文本字段，记录订单来自哪一个平台。
- 金额：货币字段，记录订单的金额。
- 订单类型：单选字段，记录订单的类型，便于分类管理
- 订单状态：单选字段，记录目前订单的处理状态，便于进度管理
- 订单创建时间：日期字段，记录订单创建的时间。
- 订单任务：关联字段， 与【多维表格-订单任务表】的【关联的订单号】字段关联。
          
多维表格-订单任务表

接着，我们继续创建第2张多维表格，这张表格的作用是管理订单的具体生产任务以及后续的物流信息。

这张表格中最少需要包含以下的字段信息：

- SKU编号：文本字段，记录需要生产的SKU编号，便于识别与追溯。
- 关联的订单号：关联字段， 与【多维表格-客户订单表】的【订单号】字段关联。
- 供应商：成员字段，进行供应商负责人的任务分配。
- 任务状态：单选字段，记录当前制作任务的状态，便于进度管理
- 生产时间：日期字段，记录生产任务开始时间。
- 物流单号：文本字段，记录订单生产任务完成后发货的物流单号。
- 物流平台：文本字段，记录发货物流的平台信息。 `,
        },
        {
          role: 'user',
          content: 'use locale zh-CN',
        },
      ],
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      onFinish(data: any) {
        console.log('onFinish data:', JSON.stringify(data, null, 2));
        if (data.object && Array.isArray(data.object)) {
          finalData.push(...data.object);
        }
      },
    });
    const { usage: usagePromise, elementStream } = result;
    for await (const object of elementStream) {
      console.log('object:-----', JSON.stringify(object, null, 2));
    }
    // 注意: 这里的elementStream是流式返回的，所以需要等待所有流式数据返回后，再进行数据验证
    // 用onFinish方法获取数据，可以确保所有流式数据返回后，再进行数据验证,
    // 在返回output: 'array'的情况下，onFinish方法中的data和elementStream中的数据不一致，elementStream中有些字段的property缺失了数据
    const usage = await usagePromise;

    console.log('usage:', JSON.stringify(usage, null, 2));
    console.log('stream elements:', JSON.stringify(finalData, null, 2));

    // 验证两种方式获取的数据是否一致

    const { data, success, error } = DatabaseSchema.array().safeParse(finalData);
    if (!success) {
      console.error('Schema validation error:', error?.message);
    }
    expect(success).toBe(true);
    const { rootFolder, user } = await MockContext.initUserContext();
    console.log('user_email:', user.email);
    await rootFolder.createChildren(user, data!);
    const children = await rootFolder.getChildren(true);
    expect(children.length).toBe(2);
  }, 100000);

  test('Test AI Create Databases from a prompt--no-schema', async () => {
    if (!TEST_AI) {
      return;
    }
    const { rootFolder, user } = await MockContext.initUserContext();
    const result = await AISO.streamObject(
      {
        user,
        schema: z.object({}),
        system: DatabaseArrayCreatePrompt,
        prompt: `
Resources:

订单设计中心(DATABASE): 集中管理客户订单与设计流程

字段:
订单编号: 自动编号 - 唯一订单标识
客户需求文档: 附件 - 客户提供的设计参考文件
设计稿版本: 版本控制 - 存储不同设计迭代版本
设计师分配: 人员选择 - 指定专属设计师
确认状态: 单选(待确认/已确认/需修改)
关系:
设计确认流程: 触发自动化
生产追踪系统: 数据同步
设计确认流程(AUTOMATION): 自动化设计确认与部门流转

触发条件: 设计状态变更为"客户确认"
执行动作:
生成带水印确认书(PDF自动生成)
通知生产部门(企业微信/邮件通知)
创建供应商任务单
审批流程: 部门主管二次确认机制
异常处理: 超时未确认自动提醒
生产追踪系统(DATABASE): 监控生产进度与物流信息

核心字段:
生产阶段: 进度条(原料采购/生产中/质检完成)
物流对接人: 外联人员信息
物流单号: 扫码录入(支持主流快递公司)
实时位置: API集成物流追踪
视图配置:
延误预警视图(预计交付时间<3天)
供应商绩效看板
客户门户(FORM): 设计确认与反馈收集

安全特性:
动态访问密码(每次生成唯一链接)
手写签名确认区
交互功能:
多版本设计对比查看器
圈注批注工具
修改要求分级提交(紧急/普通)
跨部门协作看板(DASHBOARD):

核心组件:
设计-生产时效热力图
部门交接时效计数器
物流异常事件时间轴
数据联动:
点击设计稿直接跳转原始订单
物流单号实时查询嵌入
关系网络: 订单设计中心 → (通过自动化) → 触发生产流程 生产追踪系统 ← (数据镜像) → 供应商端口 客户门户 ↔ (双向同步) → 订单设计中心 协作看板 ⬅ (聚合数据) ⬅ 所有数据库

这个方案特点:

设计版本追溯机制确保修改可回溯
双重确认流程避免交接失误
物流数据API直连提升信息准确性
动态客户门户保障商业机密安全
时效可视化帮助优化业务流程
是否需要针对某个环节做更详细的字段设计或流程说明？`,
      },
      {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onFinish(data: any) {
          console.log('onFinish data:', JSON.stringify(data, null, 2));
          if (data.object && Array.isArray(data.object)) {
            finalData.push(...data.object);
          }
        },
        temperature: 0.2,
      },
    );
    // let artifactData: PartialObject<{ html: string }> | undefined;

    // const vo: ArtifactVO = {
    //   type: 'html',
    //   id: this._artifactSO.id,
    //   data: {
    //     html: '',
    //   },
    // };

    for await (const _delta of result.fullStream) {
      // const { type } = delta;
      // console.warn('delta type', type, delta);
      // if (type === 'object') {
      // }
    }
    const finalData = (await result.object) as Database[];
    // const { usage: usagePromise } = result;
    // const usage = await usagePromise;

    // console.log('usage:', JSON.stringify(usage, null, 2));
    console.log('stream elements:', JSON.stringify(finalData, null, 2));

    const { data, success, error } = DatabaseSchema.array().safeParse(finalData);
    if (!success) {
      console.error('Schema validation error:', error?.message);
    }
    expect(success).toBe(true);
    console.log('user_email:', user.email);
    await rootFolder.createChildren(user, data!);
    const children = await rootFolder.getChildren(true);
    expect(children.length).toBe(2);
  });
});
