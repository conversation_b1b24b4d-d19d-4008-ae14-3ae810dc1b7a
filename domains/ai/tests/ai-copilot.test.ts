import assert from 'assert';
import { createDataStreamResponse } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { test, expect, describe, it, vi } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AIMessageVO } from '@bika/types/ai/vo';
import { UserSO } from '../../user/server/user-so';
import { AIChatSO } from '../server/ai-chat/ai-chat-so';
import { AISO } from '../server/ai-so';

const readResponse = async (response: Response) => {
  const decoder = new TextDecoder();
  const reader = response.body?.getReader();
  while (true && reader) {
    const { done, value } = await reader.read();
    if (done) {
      break;
    }
    const text = decoder.decode(value);
    // console.log('text=======', text);
  }
};

const mockCopilotChat = async (user: UserSO, nodeId: string, memberId?: string) => {
  const wizardSO = memberId
    ? await AISO.newWizardByMember(memberId, {
        type: 'COPILOT',
        copilot: {
          type: 'node',
          nodeId,
        },
      })
    : await AISO.newWizardByUser(user.id, {
        type: 'COPILOT',
        copilot: {
          type: 'node',
          nodeId,
        },
      });
  return wizardSO;
};

describe('Automation AI Copilot', () => {
  it('automation explanation', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const automation = children.find((child) => child.type === 'AUTOMATION')!;
    const wizard = await mockCopilotChat(user, automation.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(
          ctx,
          {
            type: 'MESSAGE',
            message: 'how does this automation work?',
          },
          'en',
          dataStream,
        );
        message = result.message;
      },
      onError: (error) => {
        console.error('error', error);
        return 'error';
      },
    });
    await readResponse(response);
    console.log('message=======', JSON.stringify(message, null, 2));

    // Check that the parts array contains tool invocations with the expected tool names
    const toolInvocations = message?.parts.filter((part) => part.type === 'tool-invocation') || [];
    const toolNames = toolInvocations.map((part) =>
      part.type === 'tool-invocation' ? part.toolInvocation.toolName : null,
    );

    expect(toolNames).toContain('get_automation_detail');
  }, 100000);

  it('Test AI Copilot Automation Ask', async () => {
    //
    const { user, space, rootFolder } = await MockContext.initUserContext();

    // Explain, database node
    // const wizardSO = await AISO.newWizardByUser(user.id, {
    //   type: 'COPILOT',
    //   mode: 'ASK',
    //   nodeId: xxxx
    // });

    // expect() 当用户输入 explain，确保调用了两个工具

    // expect(steps.length).toBe(3); // 两个工具后，得出结果

    expect(user).toBeDefined();
  });

  it('Test AI Copilot Automation Edit', async () => {
    //
    const { user, space, rootFolder } = await MockContext.initUserContext();

    // Explain, database node
    // const wizardSO = await AISO.newWizardByUser(user.id, {
    //   type: 'COPILOT',
    //   mode: 'EDIT',
    //   nodeId: xxxx
    // });

    // expect() 当用户输入 explain，确保调用了两个工具

    // expect(steps.length).toBe(3); // 两个工具后，得出结果

    expect(user).toBeDefined();
  });
});

describe('Database AI Copilot', () => {
  // 基于模型 qwen-turbo，验证 tool 的结果是否符合预期
  it('call get_database_detail tool', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const database = children.find((child) => child.type === 'DATABASE')!;
    const wizard = await mockCopilotChat(user, database.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(
          ctx,
          { type: 'MESSAGE', message: 'tell me some detail info about the database' },
          'en',
          dataStream,
        );
        message = result.message;
      },
    });
    await readResponse(response);
    console.log('message=======', JSON.stringify(message, null, 2));
    expect(message?.parts.length).toBe(2);
    expect(message?.parts[1].type).toBe('text');
    expect(message?.parts[0].type === 'tool-invocation' && message?.parts[0].toolInvocation.toolName).toBe(
      'get_database_detail',
    );
  });

  // 基于模型 qwen-turbo，验证 tool 的结果是否符合预期
  it('call get_fields_schema tool', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const database = children.find((child) => child.type === 'DATABASE')!;
    const wizard = await mockCopilotChat(user, database.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(
          ctx,
          {
            type: 'MESSAGE',
            message: 'tell me the fields schema of this database',
          },
          'en',
          dataStream,
        );
        message = result.message;
      },
    });
    await readResponse(response);
    console.log('message=======', JSON.stringify(message, null, 2));
    // ask user to provide params
    expect(message?.parts.length).toBe(2);
    expect(message?.parts[0].type === 'tool-invocation' && message?.parts[0].toolInvocation.toolName).toBe(
      'get_fields_schema',
    );
    expect(message?.parts[1].type).toBe('text');
  }, 1000000);

  /**
   * 单测 - 创建新记录
   * qwen-turbo ... fail
   * qwen-plus ... pass
   * deepseek-v3 ... fail
   * doubao-pro-32k ... pass
   * */
  it('call create_records tool', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const database = children.find((child) => child.type === 'DATABASE')!;
    const wizard = await mockCopilotChat(user, database.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(
          ctx,
          {
            type: 'MESSAGE',
            option: 'edit',
            message: 'create a new record for adding a new task "finish the UI design in 2 days"',
          },
          'en',
          dataStream,
        );
        message = result.message;
      },
    });
    await readResponse(response);
    console.log('message=======', JSON.stringify(message, null, 2));

    // Check that the parts array contains tool invocations with the expected tool names
    const toolInvocations = message?.parts.filter((part) => part.type === 'tool-invocation') || [];
    const toolNames = toolInvocations.map((part) =>
      part.type === 'tool-invocation' ? part.toolInvocation.toolName : null,
    );

    expect(toolNames).toContain('get_fields_schema');
    expect(toolNames).toContain('create_record');
  }, 1000000);

  // 基于模型 qwen-turbo，验证 tool 的结果是否符合预期
  it('call list_records tool', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const database = children.find((child) => child.type === 'DATABASE')!;
    const wizard = await mockCopilotChat(user, database.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(
          ctx,
          {
            type: 'MESSAGE',
            message: 'get the records which status is "Pending"',
          },
          'en',
          dataStream,
        );
        message = result.message;
      },
    });
    await readResponse(response);
    console.log('message=======', JSON.stringify(message, null, 2));
    expect(message?.parts[0].type === 'tool-invocation' && message?.parts[0].toolInvocation.toolName).toBe(
      'get_database_detail',
    );
    expect(message?.parts[1].type === 'tool-invocation' && message?.parts[1].toolInvocation.toolName).toBe(
      'list_records',
    );
    expect(message?.parts[2].type).toBe('text');
  }, 60000);

  it('search_records--with-init-params', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const automation = children.find((child) => child.type === 'DATABASE')!;
    const wizard = await mockCopilotChat(user, automation.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(
          ctx,
          { type: 'MESSAGE', message: 'get the records in pending status' },
          'en',
          dataStream,
        );
        message = result.message;
      },
    });
    await readResponse(response);

    expect(message?.parts.length).toBe(5);
    expect(message?.parts[2].type).toBe('text');
    expect(message?.parts[1].type === 'tool-invocation' && message?.parts[1].toolInvocation.toolName).toBe(
      'get_database_detail',
    );
    expect(message?.parts[3].type === 'tool-invocation' && message?.parts[3].toolInvocation.toolName).toBe(
      'list_records',
    );
  }, 1000000);

  it('search_records--with history', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const automation = children.find((child) => child.type === 'DATABASE')!;
    const wizard = await mockCopilotChat(user, automation.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;

    await readResponse(
      createDataStreamResponse({
        headers: {
          'Content-Type': 'text/event-stream',
        },
        execute: async (dataStream) => {
          // get_database_detail tool
          await wizard.resolve(
            ctx,
            {
              type: 'MESSAGE',
              message: 'Explain this resource',
            },
            'en',
            dataStream,
          );
        },
      }),
    );

    await readResponse(
      createDataStreamResponse({
        headers: {
          'Content-Type': 'text/event-stream',
        },
        execute: async (dataStream) => {
          // get_database_records tool
          await wizard.resolve(
            ctx,
            { type: 'MESSAGE', message: 'get the records in pending status' },
            'en',
            dataStream,
          );
        },
      }),
    );

    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        // not use tool
        const result = await wizard.resolve(
          ctx,
          {
            type: 'MESSAGE',
            message: `get the records in 'In Progress' status`,
          },
          'en',
          dataStream,
        );
        message = result.message;
      },
    });
    await readResponse(response);

    expect(message?.parts.length).toBe(1);
  }, 1000000);

  it('search_records--with member-info', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'okr-tracker-quarterly-report-automation');
    const children = await folder.getChildren();
    const database = children.find((child) => child.type === 'DATABASE')!;
    const wizard = await mockCopilotChat(user, database.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(ctx, { type: 'MESSAGE', message: '我创建的记录' }, 'zh-CN', dataStream);
        message = result.message;
      },
    });
    await readResponse(response);
    console.log('message=======', JSON.stringify(message, null, 2));
    expect(message?.parts.length).toBe(2);
    expect(message?.parts[1].type).toBe('text');
    assert(message?.parts[0].type === 'tool-invocation');
  });
});

describe('Test Form AI Copilot', () => {
  it('explain fields redirect in form should use get_node_info', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'beginner-playground');
    const children = await folder.getChildren();
    const automation = children.find((child) => child.type === 'FORM')!;
    const wizard = await mockCopilotChat(user, automation.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await wizard.resolve(
          ctx,
          { type: 'MESSAGE', message: 'include which fields' },
          'zh-CN',
          dataStream,
        );
        message = result.message;
      },
      onError: (error) => {
        console.error('error', error);
        return 'error';
      },
    });
    await readResponse(response);
    expect(message?.parts.length).toBe(2);
    expect(message?.parts[1].type).toBe('text');
    assert(message?.parts[0].type === 'tool-invocation');
    expect(message?.parts[0].type === 'tool-invocation' && message?.parts[0].toolInvocation.toolName).toBe(
      'get_node_info',
    );
  });
});

describe('Test copilot credit', () => {
  test('copilot credit--gpt-4.1--parse usage', async () => {
    // 34 160
    const usage = await AISO.parseAICreditCost('openai/gpt-4.1', {
      promptTokens: 4000,
      completionTokens: 5000,
      totalTokens: 9000,
    });
    const costCredit = Math.round((34 / 1000) * 4000 + (160 / 1000) * 5000);
    expect(usage.costCredit).toBe(costCredit);
  });

  test('test copilot cost credit-user--gpt-4.1', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'cost-credit-database',
    });
    const copilotChat = await mockCopilotChat(user, node.id);
    const credit = await user.coins.getAccount();
    const { message, usage } = await AISO.streamChat(
      {
        messages: [
          {
            id: 'for_test',
            role: 'assistant',
            content: 'test',
            parts: [
              {
                type: 'text',
                text: 'test',
              },
            ],
          },
        ],
        system: 'you are a copilot',
        user,
      },
      {
        model: 'openai/gpt-4.1',
        onFinish: (_data) => {},
        onChunk: (_chunk) => {},
        onError: (error) => {
          console.log('ai-agent-call', error);
        },
      },
      {},
    );
    // 减去了user 的credit
    await copilotChat.costAICredit(copilotChat.id, message.id || generateNanoID('msg'), [usage], user.id);
    const newCredit = await user.coins.getAccount();
    // there credit == balance
    expect(newCredit.balance).toBeLessThan(credit.balance);
    expect(newCredit.credit).toBeLessThan(credit.credit);
    expect(newCredit.credit).toBe(newCredit.balance);
  });

  test('copilot credit--member--gpt-4.1--cost credit', async () => {
    const { user, space, member, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'cost-credit-database',
    });
    const copilotChat = await mockCopilotChat(user, node.id, member.id);
    // 40 160
    const usage = await AISO.parseAICreditCost('openai/gpt-4.1', {
      promptTokens: 100,
      completionTokens: 100,
      totalTokens: 200,
    });
    const costCredit = Math.round((34 / 1000) * 100 + (160 / 1000) * 100);
    expect(usage.costCredit).toBe(costCredit);
    await copilotChat.costAICredit(copilotChat.id, generateNanoID('msg'), [usage], user.id);
    const coinsAccount = await space.billing.getCoinsAccount();
    const virtualCredit = await coinsAccount.virtualCredit();

    const virtualBalance = await coinsAccount.virtualBalance();
    // 因为是免费的空间站，目前balance是0, 当个人充值到空间站的时候就是100, 因为会加上credit=100(新用户注册奖励)
    // balance 其实没有被消耗，消耗的的virtualCredit，这里的balance是0，只是记录transaction
    expect(coinsAccount.balance).toBe(BigInt(0));
    expect(coinsAccount.credit).toBe(BigInt(0));
    // 用户实际看到的余额, 消费的是赠送的金额 这里消耗了
    expect(virtualCredit).toBe(BigInt(1000 + 3000 - usage.costCredit));
    expect(virtualBalance).toBe(virtualCredit + coinsAccount.balance);
  });

  test('copilot credit--member--gpt-4.1--credit-check--enough', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'cost-credit-database',
    });
    const copilotChat = await mockCopilotChat(user, node.id, member.id);
    // 1, 2
    const usage = await AISO.parseAICreditCost('qwen/qwen-turbo', {
      promptTokens: 1000,
      completionTokens: 1000,
      totalTokens: 2000,
    });
    // 960 credit
    await copilotChat.costAICredit(copilotChat.id, generateNanoID('msg'), [usage], user.id);
    // expect no exception
    await expect(copilotChat.checkAICredit()).resolves.not.toThrow();
  });

  test('copilot credit--member--gpt-4.1--credit-check--not-enough', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'cost-credit-database',
    });
    const copilotChat = await mockCopilotChat(user, node.id, member.id);
    const usage = await AISO.parseAICreditCost('openai/gpt-4.1', {
      promptTokens: 40000,
      completionTokens: 50000,
      totalTokens: 90000,
    });
    // 9600 credit
    await copilotChat.costAICredit(copilotChat.id, generateNanoID('msg'), [usage], user.id);
    // expect exception
    await expect(copilotChat.checkAICredit()).rejects.toThrow();
  });

  test('copilot credit--member--ai-page--with-artifact', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'PAGE',
      name: 'cost-credit-ai-page',
    });
    const copilotChat: AIChatSO = await mockCopilotChat(user, node.id, member.id);
    vi.spyOn(copilotChat.intent, 'resolveYield').mockResolvedValueOnce({
      resolution: {
        status: 'SUCCESS',
        intentParam: {
          type: 'COPILOT',
          copilot: {
            type: 'node',
            nodeId: node.id,
          },
        },
        usage: {
          model: 'openai/gpt-4.1',
          costCredit: Math.round((34 / 1000) * 100 + (160 / 1000) * 100),
          promptTokens: 100,
          completionTokens: 100,
          totalTokens: 200,
        },
        message: {
          parts: [
            {
              type: 'text',
              text: 'test',
            },
            {
              type: 'tool-invocation',
              toolInvocation: {
                state: 'result',
                toolCallId: 'mock-tool-invocation-id',
                toolName: 'generate_page',
                args: {
                  prompt: 'generate a page about bika',
                  nodeId: node.id,
                  resourceType: 'PAGE',
                },
                result: {
                  artifactId: 'mock-artifact-id',
                },
              },
            },
          ],
        },
      },
      intent: copilotChat.intent,
    });
    vi.spyOn(copilotChat, 'getAIArtifactUsages').mockResolvedValueOnce([
      {
        promptTokens: 100,
        completionTokens: 100,
        totalTokens: 200,
        costCredit: Math.round((34 / 1000) * 100 + (160 / 1000) * 100),
        model: 'qwen/qwen-turbo',
      },
    ]);

    await copilotChat.resolve(
      MockContext.createMockRequestContext(user),
      {
        type: 'MESSAGE',
        message: 'generate a page about bika',
      },
      'en',
      undefined,
    );
    const messagePOs = await copilotChat.getMessagesPOs();
    const aiMessage = messagePOs[messagePOs.length - 1];
    const usages = aiMessage.usages!;
    expect(usages.length).toBe(2);
    expect(usages[0].model).toBe('openai/gpt-4.1');
    expect(usages[0].costCredit).toBe(Math.round((34 / 1000) * 100 + (160 / 1000) * 100));
    expect(usages[0].promptTokens).toBe(100);
    expect(usages[0].completionTokens).toBe(100);
    expect(usages[0].totalTokens).toBe(200);
    expect(usages[1].model).toBe('qwen/qwen-turbo');
    expect(usages[1].costCredit).toBe(Math.round((34 / 1000) * 100 + (160 / 1000) * 100));
    expect(usages[1].promptTokens).toBe(100);
    expect(usages[1].completionTokens).toBe(100);
    expect(usages[1].totalTokens).toBe(200);
  });
});

describe('Test PAGE AI Copilot', () => {
  test('create a html page', async () => {
    if (!MockContext.isTestAI) {
      return;
    }
    const { user, member, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'PAGE',
      name: 'cost-credit-ai-page',
    });
    const copilotChat: AIChatSO = await mockCopilotChat(user, node.id, member.id);
    const ctx = MockContext.createMockRequestContext(user);
    let message: AIMessageVO | undefined;
    const response = createDataStreamResponse({
      headers: {
        'Content-Type': 'text/event-stream',
      },
      execute: async (dataStream) => {
        const result = await copilotChat.resolve(
          ctx,
          {
            type: 'MESSAGE',
            message: 'create a hello world page',
          },
          'en',
          dataStream,
        );
        message = result.message;
      },
    });
    await readResponse(response);
    expect(message?.parts.length).toBe(2);
    expect(message?.parts[0].type).toBe('tool-invocation');
    assert(message?.parts[1].type === 'text');
  });
});
