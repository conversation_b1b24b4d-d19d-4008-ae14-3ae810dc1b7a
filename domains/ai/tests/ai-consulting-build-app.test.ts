/* eslint-disable no-constant-condition */
import assert from 'assert';
import { LanguageModelV1CallOptions, LanguageModelV1StreamPart } from '@ai-sdk/provider';
import { simulateReadableStream } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AIChatSO } from '@bika/domains/ai/server/ai-chat/ai-chat-so';
import { AIMockSO } from '@bika/domains/ai/server/ai-mock/ai-mock-so';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { AIMessageVO } from '@bika/types/ai/vo';
import { AIArtifactSO } from '../../ai-artifacts/ai-artifact-so';
import tools from '../../ai-skillset/bika-app-builder/server';
import { AINodeSO } from '../../node-resources/ai-agent/ai-node-so';
import { createResumableDataStream } from '../server/stream-context';

const TEST_LONG_AI = process.env.TEST_LONG_AI && process.env.TEST_LONG_AI === 'true';

/**
 * AI 生成CustomTemplate！
 */
test('Test AI Build Apps + with approvals + without default space id', { timeout: 5 * 60 * 1000 }, async () => {
  if (!TEST_LONG_AI) return;
  const { user, space, rootFolder } = await MockContext.initUserContext();
  const wizardSO = await AISO.newWizardByUser(user.id, {
    type: 'BUILDER',
    spaceId: undefined,
    //  单测专用选项，要求 planner 也需要审批， 使停顿在那里
    needApproval: {
      planner: true,
      engineer: true,
    },
  });

  // 这里是打招呼
  const prologue = await wizardSO.getLastAIMessage();
  expect(prologue).toBe(null);
  // const prologueText = prologue!.parts.find((part) => part.type === 'text')?.text;
  // expect(prologueText).toContain('Hi');

  const mockRequestContext = MockContext.createMockRequestContext(user);

  // 默认给了些prompt
  // 用户随便聊一个话题
  const { intent, message } = await wizardSO.message(mockRequestContext, '客户管理');

  // 太短了，提示太短
  expect(intent.resolutionStatus).toBe('DIALOG');
  console.log(message.text); // too simple

  // Prompts 返回
  const intentUI0 = message.prompts;
  expect(intentUI0!.length).toBeGreaterThan(2);
  // expect(intentUI0.type).toBe('PROMPT'); // 太短，继续聊天
  // console.log(intentUI0);

  // 由于没给spaceId，所以需要用户选择
  const { intent: intentCRM, message: msgCRM } = await wizardSO.message(
    mockRequestContext,
    'Build an AI Automation CRM for me',
    // '我想要一个客户管理系统!简单拜访记录管理就好!',
  );

  const intentUIPart = msgCRM.parts[msgCRM.parts.length - 1];

  assert(intentUIPart.type === 'tool-invocation');

  const intentUI = intentUIPart.toolInvocation;
  expect(intentUI.state).toBe('call');
  expect(intentUI.toolName).toBe('bika-ai-app-builder-space-id');

  // 手工 给这个 Tool space id 的结果，选择空间站, 增加 result
  const { message: spaceResolveMsg } = await wizardSO.resolve(mockRequestContext, {
    type: 'TOOL',
    toolInvocation: {
      state: 'result',
      // step: 0,
      toolCallId: intentUI.toolCallId,
      toolName: intentUI.toolName,
      args: intentUI.args,
      result: {
        spaceId: space.id,
      },
    },
    // type: 'UI',
    // uiResolve: {
    //   type: 'CHOICES',
    //   index: 0,
    // },
  });

  // expect(false).toBe(true);
  const messages = await wizardSO.getMessages();
  expect(messages.length).toBeGreaterThan(0);

  // expect(wizardSO.intentResolutionStatus).toBe('NEEDS_DISAMBIGUATION');
  expect(wizardSO.intent).toBeDefined();

  const intentParams = wizardSO.intent.toVO();
  assert(intentParams.type === 'BUILDER');
  expect(intentParams.spaceId).toBeUndefined();

  // expect(spaceResolve.resolutionStatus).toBe('NEEDS_CONFIRMATION');
  expect(spaceResolveMsg.parts).toBeDefined();

  const parts3 = await spaceResolveMsg.parts;
  // 就是 planner 执行完成，然后enginner 在 'tool-call'卡住了，因为 needApprovals
  // 分别是: tool-invocation:result(UI Form 选择), step-start(AI 开跑), tool-invocation:call(planner 开始 call),
  expect(parts3.length).toBe(4);
  const plannerCallTool = parts3[3];
  assert(plannerCallTool.type === 'tool-invocation');
  const plannerToolName = plannerCallTool.toolInvocation.toolName;
  const plannerToolArgs = plannerCallTool.toolInvocation.args;
  expect(plannerCallTool.toolInvocation.state).toBe('call');
  expect(plannerToolName).toBe('bika-ai-app-builder-planner');

  const plannerToolCallId = plannerCallTool.toolInvocation.toolCallId;
  expect(plannerToolCallId).toBeDefined();

  // 接着，
  // 1. 手工执行 planner 的 tool execute  result，
  expect(spaceResolveMsg.skillsets!.length).toBeGreaterThan(0); // 技能集不为空，在 AISO.doSaveAiResponse强行赋值了 skillsets
  // 返回 result 是{artifactId: 'xxx'}
  const toolResult = await AIChatSO.executeTool(
    { toolCallId: plannerToolCallId, chatId: wizardSO.id },
    // spaceResolveMsg.skillsets!,
    {
      user,
    },
  );

  // 2. 手工再走 ai sdk 的 addToolResult，手工给个运行planner的运行结果，模拟客户端
  const { message: plannerMsg } = await wizardSO.resolve(mockRequestContext, {
    type: 'TOOL',
    toolInvocation: {
      state: 'result',
      step: 0,
      toolCallId: plannerToolCallId,
      toolName: plannerToolName,
      args: plannerToolArgs, // z.custom<Required<any>>((x) => x !== undefined),
      result: toolResult, // z.cust
    },
  });

  // resultPlanner.parts 里，必定包含 state==result, toolName == bika-ai-app-builder-planner
  expect(
    plannerMsg.parts.find(
      (part) =>
        part.type === 'tool-invocation' &&
        part.toolInvocation.state === 'result' &&
        part.toolInvocation.toolName === plannerToolName,
    ),
  ).toBeDefined();

  // 并且必定包含 bika-ai-app-builder-engineer 的 call
  expect(
    plannerMsg.parts.find(
      (part) =>
        part.type === 'tool-invocation' &&
        part.toolInvocation.state === 'call' &&
        part.toolInvocation.toolName === 'bika-ai-app-builder-engineer',
    ),
  ).toBeDefined();

  // console.log()
  const engineerCallTool = plannerMsg.parts[plannerMsg.parts.length - 1];
  assert(engineerCallTool.type === 'tool-invocation');
  expect(engineerCallTool.toolInvocation.toolName).toBe('bika-ai-app-builder-engineer');
  expect(engineerCallTool.toolInvocation.state).toBe('call');
  expect((engineerCallTool.toolInvocation.args as any).templateId).toBeDefined();

  // 手工执行 engineer 的 call
  const engineerToolResult = await AIChatSO.executeTool(
    { toolCallId: engineerCallTool.toolInvocation.toolCallId, chatId: wizardSO.id },
    // plannerMsg.skillsets!,
    {
      user,
    },
  );
  // Engineer 的ToolResult 结果 发回去
  const { message: engineerMsg } = await wizardSO.resolve(mockRequestContext, {
    type: 'TOOL',
    toolInvocation: {
      state: 'result',
      step: 0,
      toolCallId: engineerCallTool.toolInvocation.toolCallId,
      toolName: engineerCallTool.toolInvocation.toolName,
      args: engineerCallTool.toolInvocation.args, // z.custom<Required<any>>((x) => x !== undefined),
      result: engineerToolResult, // z.cust
    },
  });

  // 这时候，只剩 installer 了！
  expect(
    engineerMsg.parts.find(
      (part) =>
        part.type === 'tool-invocation' &&
        part.toolInvocation.state === 'call' &&
        part.toolInvocation.toolName === 'bika-ai-app-builder-installer',
    ),
  ).toBeDefined();

  const installerCallTool = engineerMsg.parts[engineerMsg.parts.length - 1];
  assert(installerCallTool.type === 'tool-invocation');
  expect(installerCallTool.toolInvocation.state).toBe('call');
  expect((installerCallTool.toolInvocation.args as any).engineerArtifactId).toBeDefined();

  // 手工执行 installer 的 call
  const installerToolResult = await AIChatSO.executeTool(
    { toolCallId: installerCallTool.toolInvocation.toolCallId, chatId: wizardSO.id },
    // engineerMsg.skillsets!,
    {
      user,
    },
  );
  // Engineer 的ToolResult 结果 发回去
  const { message: installerMsg } = await wizardSO.resolve(mockRequestContext, {
    type: 'TOOL',
    toolInvocation: {
      state: 'result',
      step: 0,
      toolCallId: installerCallTool.toolInvocation.toolCallId,
      toolName: installerCallTool.toolInvocation.toolName,
      args: installerCallTool.toolInvocation.args, // z.custom<Required<any>>((x) => x !== undefined),
      result: installerToolResult, // z.cust
    },
  });
  // installer 完成后，一切结束，最后应该是文字

  console.log('[DEBUG] Installer Result', JSON.stringify(installerMsg, null, 2));

  // const childFolders = await rootFolder.getChildren();
  // expect(childFolders.length).toBe(0);

  // // 说YES，直接创建
  // const { intent: confirmIntent, message: confirmMsg } = await wizardSO.resolve(mockRequestContext, {
  //   type: 'UI',
  //   uiResolve: {
  //     type: 'FLOW',
  //     response: 'YES',
  //   },
  // });
  // console.log(confirmMsg.text);
  // const childFolders2 = await rootFolder.getChildren(true);
  // expect(childFolders2.length).toBe(1); // 创建成功
});

test('Test AI Build Apps + without approvals + with default space id', { timeout: 5 * 60 * 1000 }, async () => {
  if (!TEST_LONG_AI) return;
  const { user, space, rootFolder } = await MockContext.initUserContext();
  const wizardSO = await AISO.newWizardByUser(user.id, {
    type: 'BUILDER',
    spaceId: space.id,
    needApproval: {
      planner: false,
      engineer: false,
    },
  });

  const mockRequestContext = MockContext.createMockRequestContext(user);

  // 由于给了spaceId，直接返回flow
  const { intent: intentCRM, message: msgCRM } = await wizardSO.message(
    mockRequestContext,
    '我想要一个客户管理系统!简单拜访记录管理就好!',
  );

  expect(intentCRM.resolutionStatus).toBe('NEEDS_CONFIRMATION');

  // const intentUIPart = msgCRM.parts[0];
  const parts = await msgCRM.parts;

  expect(parts.length).toBe(6); // step-start + planer result + step-start + engineer result

  // 确保已经有 installer 可以 在 call 等待状态
  const plannerCallTool = msgCRM.parts.find(
    (part) =>
      part.type === 'tool-invocation' &&
      part.toolInvocation.state === 'result' &&
      part.toolInvocation.toolName === 'bika-ai-app-builder-planner',
  );
  expect(plannerCallTool).toBeDefined();

  const engineerCallTool = msgCRM.parts.find(
    (part) =>
      part.type === 'tool-invocation' &&
      part.toolInvocation.state === 'result' &&
      part.toolInvocation.toolName === 'bika-ai-app-builder-engineer',
  );

  expect(engineerCallTool).toBeDefined();

  // 两个 tool 都是 result，因为 engine 不需审批

  const installCallTool = msgCRM.parts.find(
    (part) =>
      part.type === 'tool-invocation' &&
      part.toolInvocation.state === 'call' &&
      part.toolInvocation.toolName === 'bika-ai-app-builder-installer',
  );
  assert(installCallTool, 'Install Call Tool should be defined');
  assert(installCallTool.type === 'tool-invocation', 'Install Call Tool should be defined');
  expect(installCallTool).toBeDefined();
  expect(installCallTool.toolInvocation.state).toBe('call');
  expect(installCallTool.toolInvocation.toolName).toBe('bika-ai-app-builder-installer');
  expect((installCallTool.toolInvocation.args as any).spaceId).toBeDefined();

  // console.log(parts);
  // const flowTool = parts[17];
  // assert(flowTool.type === 'tool-invocation');
  // expect(flowTool.toolInvocation.toolName).toBe('form');
  // expect((flowTool.toolInvocation.args as any).type).toBe('FLOW');
  // expect(AIConsultingAIToolSchema.parse(tools[3])).toBeDefined();

  // const intentUI3 = await msgCRM.ui!;
  // assert(intentUI3.type === 'FLOW');
  // expect(intentUI3.resources.length).toBeGreaterThan(0);

  // 说NO，重新回到对话
  // const { intent: confirmIntent, message: confirmMsg } = await wizardSO.resolve(mockRequestContext, {
  //   type: 'UI',
  //   uiResolve: {
  //     type: 'FLOW',
  //     response: 'NO',
  //   },
  // });

  // expect(JSON.stringify(confirmMsg.text)).toContain('?'); // 带有问号
  // expect(confirmIntent.resolutionStatus).toBe('DIALOG'); // 回到对话状态

  // 再次对话，恢复之前的
  // const { intent: intentCRM2, message: msgCRM2 } = await wizardSO.message(
  //   mockRequestContext,
  //   '我想要一个客户管理系统!简单拜访记录管理就好!',
  // );

  // expect(intentCRM2.resolutionStatus).toBe('NEEDS_CONFIRMATION');
  // const parts2 = await msgCRM.parts;
  // expect(parts2.length).toBe(18);
  // const flowTool2 = parts2[17];
  // assert(flowTool2.type === 'tool-invocation');
  // expect(flowTool2.toolInvocation.toolName).toBe('form');
  // expect((flowTool2.toolInvocation.args as any).type).toBe('FLOW'); // intent ui

  // const cTool = parts2[16];
  // expect((cTool as any).toolInvocation.toolName).toBe('bika-app-builder');
  // expect((cTool as any).toolInvocation.args.consultingType).toBe('marketer');
  // expect(cTool.toolInvocation.args.consultingType).toBe('business-analyst');

  // const intentUI4 = await msgCRM2.ui!;
  // assert(intentUI4.type === 'FLOW');
  // expect(intentUI4.resources.length).toBeGreaterThan(0);

  // 再次生成，触发AI生成...
  // const { intent: confirmIntent3, message: _confirmMsg3 } = await wizardSO.resolve(mockRequestContext, {
  //   type: 'UI',
  //   uiResolve: {
  //     type: 'FLOW',
  //     response: 'AGAIN',
  //   },
  // });
  // expect(confirmIntent3.resolutionStatus).toBe('DIALOG');

  // TODO: 暂时disable AI生成, again会返回主聊天
  // expect(confirmIntent3.resolutionStatus).toBe('NEEDS_CONFIRMATION');
  // const confirmIntenUI3 = await confirmMsg3.ui!;
  // assert(confirmIntenUI3.type === 'FLOW');
  // expect(confirmIntenUI3.resources.length).toBeGreaterThan(0);
});

test('Test AI Build Apps --- `Build an AI Agent, to track U.S. stock news and generate a 4 - page impact report`', async () => {
  const { user, space } = await MockContext.initUserContext();

  // 三个tool
  // bika-ai-app-builder-planner
  // bika-ai-app-builder-engineer
  // bika-ai-app-builder-installer

  // bika-ai-app-builder-planner 调用了llm生成markdown, 也需要加一个filter
  AIMockSO.addFilter({
    check: async (opts) => {
      if (
        opts.prompt.find(
          (p) => p.role === 'system' && p.content.includes('You are a Bika.ai, AI Agents designer and planner'),
        )
      ) {
        return true;
      }
      return false;
    },
    doStream: 'planner result',
  });

  const chunks: LanguageModelV1StreamPart[] = [];
  let message: AIMessageVO | null = null;
  let nodeId: string | null = null;
  let engineerArtifactId: string | null = null;

  // 主流程的filter add call tool filter
  AIMockSO.addFilter({
    check: async (_opts) => true,
    doStream: async (_options: LanguageModelV1CallOptions) => {
      const mockChunks = async () => {
        const plannerCall: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName: 'bika-ai-app-builder-planner',
          args: JSON.stringify({
            user_prompt_reasoning:
              'Build an AI Agent that can automatically track U.S. stock news, analyze the impact of news on the market, and generate a 4-page detailed impact report. The steps include: 1. Automatically collect U.S. stock-related news; 2. Analyze news content to determine its positive or negative impact on the market; 3. Integrate analysis results to write a 4-page detailed report covering news summaries, impact analysis, data support, and conclusion recommendations',
          }),
          toolCallType: 'function',
        };
        const toolset = await tools({ user });
        const plannerTool = toolset['bika-ai-app-builder-planner']!;
        assert(plannerTool, 'Planner tool should be defined');
        assert(plannerTool.execute, 'Planner tool should have execute function');
        const plannerResult = await plannerTool.execute(JSON.parse(plannerCall.args), {
          toolCallId: plannerCall.toolCallId,
          messages: [],
        });

        const engineerCall: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName: 'bika-ai-app-builder-engineer',
          args: JSON.stringify({
            plannerArtifactId: plannerResult.artifactId,
            agentName: '美股新聞追蹤與影響分析報告Agent',
            agentDescription: '自動追蹤美國股票新聞，分析其對市場的影響，並生成4頁詳細影響報告。',
            agentSkills: [
              {
                category: 'News',
                skills: ['us_stock', 'fetch', 'summarize'],
              },
              {
                category: 'Market',
                skills: ['impact_analysis', 'trend', 'data_support'],
              },
              {
                category: 'Report',
                skills: ['generate', 'format_4_pages', 'conclusion'],
              },
            ],
            agentPrompt:
              '你是一個專業的美國股票新聞追蹤與分析AI Agent。你的主要任務是：\n\n1. 自動收集最新的美國股票新聞。\n2. 分析每則新聞對美國股市的潛在影響，包括正面、負面或中性。\n3. 整合分析結果，撰寫一份4頁的詳細報告，內容包括：\n   - 新聞摘要\n   - 影響分析\n   - 數據支持\n   - 結論與建議\n4. 報告需結構清晰、條理分明，並以專業金融分析師的語氣撰寫。',
          }),
          toolCallType: 'function',
        };
        const engineerTool = toolset['bika-ai-app-builder-engineer']!;
        assert(engineerTool, 'Engineer tool should be defined');
        assert(engineerTool.execute, 'Engineer tool should have execute function');
        const engineerResult = await engineerTool.execute(JSON.parse(engineerCall.args), {
          toolCallId: engineerCall.toolCallId,
          messages: [],
        });
        engineerArtifactId = engineerResult.artifactId;

        const installerCall: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName: 'bika-ai-app-builder-installer',
          args: JSON.stringify({
            engineerArtifactId: engineerResult.artifactId,
            spaceId: space.id,
          }),
          toolCallType: 'function',
        };
        const installerTool = toolset['bika-ai-app-builder-installer']!;
        assert(installerTool, 'Installer tool should be defined');
        assert(installerTool.execute, 'Installer tool should have execute function');
        const installerResult = await installerTool.execute(JSON.parse(installerCall.args), {
          toolCallId: installerCall.toolCallId,
          messages: [],
        });
        nodeId = installerResult.nodeId;

        chunks.push(plannerCall);
        chunks.push(engineerCall);
        chunks.push(installerCall);
      };

      await mockChunks();

      return {
        stream: simulateReadableStream({
          chunks,
          initialDelayInMs: 100,
          chunkDelayInMs: 50,
        }),
        rawCall: {
          rawPrompt: '',
          rawSettings: {},
        },
      };
    },
  });

  await AIMockSO.sandbox(async () => {
    const chat = await AISO.newWizardByUser(user.id, {
      type: 'BUILDER',
      spaceId: space.id,
    });
    const streamPromise = createResumableDataStream({
      chatId: chat.id,
      execute: async (dataStream) => {
        const result = await chat.resolve(
          MockContext.createMockRequestContext(user),
          {
            type: 'MESSAGE',
            message: 'Build an AI Agent, to track U.S. stock news and generate a 4 - page impact report',
          },
          'en',
          dataStream,
        );
        message = result.message;
      },
      onError: (_error) => {
        console.log('error', _error);
        return 'error';
      },
    });
    // read stream
    const stream = await streamPromise;
    if (stream) {
      const reader = stream.getReader();
      while (true) {
        const { done, value } = await reader.read();
        console.log('value----', value);
        if (done) {
          break;
        }
      }
    }
  });

  expect(message).toBeDefined();
  // 3个tool + 1个text
  expect(message!.parts.length).toBe(4);
  const aiNode = await AINodeSO.init(nodeId!);
  const artifact = await AIArtifactSO.getById(engineerArtifactId!);
  const bo = await aiNode.toBO();
  expect(artifact.data.skillsets).toStrictEqual(bo.skillsets);
  AIMockSO.clearFilters();
}, 100000);
