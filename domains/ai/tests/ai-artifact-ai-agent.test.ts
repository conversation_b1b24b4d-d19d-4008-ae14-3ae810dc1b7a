import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import { test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AIArtifactSO } from '@bika/domains/ai-artifacts/ai-artifact-so';

const TEST_LONG_AI = process.env.TEST_LONG_AI && process.env.TEST_LONG_AI === 'true';

/**
 * AI 生成CustomTemplate！
 */
test('Test Artifact AI Agent Return', { timeout: 5 * 60 * 1000 }, async () => {
  if (!TEST_LONG_AI) return;

  const { user, space, rootFolder } = await MockContext.initUserContext();

  const artifact1 = await AIArtifactSO.create(user, {
    type: 'ai-agent',
    prompt: {
      system: 'You are Bika.ai AI Agent builder',
      prompt: 'Github issue creator',
    },
    toolCallId: generateNanoID('test_artifact_'),
  });
  const value = await artifact1.getValue(user);
  assert(value.type === 'ai-agent', 'Artifact value type should be ai-agent');
  console.log('Artifact value', value.data);
  expect(value.data.name).toBeDefined();
  expect(value.data.description).toBeDefined();
  expect(value.data.skillsets!.length).toBeGreaterThan(0);
});
