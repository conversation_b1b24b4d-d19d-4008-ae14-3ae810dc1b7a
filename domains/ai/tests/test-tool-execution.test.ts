import { LanguageModelV1StreamPart } from '@ai-sdk/provider';
import { simulateReadableStream, ToolResult } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { describe, test, expect, vi, afterEach } from 'vitest';
import { z } from 'zod';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AIMockSO } from '@bika/domains/ai/server/ai-mock/ai-mock-so';
import { AISO } from '@bika/domains/ai/server/ai-so';

describe('AI SDK Tool Execution Tests', () => {
  // 创建一个 mock tool 来跟踪执行次数
  const createMockTool = () => {
    const executeMock = vi.fn().mockResolvedValue({
      success: true,
      message: 'Mock tool executed successfully',
      timestamp: new Date().toISOString(),
    });

    return {
      execute: executeMock,
      parameters: z.object({
        input: z.string(),
      }),
    };
  };

  afterEach(() => {
    vi.clearAllMocks();
    AIMockSO.clearFilters();
  });

  test('Test AI SDK automatically executes tool execute method', async () => {
    const { user } = await MockContext.createMockUser();

    // 创建 mock tool
    const mockTool = createMockTool();
    const toolName = 'test-tool';

    // 设置 mock toolset
    const mockToolset = {
      [toolName]: mockTool,
    };

    // 记录 chunks
    const chunks: LanguageModelV1StreamPart[] = [];

    // 添加 mock filter
    AIMockSO.addFilter({
      check: async (_opts) => true,
      doStream: async (_options) => {
        // 创建 tool call
        const toolCall: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName,
          args: JSON.stringify({ input: 'test input' }),
          toolCallType: 'function',
        };
        chunks.push(toolCall);

        return {
          stream: simulateReadableStream({
            chunks,
            initialDelayInMs: 100,
            chunkDelayInMs: 50,
          }),
          rawCall: { rawPrompt: '', rawSettings: {} },
        };
      },
    });

    await AIMockSO.sandbox(async () => {
      // 调用 AI SDK
      const { result } = await AISO.streamText(
        {
          user,
          prompt: 'Test prompt',
          tools: mockToolset,
        },
        {
          model: 'mock',
        },
      );

      // 等待 stream 完成
      for await (const _ of result.fullStream) {
        // 消费 stream
      }

      // 验证 tool.execute 是否被调用
      expect(mockTool.execute).toHaveBeenCalledTimes(1);
      expect(mockTool.execute).toHaveBeenCalledWith(
        { input: 'test input' },
        expect.objectContaining({
          toolCallId: expect.any(String),
          messages: expect.any(Array),
        }),
      );

      // 验证 steps 中是否包含 tool result
      const steps = await result.steps;
      expect(steps.length).toBeGreaterThan(0);

      // 检查最后一个 step 是否包含 tool result
      const lastStep = steps[steps.length - 1];
      expect(lastStep.toolResults).toBeDefined();
      expect(lastStep.toolResults!.length).toBe(1);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const toolResult: ToolResult<any, any, any> = lastStep.toolResults![0];
      expect(toolResult.toolName).toBe(toolName);
      expect(toolResult.result).toEqual({
        success: true,
        message: 'Mock tool executed successfully',
        timestamp: expect.any(String),
      });
    });
  });

  test('Test AI SDK does not execute tool when no tools provided', async () => {
    const { user } = await MockContext.createMockUser();

    // 创建 mock tool
    const mockTool = createMockTool();

    // 记录 chunks
    const chunks: LanguageModelV1StreamPart[] = [];

    // 添加 mock filter
    AIMockSO.addFilter({
      check: async (_opts) => true,
      doStream: async (_options) => {
        // 创建 tool call，但不提供 tools
        const toolCall: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName: 'test-tool',
          args: JSON.stringify({ input: 'test input' }),
          toolCallType: 'function',
        };
        chunks.push(toolCall);

        return {
          stream: simulateReadableStream({
            chunks,
            initialDelayInMs: 100,
            chunkDelayInMs: 50,
          }),
          rawCall: { rawPrompt: '', rawSettings: {} },
        };
      },
    });

    await AIMockSO.sandbox(async () => {
      // 调用 AI SDK，不提供 tools
      const { result } = await AISO.streamText(
        {
          user,
          prompt: 'Test prompt',
          // 注意：这里没有提供 tools
        },
        {
          model: 'mock',
        },
      );

      // 等待 stream 完成
      for await (const _ of result.fullStream) {
        // 消费 stream
      }

      // 验证 tool.execute 没有被调用
      expect(mockTool.execute).not.toHaveBeenCalled();

      // 验证 steps 中不包含 tool result
      const steps = await result.steps;
      expect(steps.length).toBeGreaterThan(0);

      // 检查最后一个 step 是否不包含 tool result
      const lastStep = steps[steps.length - 1];
      expect(lastStep.toolCalls.length).toBe(0);
    });
  });

  test('Test AI SDK handles tool execution errors', async () => {
    const { user } = await MockContext.createMockUser();

    // 创建会抛出错误的 mock tool
    const errorTool = {
      execute: vi.fn().mockRejectedValue(new Error('Tool execution failed')),
      parameters: z.object({
        input: z.string(),
      }),
    };

    const toolName = 'error-tool';
    const mockToolset = {
      [toolName]: errorTool,
    };

    // 记录 chunks
    const chunks: LanguageModelV1StreamPart[] = [];

    // 添加 mock filter
    AIMockSO.addFilter({
      check: async (_opts) => true,
      doStream: async (_options) => {
        // 创建 tool call
        const toolCall: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName,
          args: JSON.stringify({ input: 'test input' }),
          toolCallType: 'function',
        };
        chunks.push(toolCall);

        return {
          stream: simulateReadableStream({
            chunks,
            initialDelayInMs: 100,
            chunkDelayInMs: 50,
          }),
          rawCall: { rawPrompt: '', rawSettings: {} },
        };
      },
    });

    await AIMockSO.sandbox(async () => {
      // 调用 AI SDK
      const { result } = await AISO.streamText(
        {
          user,
          prompt: 'Test prompt',
          tools: mockToolset,
        },
        {
          model: 'mock',
        },
      );

      // 等待 stream 完成
      for await (const _ of result.fullStream) {
        console.log('result.fullStream', _);
        // 消费 stream
      }

      // 验证 tool.execute 被调用
      expect(errorTool.execute).toHaveBeenCalledTimes(1);

      // 验证 steps 中是否包含错误信息
      const steps = await result.steps;
      expect(steps.length).toBeGreaterThan(0);

      // 检查最后一个 step 是否包含错误
      const lastStep = steps[steps.length - 1];
      expect(lastStep.toolResults.length).toBe(0);
    });
  });

  test('Test AI SDK handles chained tool calls', async () => {
    const { user } = await MockContext.createMockUser();

    // 创建多个 mock tools
    const tool1 = {
      execute: vi.fn().mockResolvedValue({
        success: true,
        message: 'Tool 1 executed',
        output: 'result1',
        timestamp: new Date().toISOString(),
      }),
      parameters: z.object({
        input: z.string(),
      }),
    };

    const tool2 = {
      execute: vi.fn().mockResolvedValue({
        success: true,
        message: 'Tool 2 executed',
        output: 'result2',
        timestamp: new Date().toISOString(),
      }),
      parameters: z.object({
        input: z.string(),
        previousResult: z.string().optional(),
      }),
    };

    const tool3 = {
      execute: vi.fn().mockResolvedValue({
        success: true,
        message: 'Tool 3 executed',
        output: 'final_result',
        timestamp: new Date().toISOString(),
      }),
      parameters: z.object({
        input: z.string(),
        previousResults: z.array(z.string()).optional(),
      }),
    };

    const mockToolset = {
      'tool-1': tool1,
      'tool-2': tool2,
      'tool-3': tool3,
    };

    // 记录 chunks
    const chunks: LanguageModelV1StreamPart[] = [];

    // 添加 mock filter
    AIMockSO.addFilter({
      check: async (_opts) => true,
      doStream: async (_options) => {
        // 创建链式 tool calls
        const toolCall1: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName: 'tool-1',
          args: JSON.stringify({ input: 'initial input' }),
          toolCallType: 'function',
        };
        // 手动执行 tool1
        await tool1.execute(JSON.parse(toolCall1.args), { toolCallId: toolCall1.toolCallId });

        const toolCall2: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName: 'tool-2',
          args: JSON.stringify({ input: 'second input', previousResult: 'result1' }),
          toolCallType: 'function',
        };
        // 手动执行 tool2
        await tool2.execute(JSON.parse(toolCall2.args), { toolCallId: toolCall2.toolCallId });

        const toolCall3: LanguageModelV1StreamPart = {
          type: 'tool-call',
          toolCallId: generateNanoID('tool-call-'),
          toolName: 'tool-3',
          args: JSON.stringify({ input: 'final input', previousResults: ['result1', 'result2'] }),
          toolCallType: 'function',
        };
        // 手动执行 tool3
        await tool3.execute(JSON.parse(toolCall3.args), { toolCallId: toolCall3.toolCallId });

        chunks.push(toolCall1, toolCall2, toolCall3);

        return {
          stream: simulateReadableStream({
            chunks,
            initialDelayInMs: 100,
            chunkDelayInMs: 50,
          }),
          rawCall: { rawPrompt: '', rawSettings: {} },
        };
      },
    });

    await AIMockSO.sandbox(async () => {
      // 调用 AI SDK
      const { result } = await AISO.streamText(
        {
          user,
          prompt: 'Test chained tool calls',
          tools: mockToolset,
        },
        {
          model: 'mock',
          onChunk: async (event) => {
            if (event.chunk.type === 'tool-call') {
              const { toolCallId, toolName: callToolName, args } = event.chunk;
              const tool = mockToolset[callToolName as keyof typeof mockToolset];
              await tool!.execute(args, { toolCallId });
            }
          },
        },
      );

      // 等待 stream 完成
      for await (const _ of result.fullStream) {
        // 消费 stream
      }

      // 验证所有 tools 都被调用, chunk 中包含 3 个 tool call, onChunk中每个tool 执行了一次, ai-sdk自动调用一次, 所以每个总共调用3次
      expect(tool1.execute).toHaveBeenCalledTimes(3);
      expect(tool2.execute).toHaveBeenCalledTimes(3);
      expect(tool3.execute).toHaveBeenCalledTimes(3);

      // 验证调用参数
      expect(tool1.execute).toHaveBeenCalledWith(
        { input: 'initial input' },
        expect.objectContaining({
          toolCallId: expect.any(String),
          messages: expect.any(Array),
        }),
      );

      expect(tool2.execute).toHaveBeenCalledWith(
        { input: 'second input', previousResult: 'result1' },
        expect.objectContaining({
          toolCallId: expect.any(String),
          messages: expect.any(Array),
        }),
      );

      expect(tool3.execute).toHaveBeenCalledWith(
        { input: 'final input', previousResults: ['result1', 'result2'] },
        expect.objectContaining({
          toolCallId: expect.any(String),
          messages: expect.any(Array),
        }),
      );

      // 验证 steps 中包含所有 tool results
      const steps = await result.steps;
      expect(steps.length).toBeGreaterThan(0);

      // 检查最后一个 step 是否包含所有 tool results
      const lastStep = steps[steps.length - 1];
      expect(lastStep.toolResults).toBeDefined();
      expect(lastStep.toolResults!.length).toBe(3);

      // 验证 tool results 的顺序和内容
      const toolResults = lastStep.toolResults!;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const toolResult1: ToolResult<any, any, any> = toolResults[0];
      expect(toolResult1.toolName).toBe('tool-1');
      expect(toolResult1.result).toEqual({
        success: true,
        message: 'Tool 1 executed',
        output: 'result1',
        timestamp: expect.any(String),
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const toolResult2: ToolResult<any, any, any> = toolResults[1];
      expect(toolResult2.toolName).toBe('tool-2');
      expect(toolResult2.result).toEqual({
        success: true,
        message: 'Tool 2 executed',
        output: 'result2',
        timestamp: expect.any(String),
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const toolResult3: ToolResult<any, any, any> = toolResults[2];
      expect(toolResult3.toolName).toBe('tool-3');
      expect(toolResult3.result).toEqual({
        success: true,
        message: 'Tool 3 executed',
        output: 'final_result',
        timestamp: expect.any(String),
      });
    });
  });

  test('Test AI SDK does not execute tool when tool call is not in chunks', async () => {
    const { user } = await MockContext.createMockUser();

    // 创建 mock tool
    const mockTool = createMockTool();
    const toolName = 'test-tool';

    // 设置 mock toolset
    const mockToolset = {
      [toolName]: mockTool,
    };

    // 记录 chunks - 不包含 tool call
    const chunks: LanguageModelV1StreamPart[] = [];

    // 添加 mock filter
    AIMockSO.addFilter({
      check: async (_opts) => true,
      doStream: async (_options) => {
        // 只添加文本 chunk，不添加 tool call
        const textChunk: LanguageModelV1StreamPart = {
          type: 'text-delta',
          textDelta: 'This is a text response without tool calls',
        };
        chunks.push(textChunk);

        return {
          stream: simulateReadableStream({
            chunks,
            initialDelayInMs: 100,
            chunkDelayInMs: 50,
          }),
          rawCall: { rawPrompt: '', rawSettings: {} },
        };
      },
    });

    await AIMockSO.sandbox(async () => {
      // 调用 AI SDK
      const { result } = await AISO.streamText(
        {
          user,
          prompt: 'Test prompt',
          tools: mockToolset,
        },
        {
          model: 'mock',
        },
      );

      // 等待 stream 完成
      for await (const _ of result.fullStream) {
        // 消费 stream
      }

      // 验证 tool.execute 没有被调用，因为没有 tool call 在 chunks 中
      expect(mockTool.execute).not.toHaveBeenCalled();

      // 验证 steps 中不包含 tool result
      const steps = await result.steps;
      expect(steps.length).toBeGreaterThan(0);

      // 检查最后一个 step 是否不包含 tool result
      const lastStep = steps[steps.length - 1];
      expect(lastStep.toolCalls.length).toBe(0);
    });
  });
});
