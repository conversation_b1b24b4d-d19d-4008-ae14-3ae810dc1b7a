import { describe, test, expect } from 'vitest';
import { PresetLanguageModelServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import { DatabaseAITextField } from '@bika/types/database/bo';
import { MockContext } from '../../__tests__/mock';
import { DatabaseSO } from '../../database/server/database-so';
import { AIWriterSO } from '../server/ai-writer-so';

const TEST_AI = process.env.TEST_AI && process.env.TEST_AI === 'true';

describe('AI Filling Record', async () => {
  test('Test AI Filling Record--OpenAI', async () => {
    if (!TEST_AI) {
      return;
    }
    const { user, member, rootFolder, space } = await MockContext.initUserContext();
    const integration = await space.createIntegration(user.id, {
      type: 'OPENAI',
      name: '<PERSON><PERSON><PERSON>',
      apiKey: process.env.OPENAI_API_KEY!,
      baseUrl: 'https://openai.bika.ltd/v1',
    });
    const nodeId = await rootFolder.createChildren(user, [
      {
        resourceType: 'DATABASE',
        name: 'Quick QA',
        templateId: 'quick_qa',
        views: [
          {
            type: 'TABLE',
            name: 'Quick QA',
          },
        ],
        fields: [
          {
            name: 'User Name',
            templateId: 'user_name',
            type: 'SINGLE_TEXT',
            primary: true,
          },
          {
            name: 'Question',
            templateId: 'question',
            type: 'LONG_TEXT',
          },
          {
            name: 'Answer',
            templateId: 'answer',
            type: 'AI_TEXT',
            property: {
              model: {
                kind: 'custom',
                custom: {
                  type: 'integration',
                  integrationId: integration.id,
                },
              },
              modelId: 'gpt-3.5',
              // type: 'INTEGRATION',
              // integrationId: integration.id,
              prompt: 'You are a helpful assistant. Answer the question based on the context.',
            },
          },
        ],
      },
    ]);
    const database = await DatabaseSO.init(nodeId);
    const userField = database.getFields().find((f) => f.type === 'SINGLE_TEXT' && f.templateId === 'user_name')!;
    const questionField = database.getFields().find((f) => f.type === 'LONG_TEXT' && f.templateId === 'question')!;
    const field = database.getFields().find((f) => f.type === 'AI_TEXT')!;
    const record1 = await database.createRecord(user, member, {
      [userField.id]: 'xiao li',
      [questionField.id]: 'What is the capital of Japan?',
      [field.id]: '',
    });
    await field.update(user, {
      ...field.toBO(),
      property: {
        ...field.property,
        prompt: `You are a helpful assistant. Answer the question based on the context. \n <%= ${questionField.id} %>`,
      },
    } as unknown as DatabaseAITextField);

    const result = await AIWriterSO.quickWrite(
      {
        type: 'RECORD_CELL_FILLING',
        databaseId: database.id,
        fieldId: field!.id,
        recordId: record1.id,
      },
      '',
      {
        locale: 'zh-CN',
        createdAt: new Date().toISOString(),
        userId: user.id,
      },
      user,
    );
    expect(result.data).toBe('The capital of Japan is Tokyo.');
  });

  test('Test AI Filling Record--DeepSeek', async () => {
    if (!TEST_AI) {
      return;
    }
    const { user, member, rootFolder, space } = await MockContext.initUserContext();
    const model = 'qwen/qwen-plus';
    const config = PresetLanguageModelServerConfig[model];
    const integration = await space.createIntegration(user.id, {
      type: 'DEEPSEEK',
      name: 'DeepSeek',
      apiKey: config.apiKey!,
      baseUrl: config.baseUrl,
    });
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'Quick QA',
      templateId: 'quick_qa',
      fields: [
        {
          name: 'User Name',
          templateId: 'user_name',
          type: 'SINGLE_TEXT',
          primary: true,
        },
        {
          name: 'Question',
          templateId: 'question',
          type: 'LONG_TEXT',
        },
        {
          name: 'Answer',
          templateId: 'answer',
          type: 'AI_TEXT',
          property: {
            model: {
              kind: 'custom',
              custom: {
                type: 'integration',
                integrationId: integration.id,
                // prompt: 'You are a helpful assistant. Answer the question based on the context.',
              },
            },
            modelId: model,
            // type: 'INTEGRATION',
            // integrationId: integration.id,
            prompt: 'You are a helpful assistant. Answer the question based on the context.',
          },
        },
      ],
    });
    const database = await DatabaseSO.init(node.id);
    const userField = database.getFields().find((f) => f.type === 'SINGLE_TEXT' && f.templateId === 'user_name')!;
    const questionField = database.getFields().find((f) => f.type === 'LONG_TEXT' && f.templateId === 'question')!;
    const field = database.getFields().find((f) => f.type === 'AI_TEXT')!;
    const record1 = await database.createRecord(user, member, {
      [userField.id]: 'xiao li',
      [questionField.id]: '日本的首都是哪里？',
      [field.id]: '',
    });
    await field.update(user, {
      ...field.toBO(),
      property: {
        ...field.property,
        model,
        prompt: `你是一个AI助手，请根据以下问题给出回答。 \n <%= ${questionField.id} %>, 被回复的人是 <%= ${userField.id} %>`,
      },
    } as unknown as DatabaseAITextField);

    const result = await AIWriterSO.quickWrite(
      {
        type: 'RECORD_CELL_FILLING',
        databaseId: database.id,
        fieldId: field!.id,
        recordId: record1.id,
      },
      '',
      {
        locale: 'zh-CN',
        createdAt: new Date().toISOString(),
        userId: user.id,
      },
      user,
    );
    expect(result.data).toContain('东京');
    expect(result.data).toContain('xiao li');
  });
});
