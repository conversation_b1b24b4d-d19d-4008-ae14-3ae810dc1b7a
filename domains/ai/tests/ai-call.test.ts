import { generateNanoID } from 'basenext/utils/nano-id';
import { uploadFileToUrl } from 'sharelib/upload-file-node';
import { expect, test } from 'vitest';
import { PromptsConfig } from '@bika/contents/config/server';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { CreateReminderIntentParams, CreateReminderIntentParamsSchema } from '@bika/types/ai/bo';
import { AIMessageVO } from '@bika/types/ai/vo';
import { crudAI } from './crud-ai';
import { IntentSO } from '../server/ai-chat/intent-so';

// 默认不测whisper，因为没有cache，手工打开本地跑npm run test:ai吧
const TEST_WHISPER = false;

const TEST_AI = process.env.TEST_AI && process.env.TEST_AI === 'true';
if (TEST_AI) {
  console.log(`Will test AI via OpenAI API.... Proxy: ${process.env.OPENAI_BASE_URL}`);
}
test('OpenAI Whisper Test', async () => {
  if (TEST_WHISPER && TEST_AI) {
    // ===   AI Mission / Alignment
    // 本地语音文件快速测试
    // TODO: 用户语音识别，whisper
    const user = await MockContext.createUser();
    const audioFilePath = `${__dirname}/test.m4a`;
    const text = await AISO.audio2Text(audioFilePath);
    expect(text!.toLowerCase()).toContain('this is how i sound hope you like it');

    const text2 = await AISO.audio2Text(`${__dirname}/test2.m4a`);
    expect(text2!).toContain('冚家剷');

    // 通过上传到S3，然后做AI语音转换（客户端AI案例）
    const { path, presignedPutUrl } = await TmpAttachmentSO.getPresignedPut(user, '.m4a');

    const putRes = await uploadFileToUrl(presignedPutUrl, audioFilePath);
    expect(putRes.status).toBe(200);

    const attachmentSO = await AttachmentSO.createByPresignedPut(user, path, 'ai/');
    const text3 = await AISO.audio2TextByAttachment(attachmentSO);
    expect(text3!.toLowerCase()).toContain('this is how i sound hope you like it');

    // TODO: 语音是否上传附件 (Mission来保留附件，Mission会出现在Record Activity里)

    // TODO: 首页的AI语音，将首页的节点放入prompt

    // TODO: AI创建了一个Mission: Create Record给另一个人

    // TODO: AI创建了一个Action: Create Record，直接执行

    // TODO: AI文字转指令，转成对应的“表数据” (AI Reminder)

    // ==== AI Agent节点 （不急）
    // TODO: 创建一个AI Agent聊天节点，用于ChatGPT

    // TODO: 训练AI Agent聊天节点，选择database，用于ChatGPT
  }
});

test('AI Story Test', async () => {
  const { user, space } = await MockContext.initUserContext();
  crudAI(user, space);
});

test('Test AI Basic Calc', async () => {
  if (TEST_AI) {
    const aiMsg = await AISO.systemInvoke('1+1=?');
    console.log(`AI output: ${aiMsg}`);
    expect(aiMsg).toContain('2');
  }
});

test('Test Resend Email', async () => {
  if (TEST_AI) {
    // 暂停使用Resend
    // const { data, error } = await EmailSO.sendEmailByService('test', 'test', ['<EMAIL>']);
    // expect(data).toBeDefined();
    // expect(error).toBeNull();
  }
});

test('AI Chat Test, recognize intent', async () => {
  if (TEST_AI) {
    const prompt1 = PromptsConfig.chat(
      [
        {
          id: generateNanoID('aim'),
          role: 'user',
          parts: [
            {
              type: 'text',
              text: '明天早上7点喊我起床!!（nextIntent: CREATE_REMINDER)',
            },
          ],
        },
      ],
      'zh-CN',
    );
    const res1 = (await AISO.systemInvokeJSON(prompt1)) as PromptsConfig.AIChatResponse;
    console.log('明天早上7点喊我起床!!AI Chat Test, recognize intent: ', res1, ' typeof: ', typeof res1);
    expect(['CREATE_REMINDER', 'CHAT']).includes(res1.nextIntent);
    // expect(res1.aiMessage).toContain('创建了');

    const prompt2 = PromptsConfig.chat(
      [
        {
          id: generateNanoID('aim'),
          role: 'user',
          parts: [
            {
              type: 'text',
              text: '你叫什么名字呀？',
            },
          ],
        },
      ],
      'zh-CN',
    );
    const res2 = (await AISO.systemInvokeJSON(prompt2)) as PromptsConfig.AIChatResponse;
    expect(res2.nextIntent).toBe('CHAT'); // CHAT 或null都会被忽略
    expect(res2.aiMessage.length).toBeGreaterThan(1);
  }
});

test('AI-SDK Chat Test, recognize intent', async () => {
  if (TEST_AI) {
    const prompt1 = PromptsConfig.chat(
      [
        {
          id: generateNanoID('aim'),
          role: 'user',
          parts: [
            {
              type: 'text',
              text: 'What are the key cyberpunk themes that Gibson explores in Neuromancer?',
            },
          ],
        },
      ],
      'en',
    );
    const res1 = (await AISO.invokeByAISDK(prompt1, { model: 'anthropic/claude-sonnet-3.7' })) as string;
    console.log(
      'What are the key cyberpunk themes that Gibson explores in Neuromancer? !!AI Chat Test, recognize intent: ',
      res1,
      ' typeof: ',
      typeof res1,
    );
  }
});

// 测试AI意图对象填充
test('AI Intent Filling Test', async () => {
  // AI意图的结构定义
  // 当前AI意图的参数情况
  const currentIntentParams: CreateReminderIntentParams = {
    type: 'CREATE_REMINDER',
  };
  const chatHistories: AIMessageVO[] = [
    {
      id: generateNanoID('aim'),
      role: 'assistant',
      parts: [
        {
          type: 'text',
          text: 'Hello，你想创建什么东西呀？',
        },
      ],
    },
    {
      id: generateNanoID('aim'),
      role: 'user',
      parts: [
        {
          type: 'text',
          text: ' 明天早上7点喊我起床! (Intent: CREATE_REMINDER)',
        },
      ],
    },
  ];

  // 获取AI prompt，用于填充意图，传入聊天信息、当前意图参数、意图结构
  const prompt = await PromptsConfig.resolveAIIntent(
    CreateReminderIntentParamsSchema,
    currentIntentParams,
    chatHistories,
    'zh-CN',
  );
  // console.log('AI Intent Filling Test Prompt:', prompt);

  expect(prompt).toContain('Hello，你想创建什么东西呀？');

  if (TEST_AI) {
    const retJSON = await IntentSO.aiFillIntentParams(
      CreateReminderIntentParamsSchema,
      currentIntentParams,
      chatHistories,
      'zh-CN',
    );
    // 通常返回
    // {
    //   "aiMessage": "好的，已为您创建了一个提醒，明天七点起床",
    //   "intent": {
    //     "type": "CREATE_REMINDER",
    //     "name": "起床",
    //     "datetime": "2022-01-01T07:00:00"
    //   }
    // }

    console.log('意图ret json: ', retJSON);
    expect(retJSON.aiMessage.length).toBeGreaterThan(1); // 有返回信息

    const createReminderIntent = retJSON.intent as CreateReminderIntentParams;
    expect(createReminderIntent.type).toBe('CREATE_REMINDER');
    console.log('[CREATE_REMINDER result]', createReminderIntent);
    expect(typeof createReminderIntent.name).toBe('string'); // 之前是返回“起床”，返回字符串就不错了

    expect(typeof createReminderIntent.datetime).toBe('string');
  }
});
