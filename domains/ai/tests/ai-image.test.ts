import fs from 'fs';
import path from 'path';
import { test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock/mock.context';
// import { AIIntegrationChatSO } from '@bika/domains/ai/server/integration/ai-integration-chat-so';
import { AttachmentSO } from '../../attachment/server/attachment-so';
import { AISO } from '../server/ai-so';

const TEST_AI_IMAGE = process.env.TEST_AI_IMAGE && process.env.TEST_AI_IMAGE === 'true';
test('Test AI Image Test', async () => {
  if (!TEST_AI_IMAGE) return;

  const images = await AISO.generateImages({
    imageModel: 'openai/dall-e-3',
    prompt: 'A beautiful landscape with mountains and a river',
  });
  expect(images).toBeDefined();

  for (const image of images) {
    //   const imageB64 = image.base64
    const bytes = image.uint8Array;

    // write a local test file with bytes

    const testDir = path.join(__dirname, 'test-outputs');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir);
    }

    const filename = `test-image-${Date.now()}.png`;
    const filepath = path.join(testDir, filename);

    fs.writeFileSync(filepath, bytes);
    console.log(`Test image written to: ${filepath}`);
  }

  //   const user = await MockContext.createUser();
  //   const mockRequestContext = MockContext.createMockRequestContext(user);
  //   const chat = await AIIntegrationChatSO.getChat(user.id, 'testing', 'TELEGRAM');
  //   const res1 = await chat.chat(mockRequestContext, 'Please tell me: 1+1=?');
  //   console.log(res1.message);
  //   expect(res1.message).toContain(2);
});

test('Test AI Image Attachments Test', async () => {
  if (!TEST_AI_IMAGE) return;
  const { space } = await MockContext.initUserContext();
  const coinsAccount = await space.billing.getCoinsAccount();

  const attachs = await AttachmentSO.generateImages(
    {
      prompt: 'A beautiful landscape with beautiful girls and boys',
      type: 'node-resource-icon',
      // imageModel: 'dall-e-3',
    },
    coinsAccount,
  );

  for (const attach of attachs) {
    console.log(attach.fullPath);
    expect(attach).toBeDefined();
  }
});
