import { FileType, isWhatFileType, renderFileIconUrl } from '@bika/ui/file';

export const file2Base64 = async (file: File) =>
  new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      resolve(reader.result as string);
    };
  });

export const getAttachmentDisplayPath = (attachment: { name?: string; mimeType?: string; url: string }) => {
  const fileName = attachment.name || 'attachment';
  const mimeType = attachment.mimeType || 'application/octet-stream';

  // Determine file type using the utility function
  const fileType = isWhatFileType({ name: fileName, type: mimeType });

  switch (fileType) {
    case FileType.Image: {
      // For images, remove query parameters from URL for cleaner display
      const urlIndex = attachment.url.indexOf('?');
      return urlIndex === -1 ? attachment.url : attachment.url.slice(0, urlIndex);
    }
    default: {
      // For non-images, use the appropriate file type icon
      return renderFileIconUrl({ name: fileName, type: mimeType });
    }
  }
};
