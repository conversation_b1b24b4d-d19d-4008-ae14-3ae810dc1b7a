import { z } from 'zod';
import { AIMessageAnnotationSchema, type AIMessageAnnotation } from '@bika/types/ai/bo';
import type { AIChatContextVO } from '@bika/types/ai/vo';
import { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { UserVO } from '@bika/types/user/vo';

export class AIMessageAnnotations {
  private _msgAnnotations: AIMessageAnnotation[] | undefined;

  constructor(private readonly annotations: AIMessageAnnotation[] | undefined) {
    this._msgAnnotations = annotations;
  }

  public get skillsets(): SkillsetSelectDTO[] {
    return AIMessageAnnotations.parseSkillsets(this._msgAnnotations);
  }

  public get creator(): UserVO | undefined {
    return AIMessageAnnotations.parseCreator(this._msgAnnotations);
  }

  public get contexts(): AIChatContextVO[] {
    return AIMessageAnnotations.parseContexts(this._msgAnnotations);
  }

  static parseContexts(msgAnnotations: AIMessageAnnotation[] | undefined): AIChatContextVO[] {
    if (msgAnnotations) {
      const anos = z.array(AIMessageAnnotationSchema).parse(msgAnnotations);
      for (const d of anos) {
        if (d.type === 'contexts') {
          const contexts = d.contexts;
          return contexts;
        }
      }
    }
    return [];
  }

  /**
   *
   * Fetch SkillsetsSelects from message annotations
   *
   * @param msgAnnotations
   * @returns
   */
  static parseSkillsets(msgAnnotations: AIMessageAnnotation[] | undefined): SkillsetSelectDTO[] {
    if (msgAnnotations) {
      const anos = z.array(AIMessageAnnotationSchema).parse(msgAnnotations);
      for (const d of anos) {
        if (d.type === 'skillsets') {
          const skillsets = d.skillsets;
          return [{ kind: 'preset', key: 'default' }, ...skillsets];
        }
      }
    }
    return [{ kind: 'preset', key: 'default' }];
  }

  static parseCreator(msgAnnotations: AIMessageAnnotation[] | undefined) {
    if (msgAnnotations) {
      const anos = z.array(AIMessageAnnotationSchema).parse(msgAnnotations);
      for (const d of anos) {
        if (d.type === 'creator') {
          return d.creator;
        }
      }
    }
    return undefined;
  }
}
