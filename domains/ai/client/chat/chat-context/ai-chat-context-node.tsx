import assert from 'assert';
import { Box } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { EllipsisText } from '@bika/ui/text/ellipsis';
import { Typography } from '@bika/ui/texts';
import type { AIChatContextRendererProps } from './ai-chat-context';

export function AIChatContextNode(props: AIChatContextRendererProps) {
  const { value } = props;
  assert(value.type === 'node', 'AIChatContextNode only supports node type for now');

  return (
    <>
      <Box className="flex items-center gap-1 ">
        <div className="flex items-center gap-1 px-[16px] py-[8px] text-[--text-disabled] space-x-2 border border-[--border-default] rounded-lg h-[32px] cursor-pointer">
          {value.node.type && (
            <NodeIcon
              value={{ kind: 'node-resource', nodeType: value.node.type, customIcon: value.node.icon ?? undefined }}
              size={16}
              color="var(--text-disabled)"
            />
          )}

          <EllipsisText>
            <Typography level="b3" textColor="var(--text-secondary)">
              {value.node.name}
            </Typography>
          </EllipsisText>
        </div>
      </Box>
    </>
  );
}
