import assert from 'assert';
import { useGlobalContext } from '@bika/types/website/context';
import type { AIChatContextRendererProps } from './ai-chat-context';
import { AttachmentPreview } from '../attachment/attachment-preview';
import type { PreviewAttachment } from '../attachment/attachment-preview';

export function AIChatContextAttachment(props: AIChatContextRendererProps) {
  const globalContext = useGlobalContext();
  const { value } = props;
  assert(value.type === 'attachment', 'AIChatContextAttachment only supports attachment type for now');
  const attachmentVO = value.attachment;

  const filePublicUrl = globalContext.servers.storagePublicUrl ?? '';
  const fileUrl = `${filePublicUrl}/${attachmentVO.path}`;

  // AttachmentVO 转 PreviewAttachment
  const value2: PreviewAttachment = {
    ...attachmentVO,
    url: fileUrl,
  };

  return (
    <div className="flex items-center gap-1 px-[16px] py-[8px] text-[--text-disabled] space-x-2 border border-[--border-default] rounded-lg h-[32px] cursor-pointer">
      <AttachmentPreview
        data={{
          type: 'preview',
          attachment: value2,
        }}
        onClickDelete={(attachData) => {
          assert(attachData.type === 'preview', 'AttachmentPreview onClickDelete should only handle preview type');
          props.onClickDelete?.({ type: 'attachment', attachment: attachData.attachment });
        }}
      />
    </div>
  );
}
