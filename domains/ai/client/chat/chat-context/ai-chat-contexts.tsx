import type { AIChatContextVO } from '@bika/types/ai/vo';
import { AIChatContextRenderer } from './ai-chat-context';

interface Props {
  value: AIChatContextVO[];
  onClickDelete?: (value: AIChatContextVO) => void;
}
export function AIChatContexts(props: Props) {
  return (
    <>
      {props.value.map((context, index) => (
        <AIChatContextRenderer key={index} value={context} onClickDelete={props.onClickDelete} />
      ))}
    </>
  );
}
