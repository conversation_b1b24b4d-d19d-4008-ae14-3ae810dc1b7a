import type { AIChatContextVO } from '@bika/types/ai/vo';
import { AIChatContextAttachment } from './ai-chat-context-attachment';
import { AIChatContextNode } from './ai-chat-context-node';

export interface AIChatContextRendererProps {
  value: AIChatContextVO;
  onClickDelete?: (value: AIChatContextVO) => void;
}

export function AIChatContextRenderer(props: AIChatContextRendererProps) {
  const { value } = props;

  const t = value.type;

  let com;
  if (value.type === 'node') {
    com = <AIChatContextNode value={value} />;
  } else if (value.type === 'attachment') {
    com = <AIChatContextAttachment value={value} onClickDelete={props.onClickDelete} />;
  } else {
    throw new Error(`Unsupported AIChatContextVO type: ${t}`);
  }

  return <>{com}</>;
}
