import React from 'react';
import type { AIChatOption } from '@bika/types/ai/vo';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';

interface Props {
  options: AIChatOption[];
  initOption?: AIChatOption;
  // setOption: (option: AIChatOption) => void;
  // current: AIChatOption;
}

interface IHandle {
  option: string | undefined;
}
export function AIChatInputOptions(props: Props, ref: React.Ref<IHandle>) {
  const [option, setOption] = React.useState<string | undefined>(() => props.options?.[0]?.value);

  React.useImperativeHandle(ref, () => ({
    option,
  }));
  return (
    <>
      <SelectInput
        hideSearch
        label=""
        disabled={props.options.filter((_option) => !_option.disabled).length < 2}
        value={option || null}
        onChange={(newOpt) => {
          if (newOpt) setOption(newOpt);
        }}
        options={props.options}
        classes={{
          joySelect: {
            height: '32px',
            minHeight: '32px',
          },
        }}
        sx={{
          width: '100px',
          height: '32px',
          '& > .MuiSelect-select': {
            padding: '6px 14px',
          },
        }}
      />
    </>
  );
}
