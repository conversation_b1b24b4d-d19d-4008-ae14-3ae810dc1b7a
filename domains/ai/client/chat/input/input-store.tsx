import { create } from 'zustand';
import { IInputStore } from './types';

export const useInputStore = create<IInputStore>((set, get) => ({
  attachments: [],
  setAttachments: (attachments) => {
    if (typeof attachments === 'function') {
      set({ attachments: attachments(get().attachments) });
    } else {
      set({ attachments });
    }
  },
  clearAttachments: () => set({ attachments: [] }),
}));
