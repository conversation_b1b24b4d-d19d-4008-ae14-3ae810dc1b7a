import React from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import { useGlobalContext } from '@bika/types/website/context';
import { IconButton } from '@bika/ui/button-component';
import EyeOpenOutlined from '@bika/ui/icons/components/eye_open_outlined';
import { Stack } from '@bika/ui/layouts';
import { triggerPreviewAttachment } from '@bika/ui/preview-attachment/preivew-attachment';
import { AttachmentCard } from './attachment-card';
import { getAttachmentDisplayPath } from '../utils/utils';

export interface AttachmentRendererProps {
  attachment: AttachmentVO;
  className?: string;
}

export function MessageAttachmentRenderer({ attachment, className }: AttachmentRendererProps) {
  const globalContext = useGlobalContext();
  const fileName = attachment.name || 'attachment';
  const mimeType = attachment.mimeType || 'application/octet-stream';
  const attachmentUrl = `${globalContext.servers.storagePublicUrl}/${attachment.path}`;
  const displayPath = getAttachmentDisplayPath({
    name: fileName,
    mimeType,
    url: attachmentUrl,
  });

  const { t } = useLocale();

  const triggerPreviewLogic = () => {
    // Convert attachment data to the format expected by triggerPreviewKFile
    const attachmentForPreview = {
      name: attachment.name || 'attachment',
      contentType: attachment.mimeType,
      url: attachmentUrl,
      variant: 'kkfile' as const,
    };

    triggerPreviewAttachment({
      index: 0,
      attachments: [attachmentForPreview],
      t,
    });
  };

  return (
    <Stack
      onClick={triggerPreviewLogic}
      direction={'row'}
      className={className}
      alignItems={'center'}
      sx={{
        maxWidth: '400px',
        minHeight: '64px',
        overflowX: 'hidden',
        borderRadius: '4px',
        boxSizing: 'border-box',
        paddingRight: '8px',
        background: 'var(--bg-controls)',
        border: '1px solid var(--border-default)',
      }}
    >
      <AttachmentCard id={fileName} name={fileName} contentType={mimeType} imageSrc={displayPath} />
      <IconButton
        // onClick={triggerPreviewLogic}
        sx={{
          flex: '0 0 none',
        }}
      >
        <EyeOpenOutlined color="var(--text-secondary)" />
      </IconButton>
    </Stack>
  );
}
export { getAttachmentDisplayPath };
