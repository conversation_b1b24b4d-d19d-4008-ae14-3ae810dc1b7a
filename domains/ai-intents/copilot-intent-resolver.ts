import assert from 'assert';
import type { DataStreamWriter, ToolSet } from 'ai';
import _ from 'lodash';
import { getAIPageEditCopilotPrompt } from '@bika/contents/config/server/ai-prompts/ai-copilot/ai-page-edit.prompt';
import { getAutomationCopilotAskPrompt } from '@bika/contents/config/server/ai-prompts/ai-copilot/automation-ask.prompt';
import { getDatabaseCopilotAskPrompt } from '@bika/contents/config/server/ai-prompts/ai-copilot/database-ask.prompt';
import { getBaseNodeCopilotContextPrompt } from '@bika/contents/config/server/ai-prompts/ai-copilot/node-context.prompt';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { IStreamChatPrompt, ResolutionResultInfo } from '@bika/domains/ai/server/types';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { CopilotIntentParams, AIMessageBO, AIIntentParams } from '@bika/types/ai/bo';
import type { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { ResolutionMessage, AIChatOption, AIResolveVO } from '@bika/types/ai/vo';
import { CONST_PREFIX_DAT } from '@bika/types/database/vo';
import { iString, iStringParse } from '@bika/types/i18n/bo';
import { type SkillsetSelectDTO } from '@bika/types/skill/dto';
import { AIIntentResolver } from './resolvers';
import { AutomationSkillsetName } from '../ai-skillset/bika-automation/type';
import { DatabaseSkillsetName } from '../ai-skillset/bika-database/type';
import { NodeSkillsetName, UnitSkillsetName, UserSkillsetName } from '../ai-skillset/bika-space/types';

export class CopilotAIIntentResolver extends AIIntentResolver<CopilotIntentParams> {
  get operators(): Record<string, iString> {
    return {
      explain: {
        en: 'Explain this resource',
        'zh-CN': '解释这个资源',
        'zh-TW': '解釋這個資源',
        ja: 'このリソースを説明してください',
      },
      create_record: {
        en: 'Create a new record',
        'zh-CN': '创建一个新记录',
        'zh-TW': '創建一個新記錄',
        ja: '新しいレコードを作成してください',
      },
      search_records: {
        en: 'Search records',
        'zh-CN': '查找记录',
        'zh-TW': '查找記錄',
        ja: 'レコードを検索してください',
      },
      openapi: {
        en: 'openapi',
        'zh-CN': 'openapi',
        'zh-TW': 'openapi',
        ja: 'openapi',
      },
    };
  }

  async onInit(): Promise<boolean> {
    return true;
  }

  async getPrologue(): Promise<ResolutionMessage> {
    const locale = this.intentSO.user.locale || 'en';
    return {
      // role: 'assistant',
      parts: [
        {
          type: 'text',
          text: iStringParse(
            {
              en: 'Hi, I am your Copilot, ready to help you anytime!',
              'zh-CN': '你好，我是你的助手，随时为你提供帮助！',
              'zh-TW': '你好，我是你的助手，隨時為你提供幫助！',
              ja: 'こんにちは、私はあなたの助手です。いつでもあなたを助けます！',
            },
            locale,
          ),
        },
      ],
      prompts: this.getPrompts(),
    };
  }

  async getOptions(): Promise<AIChatOption[]> {
    assert(this.intentParams.copilot && this.intentParams.copilot.type === 'node', 'Copilot must be a node type');
    const nodeId = this.intentParams.copilot.nodeId;
    const nodeSO = await NodeSO.init(nodeId);
    assert(nodeSO, `NodeSO not found: ${nodeId}`);

    if (nodeSO.type === 'PAGE') {
      return [
        {
          label: 'Edit', // Default
          value: 'edit',
        },
        {
          label: 'Ask',
          value: 'ask',
        },
      ];
    }

    return [
      {
        label: 'Ask',
        value: 'ask',
      },
      {
        label: 'Edit',
        value: 'edit',
        disabled: true, // Page 开放 Edit
      },
    ];
  }

  async doResolve(
    resolver: AIResolveVO,
    user: UserSO,
    chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    const prompt = await this.parsePrompt(resolver, chatHistories, user);
    const { message, usage } = await AISO.streamChat(
      // chatHistories,
      prompt,
      {
        dataStreamWriter,
        model: this.getModel(resolver),
        onFinish: (_data) => {},
        onChunk: (_chunk) => {},
        onError: (_error) => {
          // Has handled this error in the parent component (apps/web/src/app/api/ai/chat/route.tsx)
          console.log('copilot-call-error', _error);
          // // cover the real error message
          // if (dataStreamWriter) {
          //   dataStreamWriter.write(`3:"sorry, something went wrong"\n`);
          // }
        },
      },
      {
        returnPrompts: this.getPrompts(),
      },
    );
    return {
      intentParam: this.intentParams,
      status: 'DIALOG',
      message,
      usage,
    };
  }

  async doComplete(): Promise<{ nextIntent: AIIntentParams | null }> {
    return {
      nextIntent: null,
    };
  }

  private isDatabaseCopilot(): boolean {
    assert(this.intentParams.copilot && this.intentParams.copilot.type === 'node', 'Copilot must be a node type');
    const nodeId = this.intentParams.copilot.nodeId;
    return nodeId.startsWith(CONST_PREFIX_DAT) || false;
  }

  /**
   * override getPrompts
   */
  protected getPrompts(): string[] {
    const locale = this.intentSO.user.locale || 'en';
    const prompts: string[] = [iStringParse(this.operators.explain, locale)];
    if (this.isDatabaseCopilot()) {
      prompts.push(iStringParse(this.operators.search_records, locale));
    }
    return prompts;
  }

  private async parsePrompt(
    resolver: AIResolveVO,
    chatHistories: AIMessageBO[],
    user: UserSO,
  ): Promise<IStreamChatPrompt> {
    assert(resolver.type === 'MESSAGE' || resolver.type === 'TOOL', 'resolver.type must be MESSAGE or TOOL');
    const tools: ToolSet = {};
    assert(this.intentParams.copilot && this.intentParams.copilot!.type === 'node', 'Copilot must be a node type');
    const nodeId = this.intentParams.copilot!.nodeId;
    const locale = user.locale;
    const nodeSO = await NodeSO.init(nodeId);

    let skillsets: SkillsetSelectDTO[] = [];

    let systemPrompt = getBaseNodeCopilotContextPrompt({ nodeId });
    if (nodeSO.type === 'PAGE') {
      // resolver.option === 'edit' &&
      systemPrompt = getAIPageEditCopilotPrompt({ nodeId });
      skillsets = [{ kind: 'preset', key: 'bika-ai-page' }]; // ['brave-search']; // 屏蔽 brave-search 在本地，被中国墙了
      if (resolver.type === 'MESSAGE' && Object.values(this.operators.explain).includes(resolver.message!.trim())) {
        skillsets.push({ kind: 'preset', key: 'bika-space', includes: [NodeSkillsetName.get_node_info] });
      }
    } else if (nodeSO.type === 'AUTOMATION') {
      systemPrompt = getAutomationCopilotAskPrompt({ nodeId, userId: user.id, locale });
      skillsets = [
        { kind: 'preset', key: 'bika-automation', includes: [AutomationSkillsetName.get_automation_detail] },
      ];
    } else if (nodeSO.type === 'DATABASE') {
      systemPrompt = getDatabaseCopilotAskPrompt({ nodeId, userId: user.id });
      const databaseSkillset: string[] = [
        DatabaseSkillsetName.list_records,
        DatabaseSkillsetName.get_database_detail,
        DatabaseSkillsetName.get_fields_schema,
        DatabaseSkillsetName.aggregate_records,
      ];

      if (resolver.option === 'edit') {
        databaseSkillset.push(DatabaseSkillsetName.create_record);
      }

      skillsets = [
        {
          kind: 'preset',
          key: 'bika-database',
          includes: databaseSkillset,
        },
        {
          kind: 'preset',
          key: 'bika-space',
          includes: [
            UserSkillsetName.list_users,
            UnitSkillsetName.list_members,
            UnitSkillsetName.list_teams,
            UnitSkillsetName.list_roles,
          ],
        },
        // {
        //   kind: 'preset',
        //   key: 'unit',
        //   includes: [],
        // },
      ];
    } else {
      skillsets = [{ kind: 'preset', key: 'bika-space', includes: [NodeSkillsetName.get_node_info] }];
    }
    // 过滤掉assistant的tool-invocation部分

    const messages = _.cloneDeep(chatHistories.slice(-4));
    return {
      messages,
      system: systemPrompt,
      skillsets,
      tools,
      user, // use for toolset
      maxSteps: 10,
    };
  }

  // private async getMessages(resolver: AIResolveVO, chatHistories: AIMessageBO[]): Promise<AIMessageBO[]> {
  //   const messages: CoreMessage[] = this.multiTurnChatMessage(chatHistories, 10);
  //   const fileParts = await this.parseAttachment(resolver);
  //   if (fileParts) {
  //     messages.push({ role: 'user', content: fileParts });
  //   }
  //   const toolMessages = this.parseToolMessage(resolver);
  //   if (toolMessages) {
  //     messages.push(...toolMessages);
  //   }
  //   return messages;
  // }

  private getModel(resolver: AIResolveVO): PresetLanguageAIModelDef | undefined {
    if (resolver.type === 'MESSAGE') {
      if (resolver.option === 'edit') {
        return (process.env.COPILOT_EDIT_AI_MODEL || 'openai/gpt-4.1') as PresetLanguageAIModelDef;
      }
      return (process.env.COPILOT_ASK_AI_MODEL || 'qwen/qwen-plus') as PresetLanguageAIModelDef;
    }
    // gtp best for tool
    if (resolver.type === 'TOOL') {
      return (process.env.COPILOT_EDIT_AI_MODEL || 'openai/gpt-4.1') as PresetLanguageAIModelDef;
    }
    return undefined;
  }
}
