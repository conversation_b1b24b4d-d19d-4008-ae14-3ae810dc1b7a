import type { DataStreamWriter } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { PromptsConfig } from '@bika/contents/config/server';
import { Locale } from '@bika/contents/i18n/config';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { ResolutionResultInfo } from '@bika/domains/ai/server/types';
import { ChatIntentParamsBO, AIIntentParams, AIMessageBO } from '@bika/types/ai/bo';
import { ResolutionMessage, IntentResolutionStatus, AIMessageVO, AIResolveVO } from '@bika/types/ai/vo';
import { AIIntentResolver } from './resolvers';
import type { UserSO } from '../user/server/user-so';

export class ChatAIIntentResolver extends AIIntentResolver<ChatIntentParamsBO> {
  async onInit(): Promise<boolean> {
    return true;
  }

  async getPrologue(): Promise<ResolutionMessage> {
    return {
      // role: 'assistant',
      parts: [
        {
          type: 'text',
          text: 'Hello hello',
        },
      ],
      text: 'Hello hello',
      prompts: await this.getPrompts('en'),
    };
  }

  static async AIChat(humanLocale: Locale, chatHistories: AIMessageVO[]) {
    const prompt = await PromptsConfig.chat(chatHistories, humanLocale);
    const unitTestMode = process.env.NODE_ENV === 'test';
    if (unitTestMode) console.log(prompt);

    const response = (await AISO.systemInvokeJSON(prompt)) as PromptsConfig.AIChatResponse;
    return response;
  }

  async doResolve(
    resolver: AIResolveVO,
    user: UserSO,
    chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    const resolverType = resolver.type;
    if (resolverType === 'MESSAGE') {
      const { message, usage } = await AISO.streamChat(
        // chatHistories,
        {
          user,
          messages: [
            ...chatHistories,
            {
              id: generateNanoID('aium-chat-'),
              role: 'user',
              parts: [
                {
                  type: 'text',
                  text: resolver.message,
                },
              ],
            },
          ],
          // prompt: resolver.message,
          // messages: chatHistories,
        },
        {},
        {},
      );

      // const response = await ChatAIIntentResolver.AIChat(userInfo.locale, chatHistories);
      // const newIntentParams = this.intentParams;
      const resolutionStatus: IntentResolutionStatus = 'NEEDS_DISAMBIGUATION';
      // if (response.nextIntent && response.nextIntent !== 'CHAT') {
      //   // 意图成功识别，直接进行切换！
      //   newIntentParams.nextIntent = response.nextIntent;
      //   resolutionStatus = 'SUCCESS'; // 不需要确认CONFIRMATION，直接跳到下一个意图
      // }

      return {
        intentParam: this.intentParams,
        status: resolutionStatus,
        message,
        usage,
      };
    }
    // 不支持UI操作
    throw new Error('Chat AI Intent can be message resolve only');
  }

  /**
   * 完成! 创建新意图
   */
  async doComplete(): Promise<{ nextIntent: AIIntentParams | null }> {
    const oldIntentPO = this._intentSO.model as unknown as ChatIntentParamsBO;
    // Chat意图的终点是切换到下一个行为意图
    const nextIntent = oldIntentPO.nextIntent || 'CHAT';
    const newIntentPO: AIIntentParams = {
      type: nextIntent,
    };

    return {
      nextIntent: newIntentPO,
    };
  }

  /**
   * 获取UI发到客户端，根据当前的解决状态和参数填写情况而定
   * @returns
   */
  // async parseUI(): Promise<AIIntentUIVO> {
  //   return this.defaultPromptUI();
  // }
}
