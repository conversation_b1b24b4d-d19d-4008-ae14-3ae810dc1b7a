import type { DataStreamWriter } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { ResolutionResultInfo } from '@bika/domains/ai/server/types';
import { ChatIntentParamsBO, AIIntentParams, AIMessageBO, AIUsage } from '@bika/types/ai/bo';
import { ResolutionMessage, AIResolveVO } from '@bika/types/ai/vo';
import { AIIntentResolver } from './resolvers';
import type { UserSO } from '../user/server/user-so';

export class DebuggerAIIntentResolver extends AIIntentResolver<ChatIntentParamsBO> {
  async onInit(): Promise<boolean> {
    return true;
  }

  async getPrologue(): Promise<ResolutionMessage> {
    return {
      // role: 'assistant',
      parts: [
        {
          type: 'text',
          text: 'I am your super assistant, how can I help you?',
        },
      ],
      // text: 'Hello hello',
      prompts: await this.getPrompts('en'),
    };
  }

  //  参考 mult agent https://sdk.vercel.ai/docs/ai-sdk-ui/chatbot-tool-usage
  async doResolve(
    resolver: AIResolveVO,
    user: UserSO,
    chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    const resolverType = resolver.type;

    if (resolverType === 'MESSAGE') {
      // let result: StreamTextResult<ToolSet, never>;
      let message: AIMessageBO;
      let usage: AIUsage;

      if (resolver.option === 'weather') {
        const r = await DebuggerAIIntentResolver.testWeather(
          chatHistories,
          user,
          resolver.message,
          undefined,
          dataStreamWriter,
        );
        message = r.message;
        usage = r.usage;
      } else {
        const r = await DebuggerAIIntentResolver.superAsk(
          chatHistories,
          user,
          resolver.message,
          undefined,
          dataStreamWriter,
        );
        message = r.message;
        usage = r.usage;
      }

      return {
        intentParam: this.intentParams,
        status: 'NEEDS_DISAMBIGUATION',
        message,
        usage,
      };
      // return;
    }
    // 不支持UI操作
    throw new Error('Chat AI Intent can be message resolve only');
  }

  /**
   * 完成! 创建新意图
   */
  async doComplete(): Promise<{ nextIntent: AIIntentParams | null }> {
    const oldIntentPO = this._intentSO.model as unknown as ChatIntentParamsBO;
    // Chat意图的终点是切换到下一个行为意图
    const nextIntent = oldIntentPO.nextIntent || 'CHAT';
    const newIntentPO: AIIntentParams = {
      type: nextIntent,
    };

    return {
      nextIntent: newIntentPO,
    };
  }

  /**
   * 获取UI发到客户端，根据当前的解决状态和参数填写情况而定
   * @returns
   */
  // async parseUI(): Promise<AIIntentUIVO> {
  //   return this.defaultPromptUI();
  // }

  public static async createDatabase(
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      {
        user,
        system: 'You are Bika.ai Super Assistant',
        messages: [
          {
            id: generateNanoID('aium-create-database-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        // prompt: userPrompt,
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'bika-database', includes: ['create_database'] }],
      },
      {
        // model: 'ark/DeepSeek-V3',
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }

  public static async createAutomation(
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      {
        user,
        system: 'You are Bika.ai Super Assistant',
        // prompt: userPrompt,
        messages: [
          {
            id: generateNanoID('aium-create-automation-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'bika-automation', includes: ['create_automation'] }],
      },
      {
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }

  public static async superAsk(
    history: AIMessageBO[],
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      // history,
      {
        user,
        system: 'You are Bika.ai Super Assistant',
        messages: [
          ...history,
          {
            id: generateNanoID('aium-super-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        // prompt: userPrompt,
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'bika-space' }],
      },
      {
        // model: 'ark/DeepSeek-V3',
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }

  /**
   * 获取UI发到客户端，根据当前的解决状态和参数填写情况而定
   * @returns
   */
  // async parseUI(): Promise<AIIntentUIVO> {
  //   return this.defaultPromptUI();
  // }

  public static async testWeather(
    history: AIMessageBO[],
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      {
        user,
        system: '你是天气报告专家，你在跟用户对话，请注意通俗易懂，不要用技术名词',
        messages: [
          ...history,
          {
            id: generateNanoID('aium-test-weather-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        // prompt: userPrompt,
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'debug' }],
      },
      {
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }
}
