import { SurveySO } from '@bika/domains/admin/server/survey-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { SpaceOnboarding } from '@bika/domains/space/server/space-onboarding';
import { UserSO } from '@bika/domains/user/server/user-so';
import { QuestionaireAIIntentUIResolveDTOSchema, StepWizardConfig } from '@bika/types/ai/bo';
import { AIIntentResolverState } from '@bika/types/ai/vo';
import { getAppEnv } from 'sharelib/app-env';
import { ApiFetchRequestContext } from '@bika/types/user/vo';

export const OnboardingTrialStepWizardConfig: StepWizardConfig = {
  name: {
    'zh-CN': '免费试用高级功能',
    en: 'Free Trial Advanced Features',
    ja: '無料トライアル高度な機能',
    'zh-TW': '免費試用高級功能',
  },
  steps: [
    {
      message: {
        'zh-CN': '免费试用一下我们的高级版本吧！',
        en: 'Try our premium version for free!',
        ja: 'プレミアムバージョンを無料でお試しください！',
        'zh-TW': '免費試用一下我們的高級版本吧！',
      },
      staticVoice: {
        'zh-CN': '/assets/audios/zh-CN/onboarding-init-5.mp3',
        en: '/assets/audios/en/onboarding-init-5.mp3',
        ja: '/assets/audios/ja/onboarding-init-5.mp3',
        'zh-TW': '/assets/audios/zh-CN/onboarding-init-5.mp3',
      },
      async ui() {
        return {
          type: 'BILLING',
          title: 'TODO: 这里嵌入一个广告位，显示免费使用的Intent UI',
          hiddenInput: true,
          hiddenClosable: true,
        };
      },
      async uiResolve(uiResolveObj, stepWizardParams, userInfo) {
        const userSO = await UserSO.init(userInfo.userId);
        const nextStep = userSO.settings.questionaire ? 10 : true; // 如果有绑定账号，跳到下一步，否则继续，直接结束本次step
        stepWizardParams.confirm = true;
        return {
          stepWizardParams,
          nextStep,
        };
      },
      async startCondition(intentResolver, stepWizardParams) {
        const memberId = intentResolver.unitId;
        const member = await MemberSO.init(memberId);
        const space = await member.getSpace();
        const entitlement = await space.getEntitlement();
        const subscription = entitlement.getSubscription();
        if (subscription === null) {
          return {
            stepWizardParams,
            start: !stepWizardParams.confirm,
          };
        }
        return {
          stepWizardParams,
          start: !subscription,
        };
      },
    },
    {
      message: {
        'zh-CN': '请告诉我们更多关于您的信息，以帮助改进您的使用体验和个性化服务。',
        en: 'Tell us more about you to improve your experience.',
        ja: 'あなたについてもっと教えてください。あなたの経験を向上させるために。',
        'zh-TW': '請告訴我們更多關於您的信息，以幫助改進您的使用體驗和個性化服務。',
      },
      staticVoice: {
        'zh-CN': '/assets/audios/zh-CN/onboarding-init-6.mp3',
        en: '/assets/audios/en/onboarding-init-6.mp3',
        ja: '/assets/audios/ja/onboarding-init-6.mp3',
        'zh-TW': '/assets/audios/zh-CN/onboarding-init-6.mp3',
      },
      async startCondition(intentResolver, stepWizardParams) {
        const appEnv = getAppEnv();
        console.log('appEnv', intentResolver, stepWizardParams);
        return {
          stepWizardParams,
          start: appEnv !== 'SELF-HOSTED' && !stepWizardParams.questionaire,
        };
      },
      async ui() {
        return {
          hiddenInput: true,
          type: 'QUESTIONAIRE',
          hiddenClosable: true,
        };
      },
      async uiResolve(uiResolveObj, stepWizardParams, userInfo) {
        const questionaireResolve = QuestionaireAIIntentUIResolveDTOSchema.parse(uiResolveObj);

        const userSO = await UserSO.init(userInfo.userId);
        const userVO = await userSO.toVO();
        stepWizardParams.questionaire = true;

        await SurveySO.create({
          type: 'QUESTIONAIRE',
          userId: userInfo.userId,
          dataRow: {
            USER_ID: userInfo.userId,
            INDUSTRY_JOB: questionaireResolve.industryJob,
            USE_CASE: questionaireResolve.useCase,
            CONTACT: questionaireResolve.contact,
            EMAIL: userSO.model.email || '',
            PHONE: userSO.model.phone || '',
            USER_JSON: JSON.stringify({
              host: process.env.APP_HOSTNAME,
              user: userVO,
            }),
          },
        });

        await userSO.updateSettings({
          questionaire: true,
        });

        return {
          stepWizardParams,
          nextStep: true,
        };
      },
    },
    {
      // 您已订阅高级版，您可以使用所有功能。
      message: {
        'zh-CN': '这是私有化部署版本，您可以尽情使用 Bika.ai',
        en: 'This is a privately deployed version, you can freely use Bika.ai',
        ja: 'これはプライベートデプロイメントバージョンです。Bika.aiを思う存分ご利用いただけます。',
        'zh-TW': '這是私有化部署版本，您可以盡情使用 Bika.ai',
      },
      async startCondition(intentResolver, stepWizardParams) {
        const appEnv = getAppEnv();
        return {
          stepWizardParams,
          start: appEnv === 'SELF-HOSTED' && !stepWizardParams.confirmAlert,
        };
      },
      async ui() {
        return {
          type: 'CONFIRM',
          hiddenInput: true,
          hiddenClosable: true,
          yesOnly: true,
        };
      },
      async uiResolve(_uiResolveObj, stepWizardParams) {
        const appEnv = getAppEnv();
        stepWizardParams.confirmAlert = true;
        return {
          stepWizardParams,
          nextStep: appEnv === 'SELF-HOSTED' ? true : 20,
        };
      },
    },
  ],

  async successUI() {
    return {
      type: 'CONFIRM',
      title: {
        'zh-CN': `恭喜你完成了所有步骤！`,
        en: `Congratulations! You have completed all steps!`,
        ja: `おめでとうございます！すべてのステップが完了しました！`,
        'zh-TW': `恭喜你完成了所有步驟！`,
      },
      yesOnly: true,
    };
  },

  async doComplete(stepWizardParams, intentResolver: AIIntentResolverState, ctx: ApiFetchRequestContext) {
    const memberId = intentResolver.unitId;
    const member = await MemberSO.init(memberId);

    await SpaceOnboarding.onboarding(member, 'ONBOARDING_TRIAL', stepWizardParams, ctx);

    return {
      nextIntent: null,
    };
  },
};
