/* eslint-disable max-classes-per-file */
import assert from 'assert';
import { FilePart, ImagePart, type DataContent, type DataStreamWriter } from 'ai';
import _ from 'lodash';
import { storagePublicUrl, getAppEnv, isInCI } from 'sharelib/app-env';
import { getAIIntentTypesConfig } from '@bika/contents/config/client/ai/wizard';
import { Locale } from '@bika/contents/i18n/config';
import { getServerLocaleContext } from '@bika/contents/i18n/server';
import { IntentSO } from '@bika/domains/ai/server/ai-chat/intent-so';
import { ResolutionResultInfo } from '@bika/domains/ai/server/types';
import type { UserSO } from '@bika/domains/user/server/user-so';
import { AIIntentParams, AIMessageBO } from '@bika/types/ai/bo';
import type { AIIntentType } from '@bika/types/ai/bo';
import { AIIntentUIVO, ResolutionMessage } from '@bika/types/ai/vo';
import type { AIResolveVO, AIChatOption } from '@bika/types/ai/vo';
import { iString, iStringParse } from '@bika/types/system';
import { ApiFetchRequestContext } from '@bika/types/user/vo';

export abstract class AIIntentResolver<T extends AIIntentParams> {
  protected _intentSO: IntentSO;

  constructor(intentSO: IntentSO) {
    this._intentSO = intentSO;
  }

  public get intentParams() {
    return this._intentSO.model as T;
  }

  public get intentSO() {
    return this._intentSO;
  }

  get intentType(): AIIntentType {
    return this.intentParams.type;
  }

  getTitle(): iString {
    return this.intentParams.type;
  }

  abstract onInit(): Promise<boolean>;

  /**
   * Resolve是对_intent填充参数的过程，可以文字resolve，也可以UIresolve
   *
   *
   * @param _resolver
   * @param _humanLocale
   * @param _chatHistories
   */
  abstract doResolve(
    _resolver: AIResolveVO,
    user: UserSO,
    _chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo>;

  /**
   * 打招呼, first AI message，可以没有，undefined
   */
  abstract getPrologue(): Promise<ResolutionMessage | undefined>;

  /**
   * 执行具体的Action
   * 如果有返回值，表示意图是下一个意图，resolveMessage会递归执行，跳过去
   */
  abstract doComplete(ctx: ApiFetchRequestContext): Promise<{ nextIntent: AIIntentParams | null; args?: unknown }>;

  protected async defaultSuccessUI(): Promise<AIIntentUIVO> {
    return {
      type: 'CONFIRM',
      title: 'It is done!',
      hiddenInput: true,
      yes: 'End this conversation',
      yesOnly: true,
      yesAction: 'CLOSE',
    };
  }

  /**
   *  读取配置读 prompt，也可以 override 这里变成动态 prompts
   * @param locale
   * @returns
   */
  protected getPrompts(locale: Locale): string[] {
    const lt = getServerLocaleContext(locale);
    const config = getAIIntentTypesConfig(lt)[this.intentType];
    const prompts = config.prompts;
    return prompts.map((p) => iStringParse(p));
  }

  /**
   * 默认Prompt UI
   * @returns
   *
   * @deprecated 改用 annotation(prompts)返回 prompts
   */
  public async defaultPromptUI(): Promise<AIIntentUIVO> {
    return {
      type: 'PROMPT',
      title: 'You can ask me:',
      prompts: _.sampleSize(
        [
          'Create a reminder...',
          'Remind me to have breakfast at 8 AM tomorrow...',
          'What is your favorite food?',
          'What is your favorite color?',
          'What is your favorite travel destination?',
          'What is your favorite movie?',
          'What do you think is the key to success?',
          'Do you like reading? What is your favorite book?',
          'Do you like sports? What is your favorite sport?',
          'What is your favorite season?',
          'Do you like attending social events?',
          'Do you like zoos?',
          'What is your favorite fruit?',
          'Do you prefer working from home or in the office?',
        ],
        5,
      ),
    };
  }

  handling(): boolean {
    return true;
  }

  /**
   *  默认取 intent config 里的 options
   * @returns
   */
  public async getOptions(): Promise<AIChatOption[] | undefined> {
    const localeCtx = getServerLocaleContext('en');
    const options = getAIIntentTypesConfig(localeCtx)[this.intentType].options;
    return options;
  }

  protected async parseAttachment(resolver: AIResolveVO): Promise<Array<ImagePart | FilePart> | undefined> {
    if (resolver.type !== 'MESSAGE' || !resolver.contexts?.length) {
      return undefined;
    }
    const shouldFetch = isInCI() || getAppEnv() === 'LOCAL';

    return Promise.all(
      resolver.contexts.map(async (context) => {
        assert(context.type === 'attachment', `context type must be 'attachment', got ${context.type}`);

        const attachment = context.attachment;
        const attachmentUrl = `${storagePublicUrl()}/${attachment.path}`;
        // Can either be a base64-encoded string, a Uint8Array, an ArrayBuffer, or a Buffe
        let fileData: DataContent | URL = new URL(attachmentUrl);
        if (shouldFetch) {
          const res = await fetch(attachmentUrl);
          const arrayBuffer = await res.arrayBuffer();
          fileData = new Uint8Array(arrayBuffer);
        }
        if (attachment.mimeType?.startsWith('image/')) {
          return {
            type: 'image',
            image: fileData,
            mimeType: attachment.mimeType,
          };
        }
        return {
          type: 'file',
          data: fileData,
          mimeType: attachment.mimeType || 'application/octet-stream',
          name: attachment.name,
        };
      }),
    );
  }

  /**
   * get recent chat messages, exclude the tool-invocation messages
   * @param chatHistories chat histories
   * @param len conversation length, default is 10, user and assistant each one, means 5 turn
   * @returns multi turn chat messages
   */
  // protected multiTurnChatMessage(chatHistories: AIMessageBO[], len: number = 10): CoreMessage[] {
  //   const messages: CoreMessage[] = [];
  //   // 倒序遍历
  //   for (let i = chatHistories.length - 1; i >= 0; i--) {
  //     const message = chatHistories[i];
  //     if (message.role === 'user') {
  //       const content = message.parts
  //         .filter((part): part is { type: 'text'; text: string } => part.type === 'text')
  //         .map((part) => part.text)
  //         .join('');
  //       if (content) {
  //         messages.push({
  //           role: 'user',
  //           content,
  //         });
  //       }
  //     }
  //     if (message.role === 'assistant') {
  //       const content = message.parts
  //         .filter((part): part is { type: 'text'; text: string } => part.type === 'text')
  //         .map((part) => part.text)
  //         .join('');
  //       if (content) {
  //         messages.push({
  //           role: 'assistant',
  //           content,
  //         });
  //       }
  //     }
  //     if (messages.length > len && messages[messages.length - 1].role === 'user') {
  //       break;
  //     }
  //   }
  //   return messages.reverse();
  // }

  // protected parseToolMessage(resolver: AIResolveVO): CoreMessage[] {
  //   const messages: CoreMessage[] = [];
  //   // tool messages
  //   if (resolver.type === 'TOOL' && resolver.uiMessage) {
  //     for (const toolResult of resolver.uiMessage?.toolInvocations || []) {
  //       if (toolResult.state === 'result') {
  //         messages.push({
  //           role: 'assistant',
  //           content: [
  //             {
  //               type: 'tool-call',
  //               toolCallId: toolResult.toolCallId,
  //               toolName: toolResult.toolName,
  //               args: toolResult.args,
  //             },
  //           ],
  //         });
  //         messages.push({
  //           role: 'tool',
  //           content: [
  //             {
  //               type: 'tool-result',
  //               toolCallId: toolResult.toolCallId,
  //               toolName: toolResult.toolName,
  //               result: toolResult.result,
  //             },
  //           ],
  //         });
  //       }
  //     }
  //   }
  //   return messages;
  // }
}
