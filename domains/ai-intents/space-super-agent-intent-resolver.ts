import type { DataStreamWriter } from 'ai';
import { generateNanoID } from 'basenext/utils/nano-id';
import { ChiefOfStaffPrompt } from '@bika/contents/config/server/ai-prompts/chief-of-staff.prompt';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { ResolutionResultInfo, IStreamChatPrompt } from '@bika/domains/ai/server/types';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { AIIntentParams, AIMessageBO, SuperAgentIntentParams } from '@bika/types/ai/bo';
import { AIChatOption, AIResolveVO } from '@bika/types/ai/vo';
import { TalkExpertKeySchema } from '@bika/types/space/bo';
import { AIIntentResolver } from './resolvers';

export class SpaceSuperAgentAIIntentResolver extends AIIntentResolver<SuperAgentIntentParams> {
  async onInit(): Promise<boolean> {
    return true;
  }

  async getOptions(): Promise<AIChatOption[]> {
    return [
      {
        label: 'Chief of Staff',
        value: 'chief-of-staff',
        disabled: false,
      },
    ];
  }

  async getPrologue() {
    return undefined;
  }

  async doResolve(
    _resolver: AIResolveVO,
    user: UserSO,
    chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    const spaceSO = await SpaceSO.init(this.intentParams.spaceId!);
    const prompt = this.parsePrompt(user, spaceSO, chatHistories);
    const { message, usage } = await AISO.streamChat(
      prompt,
      {
        dataStreamWriter,
      },
      {},
    );

    return {
      intentParam: this.intentParams,
      status: 'DIALOG',
      message,
      usage,
    };
  }

  /**
   * 完成! 创建新意图
   */
  async doComplete(): Promise<{ nextIntent: AIIntentParams | null }> {
    return {
      nextIntent: null,
    };
  }

  /**
   * 获取UI发到客户端，根据当前的解决状态和参数填写情况而定
   * @returns
   */
  // async parseUI(): Promise<AIIntentUIVO> {
  //   return this.defaultPromptUI();
  // }

  public static async createDatabase(
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      {
        user,
        system: 'You are Bika.ai Super Assistant',
        messages: [
          {
            id: generateNanoID('aium-create-database-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        // prompt: userPrompt,
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'bika-database', includes: ['create_database'] }],
      },
      {
        // model: 'ark/DeepSeek-V3',
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }

  public static async createAutomation(
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      {
        user,
        system: 'You are Bika.ai Super Assistant',
        // prompt: userPrompt,
        messages: [
          {
            id: generateNanoID('aium-create-automation-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'bika-automation', includes: ['create_automation'] }],
      },
      {
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }

  public static async superAsk(
    history: AIMessageBO[],
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      // history,
      {
        user,
        system: 'You are Bika.ai Super Assistant',
        messages: [
          ...history,
          {
            id: generateNanoID('aium-super-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        // prompt: userPrompt,
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'bika-space' }],
      },
      {
        // model: 'ark/DeepSeek-V3',
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }

  /**
   * 获取UI发到客户端，根据当前的解决状态和参数填写情况而定
   * @returns
   */
  // async parseUI(): Promise<AIIntentUIVO> {
  //   return this.defaultPromptUI();
  // }

  public static async testWeather(
    history: AIMessageBO[],
    user: UserSO,
    userPrompt: string,
    onChunk?: (chunk: unknown) => void,
    dataStreamWriter?: DataStreamWriter,
  ) {
    return AISO.streamChat(
      {
        user,
        system: '你是天气报告专家，你在跟用户对话，请注意通俗易懂，不要用技术名词',
        messages: [
          ...history,
          {
            id: generateNanoID('aium-test-weather-'),
            role: 'user',
            parts: [
              {
                type: 'text',
                text: userPrompt,
              },
            ],
          },
        ],
        // prompt: userPrompt,
        maxSteps: 5,
        skillsets: [{ kind: 'preset', key: 'debug' }],
      },
      {
        onChunk,
        dataStreamWriter,
      },
      {},
    );
  }

  private parsePrompt(user: UserSO, space: SpaceSO, chatHistories: AIMessageBO[]): IStreamChatPrompt {
    if (
      this.intentParams.agent?.type === 'expert' &&
      this.intentParams.agent.expertKey === TalkExpertKeySchema.enum.supervisor
    ) {
      return {
        system: ChiefOfStaffPrompt,
        messages: chatHistories,
        space,
        user,
        maxSteps: 20,
        skillsets: [{ kind: 'preset', key: 'bika-super-agent' }],
      };
    }
    throw new Error('Invalid agent type');
  }
}
