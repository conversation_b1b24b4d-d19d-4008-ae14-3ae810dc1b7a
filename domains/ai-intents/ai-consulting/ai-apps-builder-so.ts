import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { getDefaultAIModel } from '@bika/contents/config/server/ai/ai-model-config';
import { AiAppsBuildWithSchemaPrompt } from '@bika/contents/config/server/ai-prompts/ai-apps-build-prompt';
import crmTemplate from '@bika/contents/templates/crm-b2b-sales/template';
import twitterTempalte from '@bika/contents/templates/x-ai-automated-tweets/template';
import type { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { BuildAIAppsResponseVOSchema, type BuildAIAppsResponseVO } from '@bika/types/template/vo';

export interface AIAppsBuildResult {
  aiApp: BuildAIAppsResponseVO | null;
  str: string;
}

/**
 * 使用AI，生成资源、模板
 */
export class AIAppsBuilderSO {
  /**
   * compose 模式生成 AI 智能体模板
   *
   * @returns stream
   */
  public static async composer(prompt: string, customTemplateId?: string, advanceModel?: PresetLanguageAIModelDef) {
    //
    console.log('start ai composer... for ai agent templates');
  }

  public static async build(
    prompt: string,
    customTemplateId?: string,
    advanceModel?: PresetLanguageAIModelDef,
    onStream?: (chunk: string | 'DONE') => void,
  ): Promise<AIAppsBuildResult> {
    const aistream = await AIAppsBuilderSO.stream(prompt, customTemplateId, advanceModel);

    let reasoningContent = '';
    let aimsg = '';
    for await (const chunk of aistream) {
      let chunkStr;

      const delta = chunk.choices[0].delta as { reasoning_content?: string };
      if (delta.reasoning_content) {
        chunkStr = delta.reasoning_content;
        reasoningContent = reasoningContent.concat(chunkStr);
        process.stdout.write(chunkStr);
      } else {
        chunkStr = chunk.choices[0].delta?.content || '';
        aimsg = aimsg.concat(chunkStr);
        process.stdout.write(chunkStr);
      }

      if (onStream) {
        onStream(chunkStr);
      }
    }

    // 结束
    if (onStream) {
      onStream('[DONE]');
    }

    // OpenAI生成，会加上```json，需要去掉
    if (aimsg.startsWith('```json')) {
      aimsg = aimsg.slice(7);
    }
    if (aimsg.endsWith('```')) {
      aimsg = aimsg.slice(0, -3);
    }

    const validate = BuildAIAppsResponseVOSchema.safeParse(JSON.parse(aimsg));
    if (!validate.success) {
      console.error('AI生成的模板不符合规范！', validate.error);
      return {
        aiApp: null,
        str: aimsg,
      };
    }

    return {
      aiApp: validate.data,
      str: aimsg,
    };
  }

  /**
   * AI生成App ，如果不传options，那么使用系统AI模型
   */
  public static async stream(aiPrompt: string, customTemplateId?: string, advancedModel?: PresetLanguageAIModelDef) {
    const references = `
${JSON.stringify(crmTemplate)}

${JSON.stringify(twitterTempalte)}
`;

    const theModel = advancedModel || getDefaultAIModel();
    const newTemplateId = customTemplateId || generateNanoID();
    const finalPrompt = _.template(AiAppsBuildWithSchemaPrompt)({
      templateId: newTemplateId,
      schema: JSON.stringify(zodToJsonSchema(BuildAIAppsResponseVOSchema)), // 压缩掉换行
      prompt: aiPrompt,
      references, // 压缩掉换行
    });

    console.log(
      `Prompt: ${finalPrompt}
-------------------------
开始通过AI生成模板啦！model:${theModel} TemplateId: ${newTemplateId}, Prompt: ${aiPrompt}...

`,
    );

    const AISO = await import('@bika/domains/ai/server/ai-so').then((mod) => mod.AISO);

    const aistream = AISO.streamByOpenAI(finalPrompt, { model: theModel, json: true });
    return aistream;
  }
}
