import { z } from 'zod';
import { GiftCodeFactory } from '@bika/domains/pricing/server/gift-code/gift-code-factory';
import { UserSO } from '@bika/domains/user/server';
import { adminProcedure } from '@bika/server-orm/trpc';
import { RecordListDTOSchema } from '@bika/types/database/dto';
import { GiftCodeGenerateDTOSchema } from '@bika/types/pricing/dto';

export const adminGiftCodeSubRouter = {
  database: adminProcedure.query(async () => GiftCodeFactory.toDatabaseViews()),
  records: adminProcedure
    .input(RecordListDTOSchema)
    .query(async (opts) => GiftCodeFactory.toDatabaseRecords(opts.input)),

  generateGiftCodes: adminProcedure.input(GiftCodeGenerateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    const { channel, plan, numbers = 100, codePrefix } = input;
    const codeType = GiftCodeFactory.determineGiftCodeType(channel);
    const giftCodes = await GiftCodeFactory.generateGiftCodes({
      type: codeType,
      plan,
      numbers,
      codePrefix,
      userId: user.id,
    });
    return GiftCodeFactory.exportGiftCodes(giftCodes);
  }),

  delete: adminProcedure.input(z.object({ id: z.string() })).mutation(async (opts) => {
    const { input } = opts;
    const giftCode = await GiftCodeFactory.findById(input.id);
    if (!giftCode) {
      return false;
    }
    await giftCode.delete();
    return true;
  }),

  void: adminProcedure.input(z.object({ id: z.string() })).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    const giftCode = await GiftCodeFactory.findUniqueCode(input.id);
    if (!giftCode) {
      return false;
    }
    await giftCode.void(user.id);
    return true;
  }),

  voids: adminProcedure.input(z.object({ ids: z.array(z.string()) })).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await GiftCodeFactory.voidGiftCodes(user.id, input.ids);
    return true;
  }),
};
