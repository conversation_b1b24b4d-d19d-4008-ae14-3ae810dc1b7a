import { SiteSsoSO } from '@bika/domains/system/server/site-sso-so';
import { adminProcedure } from '@bika/server-orm/trpc';
import { SiteSsoInfoSchema } from '@bika/types/system';

export const adminSSOSubRouter = {
  /**
   * 查看设置
   */
  info: adminProcedure.query(async () => {
    const siteSso = await SiteSsoSO.getSiteSsoInfo();
    return siteSso.model;
  }),

  /**
   * 更新设置
   */
  update: adminProcedure.input(SiteSsoInfoSchema.partial()).mutation(async ({ input }) => {
    const siteSso = await SiteSsoSO.getSiteSsoInfo();
    await siteSso.update(input);
    return siteSso.model;
  }),

  /**
   * 重置设置
   */
  reset: adminProcedure.mutation(async () => {
    const siteSso = await SiteSsoSO.getSiteSsoInfo();
    await siteSso.reset();
  }),
};
