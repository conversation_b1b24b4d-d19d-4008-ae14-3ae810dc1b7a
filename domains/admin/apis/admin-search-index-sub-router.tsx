import { adminProcedure } from '@bika/server-orm/trpc';
import { RecordListDTOSchema } from '@bika/types/database/dto';
import { AISearchSO } from '../../ai/server/ai-search-so';
import { AdminSearchPageTypeSchema, AdminSearchSO } from '../server/admin-search-so';

export const adminSearchIndexSubRouter = {
  // database vo and records
  database: adminProcedure.query(async () => AdminSearchSO.getDatabase()),
  records: adminProcedure.input(RecordListDTOSchema).query(async (opts) => {
    const { input } = opts;
    const dto = input;
    return AdminSearchSO.getRecords(dto);
  }),
  view: adminProcedure.input(AdminSearchPageTypeSchema).query(async (opts) => AdminSearchSO.getView(opts.input)),

  rebuildIndex: adminProcedure.mutation(async () => AISearchSO.rewriteAllNodeSearchIndex(50)),
  cleanIndexes: adminProcedure.mutation(async () => AISearchSO.cleanIndexes()),
};
