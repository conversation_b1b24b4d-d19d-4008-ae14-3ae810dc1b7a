import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import { z } from 'zod';
import { TemplatesVerified } from '@bika/contents/config/server/template/templates-init-data';
import { AdminTemplatesSO } from '@bika/domains/admin/server/admin-templates-so';
import type { AdminTemplateBasicVO } from '@bika/domains/admin/types/admin-templates';
import { publicProcedure, adminProcedure } from '@bika/server-orm/trpc';
import { LocalContentLoader } from '@bika/server-orm';
import { Database } from '@bika/types/database/bo';
import {
  FieldGetDTOSchema,
  FieldUpdateDTOSchema,
  FieldDeleteDTOSchema,
  ViewDeleteDTOSchema,
  ViewUpdateDTOSchema,
} from '@bika/types/database/dto';
import { EditorNodeFolderDTOSchema } from '@bika/types/editor/dto';
import { iStringParse } from '@bika/types/i18n/bo';
import { NodeResourceTypeSchema } from '@bika/types/node/bo';
import { createNodeResourceBOWithDTO } from '@bika/types/node/default';
import { NodeCreateDTOSchemaWithoutSpaceId, UpdateResourceDTOSchema } from '@bika/types/node/dto';
import { LocaleSchema } from '@bika/types/system';
import { CustomTemplateSchema, type CustomTemplate } from '@bika/types/template/bo';
import type { RolePaginationVO } from '@bika/types/unit/vo';
import {
  sendOneTemplateWarningEmailToAuthor,
  sendAllTemplatesWarningEmailToAll,
  sendAuthorWarningTemplates,
} from './site-admin-controller';

/**
 * 管理本地模板的tRPC路由，嵌套进去admin router
 */
export const adminLocalTemplatesSubRouter = {
  /**
   * 获取所有local templates，返回NodeTreeVO，用以Resource Editor
   */
  localTemplatesAsRootNode: adminProcedure
    .input(
      z.object({
        locale: LocaleSchema.optional(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      return AdminTemplatesSO.localTemplatesAsRootNodeVO(input);
    }),

  getRoles: publicProcedure.input(z.object({ templateId: z.string() })).query(async (_opts) => {
    const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(_opts.input.templateId);

    const total = adminTemplateSO.template.presetUnits?.length || 0;

    const rolesVO: RolePaginationVO = {
      data: [],
      pagination: {
        pageNo: 1,
        pageSize: 10,
        total,
      },
    };
    //   type: RoleUnitType,
    // sequence: z.number().optional(),
    // manageSpace: z.boolean(),
    // permissions: z.array(RolePermissionSchema).optional(),
    // memberCount: z.number().optional()
    if (adminTemplateSO.template.presetUnits) {
      for (const unitBo of adminTemplateSO.template.presetUnits) {
        rolesVO.data.push({
          id: unitBo.id || unitBo.templateId!,
          name: iStringParse(unitBo.name, 'zh-CN'),
          manageSpace: false,
          type: 'Role',
        });
      }
    }
    return rolesVO;
  }),

  searchNodes: adminProcedure
    .input(
      z.object({
        templateId: z.string(),
        resourceType: NodeResourceTypeSchema,
      }),
    )
    .query(async (opts) =>
      AdminTemplatesSO.findResourceByTemplateIdAndType(opts.input.templateId, opts.input.resourceType),
    ),
  getTemplateAsNode: adminProcedure
    .input(z.object({ templateId: z.string(), locale: LocaleSchema.optional() }))
    .query(async (opts) => {
      const { input } = opts;
      const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(input.templateId);
      return adminTemplateSO.toNodeDetailVO({ locale: input.locale });
    }),

  getDatabase: adminProcedure
    .input(z.object({ databaseId: z.string(), locale: LocaleSchema.optional() }))
    .query(async (opts) => {
      const { input } = opts;
      const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(input.databaseId);
      const resource = adminTemplateSO.resource;
      // duplicate template id
      if (!resource || resource.resourceType !== 'DATABASE') {
        throw new Error(`resource not found' + ${input.databaseId}`);
      }
      return adminTemplateSO.toDatabaseVO(resource as Database, { locale: input.locale });
    }),

  /**
   * Delete a view
   */
  deleteView: publicProcedure.input(ViewDeleteDTOSchema).mutation(async (opts) => {
    const { input } = opts;
    const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(input.databaseId);
    return adminTemplateSO.deleteDatabaseView(input.viewId);
  }),

  /**
   * Update a view
   */
  updateView: publicProcedure.input(ViewUpdateDTOSchema).mutation(async (opts) => {
    const { input } = opts;
    const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(input.databaseId);
    return adminTemplateSO.updateDatabaseView(input.viewId, input.data);
  }),

  /**
   * Retrieve the info of a field
   */
  getField: publicProcedure
    .input(
      FieldGetDTOSchema.extend({
        locale: LocaleSchema.optional(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(input.databaseId);
      return adminTemplateSO.getDatabaseField(input.fieldId);
    }),
  /**
   * Update a field
   */
  updateField: publicProcedure.input(FieldUpdateDTOSchema).mutation(async (opts) => {
    const { input } = opts;
    const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(input.databaseId);
    return adminTemplateSO.updateDatabaseField(input.field);
  }),

  /**
   * Delete a field
   */
  deleteField: publicProcedure.input(FieldDeleteDTOSchema).mutation(async (opts) => {
    const { input } = opts;
    const adminTemplateSO = await AdminTemplatesSO.initWithResourceId(input.databaseId);
    return adminTemplateSO.deleteDatabaseField(input.fieldId);
  }),
  /**
   * 获取所有local templates，只返回基础信息
   */
  localTemplates: publicProcedure.query(async (_opts) => {
    const retTpls: AdminTemplateBasicVO[] = [];
    const templates = await LocalContentLoader.template.fsTemplatesList();
    for (const tpl of templates) {
      const templateRepo = await LocalContentLoader.template.importLocalTemplateRepo(tpl.templateId);
      const { score, warnings } = LocalContentLoader.template.checkTemplateRepoScore(templateRepo);
      const warningsText = LocalContentLoader.template.getTemplateRepoWarningText(warnings);
      retTpls.push({
        templateId: templateRepo.templateId,
        name: templateRepo.name,
        author: typeof templateRepo.author === 'string' ? templateRepo.author : 'UNKNOWN',
        visibility: templateRepo.visibility || 'PUBLIC',
        warningsText,
        score,
        verified: TemplatesVerified[templateRepo.templateId],
      });
    }
    return retTpls;
  }),

  /**
   * 获取本地的模板JSON
   */
  getLocalTemplateBO: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      return LocalContentLoader.template.importLocalCustomTemplate(input.templateId);
    }),

  /**
   * 获取本地模版的resource
   */
  getLocalTemplateResourceBO: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      const so = await AdminTemplatesSO.initWithResourceId(input.templateId);
      if (!so.resource) {
        return {
          name: so.name,
          description: so.description,
          cover: so.template.cover,
          resourceType: 'FOLDER',
          templateId: so.templateId,
        };
      }
      return so.resource;
    }),

  getLocalTemplateVO: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .output(EditorNodeFolderDTOSchema)
    .query(async (opts) => {
      const { input } = opts;
      const localTemplate = await AdminTemplatesSO.localTemplateVO(input.templateId);

      return {
        nodeFolderType: 'TEMPLATE',
        data: {
          resourceType: 'FOLDER',
          name: localTemplate.name,
          description: localTemplate.description,
          children: localTemplate.current.data.resources,
        },
        template: localTemplate,
      };
    }),

  saveLocalTemplateBO: publicProcedure
    .input(
      z.object({
        template: CustomTemplateSchema,
      }),
    )
    .mutation(async (opts) => {
      const { input } = opts;
      const result = await LocalContentLoader.template.fsSaveLocalTemplateBO(input.template);
      return {
        result,
      };
    }),

  createResource: adminProcedure.input(NodeCreateDTOSchemaWithoutSpaceId).mutation(async (opts) => {
    const { input } = opts;
    if (input.parentId === 'LOCAL_ROOT_NODE') {
      // 顶层，新建模板
      const newTemplate: CustomTemplate = {
        name: input.data.name,
        templateId: iStringParse(input.data.name).toLowerCase().replace(/\s/g, '-'),
        cover: '/assets/template/template-cover-no-image-color.png',
        category: ['marketing', 'operation'],
        version: '0.0.1',
        schemaVersion: 'v1',
        resources: [],
      };

      await LocalContentLoader.template.fsSaveLocalTemplateBO(newTemplate);
      return;
    } // 不是顶层，放到resources

    // 新建资源？
    const { template } = await AdminTemplatesSO.findTemplateByResourceTemplateId(input.parentId);

    assert(template);
    const newBO = createNodeResourceBOWithDTO({ templateId: generateNanoID() }, input.data);
    template.resources.push(newBO);

    await LocalContentLoader.template.fsSaveLocalTemplateBO(template);
  }),

  updateResource: publicProcedure
    .input(
      z.object({
        id: z.string().describe('templateId'),
        data: UpdateResourceDTOSchema.describe('update parameter'),
      }),
    )
    .mutation(async (opts) => {
      const { input } = opts;
      AdminTemplatesSO.updateResource(input.id, input.data);
    }),

  /**
   * 发送所有模板的警告邮件
   */
  allTemplatesWarningEmail: adminProcedure.mutation(async (_opts) => sendAllTemplatesWarningEmailToAll()),

  // 逐个作者通知
  sendAuthorWarningTemplates: adminProcedure.mutation(async (_opts) => sendAuthorWarningTemplates()),

  /**
   * 发送针对某个模板的警告邮件
   */
  oneTemplateWarningEmail: adminProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input } = opts;

      return sendOneTemplateWarningEmailToAuthor(input);
    }),
};
