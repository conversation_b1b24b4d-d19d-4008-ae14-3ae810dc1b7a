import { createRoute, OpenAPIHono } from '@hono/zod-openapi';
import { IdParamSchema, UserCreateDTOSchema, UserUpdateDTOSchema } from '@bika/domains/admin/types/dto';
import { UserSO } from '@bika/domains/user/server';
import { SessionSO } from '@bika/server-orm/session';
import { createResponseVOSchema, ResponseVOBuilder } from '@bika/types/openapi/vo';
import { UserVOSchema } from '@bika/types/user/vo';
import { SiteAdminAuthVariables } from './site-admin-apis-types';

// 站点级用户API列表

// 获取用户
const getUser = createRoute({
  method: 'get',
  path: '/users/{id}',
  request: {
    params: IdParamSchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(UserVOSchema).openapi('User'),
        },
      },
      description: 'Retrieve the user',
    },
  },
});

// 创建用户
const createUser = createRoute({
  method: 'post',
  path: '/users',
  request: {
    body: {
      content: {
        'application/json': {
          schema: UserCreateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(UserVOSchema).openapi('User'),
        },
      },
      description: 'Create a user',
    },
  },
});

// 更新用户
const updateUser = createRoute({
  method: 'put',
  path: '/users/{id}',
  request: {
    params: IdParamSchema,
    body: {
      content: {
        'application/json': {
          schema: UserUpdateDTOSchema,
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: createResponseVOSchema(UserVOSchema).openapi('User'),
        },
      },
      description: 'Update the user',
    },
  },
});

// 删除用户
const deleteUser = createRoute({
  method: 'delete',
  path: '/users/{id}',
  request: {
    params: IdParamSchema,
  },
  responses: {
    200: {
      description: 'Delete the user',
    },
  },
});

/**
 * 定义站点用户API
 */
export const initSiteUserAPIs = (app: OpenAPIHono<{ Variables: SiteAdminAuthVariables }>) => {
  app.openapi(getUser, async (c) => {
    const { id } = c.req.valid('param');
    const user = await UserSO.init(id);
    return c.json(ResponseVOBuilder.success(user.toVO()), 200);
  });
  app.openapi(createUser, async (c) => {
    const userDTO = c.req.valid('json');
    const user = await UserSO.createUser({ ...userDTO }, {}, {}, { checkEmail: true });
    return c.json(ResponseVOBuilder.success(user.toVO()), 200);
  });
  app.openapi(updateUser, async (c) => {
    // const userId = c.get('userId');
    const { id } = c.req.valid('param');
    const userDTO = c.req.valid('json');
    const user = await UserSO.init(id);
    await user.updateUserInfo({ ...userDTO });
    return c.json(ResponseVOBuilder.success(user.toVO()), 200);
  });
  app.openapi(deleteUser, async (c) => {
    const { id } = c.req.valid('param');
    const user = await UserSO.init(id);
    const sessions = await SessionSO.find(user.id);
    await user.destroy();
    // 剔除会话
    if (sessions.length > 0) {
      await Promise.all(sessions.map((session) => session.invalidate()));
    }
    return c.json({}, 200);
  });
};
