import { router } from '@bika/server-orm/trpc';
import { adminDashboardSubRouter } from './admin-dashboard-sub-router';
import { adminDebugSubRouter } from './admin-debug-sub-router';
import { adminGiftCodeSubRouter } from './admin-gift-code-sub-router';
import { adminLicenseCodeSubRouter } from './admin-license-code-sub-router';
import { adminLocalTemplatesSubRouter } from './admin-local-template-sub-router';
import { adminSearchIndexSubRouter } from './admin-search-index-sub-router';
import { adminSiteAdminSubRouter } from './admin-site-admin-sub-router';
import { adminSpacesSubRouter } from './admin-spaces-sub-router';
import { adminSSOSubRouter } from './admin-sso-router';
import { adminSurveySubRouter } from './admin-survey-sub-router';
import { adminUsersSubRouter } from './admin-users-sub-router';

/**
 * 超级管理的路由
 */
export const adminRouter = router({
  debug: router(adminDebugSubRouter),
  users: router(adminUsersSubRouter),
  templates: router(adminLocalTemplatesSubRouter),
  space: router(adminSpacesSubRouter),
  searchIndex: router(adminSearchIndexSubRouter),
  siteAdmin: router(adminSiteAdminSubRouter),
  survey: router(adminSurveySubRouter),
  giftCode: router(adminGiftCodeSubRouter),
  dashboard: router(adminDashboardSubRouter),
  licenseCode: router(adminLicenseCodeSubRouter),
  sso: router(adminSSOSubRouter),
});
