import { Space<PERSON> } from '@bika/domains/space/server';
import { UserSO } from '@bika/domains/user/server';
import { adminProcedure } from '@bika/server-orm/trpc';
import { RecordListDTOSchema } from '@bika/types/database/dto';
import { AdminUpdateSubscriptionDTOSchema } from '@bika/types/pricing/dto';
import * as SiteAdminController from './site-admin-controller';

export const adminSpacesSubRouter = {
  records: adminProcedure.input(RecordListDTOSchema).query(async (opts) => {
    const { input } = opts;
    return SpaceSO.toDatabaseRecords(input);
  }),

  database: adminProcedure.query(async () => SpaceSO.toDatabaseViews()),

  updateSubscription: adminProcedure.input(AdminUpdateSubscriptionDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await SiteAdminController.updateSubscription(user, input);
  }),
};
