import { adminProcedure } from '@bika/server-orm/trpc';
import { ChartWidgetVO, WidgetVO } from '@bika/types/dashboard/vo';
import { UserMetrics } from '../server/metrics/user-metrics';
import { lineChartToWidgetVO } from '../server/metrics/utils';

export const adminDashboardSubRouter = {
  userMetrics: adminProcedure.query(async (_opts) => {
    const [dau, wau, mau, userCountWidget, dailyNewUsers] = await Promise.all([
      UserMetrics.activeStat({ interval: 'D' }),
      UserMetrics.activeStat({ interval: 'W' }),
      UserMetrics.activeStat({ interval: 'M' }),
      UserMetrics.countWidget(),
      UserMetrics.dailyNewUsers(),
    ]);

    const dauChartWidget: ChartWidgetVO = lineChartToWidgetVO('dau', '每日活跃用户', dau);
    const wauChartWidget: ChartWidgetVO = lineChartToWidgetVO('wau', '每周活跃用户', wau);
    const mauChartWidget: ChartWidgetVO = lineChartToWidgetVO('mau', '每月活跃用户', mau);

    const dailyNewUsersWidget: ChartWidgetVO = lineChartToWidgetVO('daily_new_user', '每日新增用户', dailyNewUsers);

    const widgets: WidgetVO[] = [
      { ...userCountWidget, id: 'user_count' },
      { ...dauChartWidget },
      { ...wauChartWidget },
      { ...mauChartWidget },
      { ...dailyNewUsersWidget, id: 'daily_new_user' },
    ];

    return widgets;
  }),
};
