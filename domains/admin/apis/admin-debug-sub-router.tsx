import { z } from 'zod';
import { adminProcedure, router } from '@bika/server-orm/trpc';
import { WebsiteNotificationBarSchema } from '@bika/types/system/remote-storage';
import { SpaceSO } from '../../space/server';
import { RemoteStorageHelper } from '../../system/server/remote-storage/remote-storage-helper';
import { UserSO } from '../../user/server/user-so';
import { AdminDebugSO } from '../server/admin-debug-so';

export const adminDebugSubRouter = {
  // 网站顶部通知栏
  listWebsiteNotificationBars: adminProcedure
    .output(z.array(WebsiteNotificationBarSchema))
    .query(async (opts) => RemoteStorageHelper.websiteNotificationBars.list()),

  saveWebsiteNotificationBars: adminProcedure.input(z.array(WebsiteNotificationBarSchema)).mutation(async (opts) => {
    const { ctx, input } = opts;
    const bars = await RemoteStorageHelper.websiteNotificationBars.save(input);
    return bars;
  }),

  mockAI: router({
    get: adminProcedure.output(z.object({ enabled: z.boolean().nullish() })).query(async () => {
      const property = await RemoteStorageHelper.systemMockAI.get();
      return {
        enabled: property?.enabled || null,
      };
    }),

    set: adminProcedure
      .input(
        z.object({
          enabled: z.boolean(),
        }),
      )
      .output(z.object({ enabled: z.boolean().nullish() }))
      .mutation(async (opts) => {
        const { ctx, input } = opts;
        const property = await RemoteStorageHelper.systemMockAI.set(input.enabled);

        return {
          enabled: property?.enabled || null,
        };
      }),
  }),

  // 创建所有模板，在指定space
  createTestingTemplates: adminProcedure
    .input(
      z.object({
        spaceId: z.string(),
        // 是否测是导入？安装完模板后，导出+导入，否则只安装
        testImport: z.boolean().optional(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const user = await UserSO.init(ctx.session!.userId);
      const space = await SpaceSO.init(input.spaceId);
      const folderId = await AdminDebugSO.installAllTemplate(user, space);
      console.log('All template installed');
      if (input.testImport) {
        await AdminDebugSO.testExportAndImport(user, space, folderId);
      }
      return folderId;
    }),
};
