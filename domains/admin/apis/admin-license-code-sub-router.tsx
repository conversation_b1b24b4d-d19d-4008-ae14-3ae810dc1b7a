import { z } from 'zod';
import { LicenseCodeSpecSchema } from '@bika/domains/admin/types/license';
import { UserSO } from '@bika/domains/user/server';
import { adminProcedure } from '@bika/server-orm/trpc';
import { RecordListDTOSchema } from '@bika/types/database/dto';
import { DatabaseVOSchema } from '@bika/types/database/vo';
import { LicenseHelper } from '../server/license-helper';

export const adminLicenseCodeSubRouter = {
  /**
   * 授权码表结构
   */
  database: adminProcedure.output(DatabaseVOSchema).query(async () => LicenseHelper.getDatabase()),

  /**
   * 授权码列表
   */
  records: adminProcedure.input(RecordListDTOSchema).query(async ({ input }) => LicenseHelper.getRecords(input)),

  /**
   * 生成新的授权码
   */
  create: adminProcedure.input(LicenseCodeSpecSchema).mutation(async ({ ctx, input }) => {
    const user = await UserSO.init(ctx.session!.userId);
    return LicenseHelper.create(user.id, input);
  }),

  /**
   * 更新的授权码
   */
  update: adminProcedure.input(LicenseCodeSpecSchema).mutation(async ({ ctx, input }) => {
    const user = await UserSO.init(ctx.session!.userId);
    return LicenseHelper.update(user.id, input);
  }),

  delete: adminProcedure
    .input(z.object({ name: z.string() }))
    .mutation(async ({ input }) => LicenseHelper.delete(input.name)),
};
