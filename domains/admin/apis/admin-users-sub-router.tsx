import { adminProcedure } from '@bika/server-orm/trpc';
import { RecordListDTOSchema } from '@bika/types/database/dto';
import { DatabaseVOSchema } from '@bika/types/database/vo';
import { UserSO } from '../../user/server';

export const adminUsersSubRouter = {
  // database vo and records
  database: adminProcedure.output(DatabaseVOSchema).query(async () => UserSO.toDatabaseVO()),

  records: adminProcedure.input(RecordListDTOSchema).query(async (opts) => {
    const { input } = opts;
    return UserSO.findAsDatabaseRecords(input);
  }),
};
