import { z } from 'zod';
import { adminProcedure, protectedProcedure } from '@bika/server-orm/trpc';
import { DatabaseVOSchema, RecordPaginationVOSchema } from '@bika/types/database/vo';
import { SurveyTypeSchema, SurveyTypes } from '@bika/types/website/bo';
import { CommitSurveyDTOSchema } from '@bika/types/website/dto';
import { SurveySO } from '../server/survey-so';

export const adminSurveySubRouter = {
  /**
   * 提交调查问卷
   */
  commitSurvey: protectedProcedure.input(CommitSurveyDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const session = ctx.session!;
    const userId = session.userId;
    const { type, data, metadata } = input;
    const newSurvey = await SurveySO.create({
      userId,
      type,
      dataRow: data,
      metadata,
    });
    return newSurvey.toVO();
  }),

  /**
   * 获取调查问卷的具体数据
   */
  records: adminProcedure
    .input(
      z.object({
        type: SurveyTypeSchema,
      }),
    )
    .output(RecordPaginationVOSchema)
    .query(async (opts) => {
      const { input } = opts;
      const surveys = await SurveySO.fetchRecords(input.type);
      return surveys;
    }),

  // 获取调查问卷，返回Database VO
  database: adminProcedure.output(DatabaseVOSchema).query(async (_opts) => {
    const views = await Promise.all(SurveyTypes.map((surveyType) => SurveySO.findSurveyColumns(surveyType)));
    console.log(views);
    return {
      id: 'survey',
      name: '调查问卷',
      spaceId: 'admin',
      views,
    };
  }),

  // 获取调查问卷，详细的columns
  getSurveyViewVO: adminProcedure
    .input(
      z.object({
        type: SurveyTypeSchema,
      }),
    )
    .query(async (_opts) => {
      const viewVO = SurveySO.findSurveyColumns(_opts.input.type);
      return viewVO;
    }),
};
