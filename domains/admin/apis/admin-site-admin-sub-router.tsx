import { z } from 'zod';
import { SiteAdminSO } from '@bika/domains/user/server/site-admin-so';
import { adminProcedure } from '@bika/server-orm/trpc';
import { DatabaseVOSchema, RecordPaginationVOSchema, ViewVOSchema } from '@bika/types/database/vo';
import type { SiteAdminVO } from '../types/vo';

export const adminSiteAdminSubRouter = {
  getSiteAdminRecords: adminProcedure
    .output(
      z.object({
        database: DatabaseVOSchema,
        view: ViewVOSchema,
        records: RecordPaginationVOSchema,
      }),
    )
    .query(async () => SiteAdminSO.toDatabaseRecords()),

  // 获取站点管理员
  getSiteAdmins: adminProcedure.query(async (_opts) => {
    const siteAdmins = await SiteAdminSO.getAllSiteAdmins();

    const siteAdminVOs: SiteAdminVO[] = [];
    for (const so of siteAdmins) {
      siteAdminVOs.push(await so.toVO());
    }
    return siteAdminVOs;
  }),

  deleteSiteAdmin: adminProcedure
    .input(
      z.object({
        userId: z.string(),
      }),
    )
    .mutation(async (_opts) => SiteAdminSO.delete(_opts.input.userId)),

  addSiteAdmin: adminProcedure
    .input(
      z.object({
        userId: z.string(),
      }),
    )
    .mutation(async (_opts) => {
      const { input } = _opts;
      const newSiteAdmin = await SiteAdminSO.addAdmin(input.userId);

      return newSiteAdmin;
    }),
};
