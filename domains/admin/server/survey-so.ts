/* eslint-disable @typescript-eslint/no-explicit-any */
import { generateNanoID } from 'basenext/utils/nano-id';
import { db, type SurveyModel } from '@bika/server-orm';
import type { RecordPaginationVO, ViewVO, CellRenderVO, ViewFieldVO } from '@bika/types/database/vo';
import { SurveyType } from '@bika/types/website/bo';
import { EventSO } from '../../event/server/event/event-so';

export class SurveySO {
  private _model: SurveyModel;

  private constructor(private model: SurveyModel) {
    this._model = model;
  }

  public get data() {
    return this._model.data;
  }

  public get metadata() {
    return this._model.metadata;
  }

  public get id() {
    return this._model.id;
  }

  // 直接model即VO
  public toVO() {
    return this._model;
  }

  static async create(args: { userId: string; type: SurveyType; dataRow: Record<string, any>; metadata?: any }) {
    const surveyPO = await db.mongo.survey.create({
      id: generateNanoID('sur'),
      type: args.type,

      data: args.dataRow,
      metadata: args.metadata,
      createdBy: args.userId,
      updatedBy: args.userId,
    });
    const newSurvey = new SurveySO(surveyPO);

    EventSO.survey.onNewSurveyCreated(newSurvey);

    return newSurvey;
  }

  static async findById(id: string) {
    const surveyPO = await db.mongo.survey.findOne({
      id,
    });
    if (surveyPO) return new SurveySO(surveyPO);
    return null;
  }

  /**
   * 获取某个类型的调查问卷，并转换为ViewVO
   *
   * 方法是任意取一个问卷，然后将其data字段的key作为columns
   */
  static async findSurveyColumns(type: SurveyType): Promise<ViewVO> {
    const surveys = await db.mongo.survey
      .find({
        type,
      })
      .limit(1)
      .sort({ createdAt: -1 }); // 最近插入的

    const survey = surveys[0];
    let columns: ViewFieldVO[];

    if (survey) {
      columns = Object.keys(survey.data ?? {}).map((key, index) => ({
        id: key,
        databaseId: 'survey',
        type: 'LONG_TEXT',
        name: key,
        primary: index === 0,
      }));

      // 另外插字段
      columns.push({
        id: 'created_at',
        databaseId: 'survey',
        type: 'SINGLE_TEXT',
        name: 'Created At',
        primary: false,
      });

      // 另外插字段
      columns.push({
        id: 'created_by',
        databaseId: 'survey',
        type: 'SINGLE_TEXT',
        name: 'Created By',
        primary: false,
      });
    } else {
      columns = [];
    }

    return {
      id: type,
      name: type,
      type: 'TABLE',
      databaseId: 'survey',
      columns,
    };
  }

  static async findByType(type: SurveyType) {
    const surveys = await db.mongo.survey
      .find({
        type,
      })
      .sort({ createdAt: -1 }); // 最近插入的

    return surveys.map((survey) => new SurveySO(survey));
  }

  public static async fetchRecords(type: SurveyType): Promise<RecordPaginationVO> {
    const surveys = await this.findByType(type);

    return {
      total: surveys.length,
      rows: surveys.map((survey) => {
        const cells: Record<string, CellRenderVO> = {};
        for (const key of Object.keys(survey.data)) {
          cells[key] = {
            id: key,
            name: key,
            data: survey.data[key],
            value: survey.data[key],
          };
        }

        // 另外插字段
        cells.created_at = {
          id: 'created_at',
          name: 'Created At',
          data: survey.model.createdAt.toISOString(),
          value: survey.model.createdAt.toISOString(),
        };

        // 另外插字段
        cells.created_by = {
          id: 'created_by',
          name: 'Created By',
          data: survey.model.createdBy,
          value: survey.model.createdBy,
        };

        return {
          id: survey.id,
          databaseId: 'survey',
          revision: 1,
          cells,
        };
      }),
    };
  }

  static async findByMetadata(type: SurveyType, queryJson: any) {
    // 将queryJson转换为 'metadata.xxx'再进行查询

    const metadataFields: Record<string, any> = {};

    for (const key of Object.keys(queryJson)) {
      metadataFields[`metadata.${key}`] = queryJson[key];
    }

    const surveys = await db.mongo.survey
      .find({
        type,
        ...metadataFields,
      })
      .sort({ createdAt: -1 }); // 最近插入的

    return surveys.map((survey) => new SurveySO(survey));
  }
}
