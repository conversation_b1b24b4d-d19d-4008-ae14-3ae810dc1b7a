import { decrypt } from '@bika.ai/license';
import axios, { AxiosInstance } from 'axios';
import NodeCache from 'node-cache';
import { getAppEnv, isInUnitTest } from 'sharelib/app-env';
import { ServerError, errors } from '@bika/contents/config/server/error';
import { LicenseCodeSpec, LicenseCode, LicenseCodeSchema } from '@bika/domains/admin/types/bo';
import { CheckLicenseVO } from '@bika/domains/admin/types/vo';
import { RemoteStorageSO } from '@bika/domains/system/server/remote-storage/remote-storage-so';
import { RecordListDTO } from '@bika/types/database/dto';
import { DatabaseVO, RecordPaginationVO, ViewFieldVO, ViewVO } from '@bika/types/database/vo';
import { LicenseRemoteStorageProperty, LicenseRemoteStoragePropertySchema } from '@bika/types/system/remote-storage';
import { LicenseCodeSO } from './license-code-so';
import { LicenseCodeData, LicenseCodeDataSchema, LicenseCodeDataListSchema } from './types';

/**
 * License，调取边缘RSA验证服务: https://license.bika.ltd
 */
export class LicenseHelper {
  // 内存里缓存
  // private static cacheLicensed = false;

  private static _httpClient: AxiosInstance;

  private static _cache: NodeCache;

  static get httpClient() {
    if (!this._httpClient) {
      this._httpClient = axios.create({
        baseURL: 'https://license.bika.ltd',
      });
    }
    return this._httpClient;
  }

  static get cache() {
    if (!this._cache) {
      this._cache = new NodeCache();
    }
    return this._cache;
  }

  static get licenseServerAdminKey(): string | undefined {
    return process.env.LICENSE_SERVER_ADMIN_KEY;
  }

  static async update(userId: string, spec: LicenseCodeSpec) {
    const licenseCode: LicenseCode = {
      ...spec,
      createdBy: userId,
      createdAt: new Date().toISOString(),
    };
    const encrypted = await this.encryptCode(licenseCode);

    // 再去更新 license
    const licenseCodeData: LicenseCodeData = {
      name: spec.name,
      code: encrypted,
      // expiredAt: props.expiredAt,
      activated: 0,
      // remark: spec.remark,
      // createdBy: userId,
      createdAt: new Date().toISOString(),
    };
    const updateLicenseRes = await this.httpClient.patch(`/${this.licenseServerAdminKey}/license`, licenseCodeData);
    if (updateLicenseRes.data !== 'Success') {
      throw new Error('Failed to update license...');
    }
    return new LicenseCodeSO(licenseCodeData);
  }

  static async encryptCode(licenseCode: LicenseCode): Promise<string> {
    const res = await this.httpClient.post(`/${this.licenseServerAdminKey}/encrypt`, {
      s: JSON.stringify(licenseCode),
    });
    if (typeof res.data !== 'string') {
      throw new Error('Failed to encrypt license code data');
    }
    // 返回加密后的license code
    const encrypted = res.data;
    return encrypted;
  }

  /**
   * 生成license
   * @param props
   * @returns
   */
  static async create(userId: string, props: LicenseCodeSpec): Promise<LicenseCodeSO> {
    const licenseCode: LicenseCode = {
      ...props,
      createdBy: userId,
      createdAt: new Date().toISOString(),
    };
    const encrypted = await this.encryptCode(licenseCode);

    // 再去创建license
    const license: LicenseCodeData = {
      name: props.name,
      code: encrypted,
      // expiredAt: props.expiredAt,
      activated: 0,
      // remark: props.remark,
      // createdBy: userId,
      createdAt: new Date().toISOString(),
    };
    const createLicenseRes = await this.httpClient.post(`/${this.licenseServerAdminKey}/license`, license);

    if (createLicenseRes.data !== 'Success') {
      throw new Error('Failed to create license...');
    }

    return new LicenseCodeSO(license);
  }

  /**
   * 获取单个license，从远程获取
   * @param name
   * @returns
   */
  static async getLicenseByCode(code: string): Promise<LicenseCodeSO | null> {
    try {
      // code 可能是带有特殊符号(+和/), 需要将url来encode
      // const encodedCode = encodeURIComponent(code);
      const res = await this.httpClient.post(`/${this.licenseServerAdminKey}/license/code`, code);
      const licenseParse = LicenseCodeDataSchema.safeParse(res.data);
      if (licenseParse.success) {
        return new LicenseCodeSO(licenseParse.data);
      }

      // eslint-disable-next-line no-console
      console.error('license parse error', licenseParse.error);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('get license error', err);
    }

    return null;
  }

  /**
   * 获取单个license
   * @param name
   * @returns
   */
  static async getLicenseByName(name: string): Promise<LicenseCodeSO | null> {
    try {
      const res = await this.httpClient.get(`/${this.licenseServerAdminKey}/license/${name}`);
      const licenseParse = LicenseCodeDataSchema.safeParse(res.data);
      if (licenseParse.success) {
        return new LicenseCodeSO(licenseParse.data);
      }

      // eslint-disable-next-line no-console
      console.error('license parse error', licenseParse.error);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error(`get license error: ${name}`, err);
    }

    return null;
  }

  static async getLicenses(limit: number, offset: number): Promise<LicenseCodeSO[]> {
    const res = await this.httpClient.get(`/${this.licenseServerAdminKey}/licenses/${limit}/${offset}`);
    // console.log('res', res.data);
    const licenses = LicenseCodeDataListSchema.parse(res.data);
    return licenses.map((license) => new LicenseCodeSO(license));
  }

  static async delete(name: string): Promise<void> {
    const res = await this.httpClient.delete(`/${this.licenseServerAdminKey}/license/${name}`);
    if (res.data !== 'Success') {
      throw new Error('Failed to delete license...');
    }
  }

  static async activate(license: LicenseCodeData): Promise<LicenseCodeData> {
    const res = await this.httpClient.post(`/license/activate`, license);
    return LicenseCodeDataSchema.parse(res.data);
  }

  static setLicenseCache(license: Omit<LicenseRemoteStorageProperty, 'type'>): void {
    this.cache.set('LICENSE', license);
  }

  static decryptCode(licenseCode: string): LicenseCode {
    const decryptCodeStr = decrypt(licenseCode);
    const codeParseResult = LicenseCodeSchema.safeParse(JSON.parse(decryptCodeStr));
    if (codeParseResult.success) {
      return codeParseResult.data;
    }
    throw new ServerError(errors.common.license_not_found);
  }

  /**
   * 获取顺序: 缓存 -> 环境变量 -> 数据库 (调用License服务)
   * 缓存后面如果存在, 那么设置缓存以防重复调用数据库和License服务
   */
  public static async getLicenseStore(): Promise<LicenseCodeSO | null> {
    // 缓存里获取
    if (this.cache.has('LICENSE')) {
      const value = this.cache.get('LICENSE');
      if (value) {
        // console.log(`License --> get license from cache: ${value}`);
        const license = LicenseRemoteStoragePropertySchema.safeParse(value);
        if (license.success) {
          // 缓存有值且验证通过
          return new LicenseCodeSO(license.data);
        }
      }
    }

    // 环境变量是否有配置?
    const licenseCodeFromEnv = process.env.LICENSE_CODE;
    if (licenseCodeFromEnv) {
      // 本地解码一次，判断是否离线，如果非离线，继续去远程验证
      const theCode = this.decryptCode(licenseCodeFromEnv);

      if (theCode.offline) {
        const appEnv = getAppEnv();
        // LOCAL开发环境不会走到这里来, 除非代码出错
        if (appEnv !== 'SELF-HOSTED' && !isInUnitTest()) {
          // 非私有化, 校验code name, 即验证域名
          const appHostname = process.env.APP_HOSTNAME!;
          if (!appHostname.includes(theCode.name)) {
            throw new Error(`offline license code not match, needs: ${theCode.name}`);
          }
        }

        // 离线验证
        const licenseData = {
          name: theCode.name,
          code: licenseCodeFromEnv,
          activated: 1,
          expiredAt: theCode.expiredAt,
          advancedAI: theCode.advancedAI,
          createdAt: new Date().toISOString(),
        };
        this.setLicenseCache(licenseData);
        return new LicenseCodeSO(licenseData);
      }

      // 非离线，远程解码认证
      if (!this.licenseServerAdminKey) {
        // 暂不支持离线验证
        throw new Error('no support offline license validate');
      }

      const license = await this.getLicenseByCode(licenseCodeFromEnv);
      if (license) {
        // console.log(`License --> get license from env: ${license}`);
        // 设置缓存
        this.setLicenseCache({ ...license.licenseData, ...license.licenseCode });
        return license;
      }
    }

    // 数据库里获取, 激活通过的都会存在这里, 始终使用最新的激活授权码
    const property = await RemoteStorageSO.getProperty<LicenseRemoteStorageProperty>('LICENSE');
    if (property) {
      // console.log(`License --> get license from database: ${property}`);
      // 有值
      const { code, name, activated, expiredAt, createdAt } = property;
      // 设置缓存
      this.setLicenseCache({ code, name, activated, expiredAt, createdAt });
      return new LicenseCodeSO({ code, name, activated, createdAt });
    }

    // 都没有
    return null;
  }

  /**
   * license code检查，如果非法，那么rewrite到license code页面
   *
   * 检查：
   * 1. 首先去数据库检查，license code是否存在？
   * 2. 如果没有，环境变量取一下，有没有？
   *   2.2. 环境变量有？那么尝试安装验证
   *   2.2. 环境变量没有？那么rewrite到license code页面
   * 3. license 提交后，尝试安装验证
   *
   * 验证：
   * 1. 解码，判断是否offline 验证码
   * 2. 如果是offline 验证码，那么直接验证
   * 3. 成功则放入数据库
   * 4. 失败则rewrite到license code页面，显示错误
   * 5. online验证码，则类似，只是走一下网络
   *
   *
   * @returns true | false
   */
  static async checkLicense(): Promise<CheckLicenseVO> {
    // license验证
    const storeLicense = await this.getLicenseStore();
    if (!storeLicense) {
      return { hasLicense: false };
    }

    return { hasLicense: true, expired: storeLicense.isExpired(), advancedAI: storeLicense.advancedAI || false };

    // if (!this.cacheLicensed) {
    //   if (getAppEnv() === 'SELF-HOSTED') {
    //     if (!this.cacheLicensed) {
    //       // TODO: 首次启动，去校验license
    //       this.cacheLicensed = false;
    //     }
    //     return this.cacheLicensed;
    //   }

    //   // TODO: 不过，非self-hosted，都去激活记录一下
    //   const storeLicense = this.getLicenseStore();
    //   if (!storeLicense) {
    //     // eslint-disable-next-line no-console
    //     console.warn('Official deployment but no license code found in env or database');
    //   }

    //   this.cacheLicensed = true;
    // }
    // return this.cacheLicensed;
  }

  static getDatabase(): DatabaseVO {
    const columns: ViewFieldVO[] = [];
    columns.push({ id: 'name', databaseId: 'license-code', type: 'SINGLE_TEXT', name: 'Name', primary: true });
    columns.push({ id: 'code', databaseId: 'license-code', type: 'LONG_TEXT', name: 'Code', primary: false });
    columns.push({
      id: 'activated',
      databaseId: 'license-code',
      type: 'NUMBER',
      name: 'Activated Number',
      primary: false,
      property: {
        precision: 0,
      },
    });
    columns.push({
      id: 'expiredAt',
      databaseId: 'license-code',
      type: 'DATETIME',
      name: 'Expired At',
      primary: false,
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
      },
    });
    columns.push({ id: 'remark', databaseId: 'license-code', type: 'SINGLE_TEXT', name: 'Remark', primary: false });
    columns.push({
      id: 'createdAt',
      databaseId: 'license-code',
      type: 'DATETIME',
      name: 'Created At',
      primary: false,
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
      },
    });

    const views: ViewVO[] = [
      {
        id: 'all',
        name: 'ALL',
        type: 'TABLE',
        databaseId: 'license-code',
        columns,
      },
    ];

    const database: DatabaseVO = {
      id: 'license-code',
      name: 'License Code',
      spaceId: '1',
      views,
    };

    return database;
  }

  static async getRecords(dto: RecordListDTO): Promise<RecordPaginationVO> {
    const { startRow, endRow } = dto;
    const skip = startRow;
    const take = endRow - startRow;

    const licenses = await this.getLicenses(take, skip);

    const records = licenses.map((license) => license.toRecordVO());

    return {
      total: records.length,
      rows: records,
    };
  }
}
