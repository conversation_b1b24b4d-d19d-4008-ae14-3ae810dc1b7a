import { decrypt } from '@bika.ai/license';
import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import { test, expect, describe } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { LicenseCodeSpecSchema } from '@bika/domains/admin/types/bo';
import { LicenseHelper } from '../server/license-helper';

const hasLicenseAccess = LicenseHelper.licenseServerAdminKey !== undefined;

describe.runIf(hasLicenseAccess)('license test', () => {
  /**
   * 新用户第一次使用Bika的流程
   */
  test('license code test', async () => {
    const user = await MockContext.createUser('jack');
    const licenseName = generateNanoID('test-license-');
    // 创建测试
    const license = await LicenseHelper.create(user.id, {
      name: licenseName,
      expiredAt: dayjs().add(100, 'year').toISOString(),
      seat: 1000,
      plan: 'TEAM',
      trial: false,
    });
    expect(license.name.startsWith('test-license-')).toBe(true);
    // 还没有激活
    expect(license.activated).toBe(0);

    // 首次激活
    await license.activate();
    expect(license.activated).toBe(1);

    // 再激活一次
    await license.activate();
    expect(license.activated).toBe(2);

    // 根据name获取
    const getTest = await LicenseHelper.getLicenseByName(license.name);
    expect(getTest!.name).toBe(license.name);
    expect(getTest!.activated).toBe(2); // 被激活了两次
    // 根据code获取
    const getTestCode = await LicenseHelper.getLicenseByCode(license.code);
    expect(getTestCode!.name).toBe(getTest!.name);
    expect(getTestCode!.activated).toBe(2); // 被激活了两次

    expect(license.advancedAI).not.toBe(true); // 默认不开启高级AI
    expect(license.licenseCode.createdBy).toBe(user.id);

    // 修改 update
    const newLicenseSpec = { ...license.licenseCode };
    newLicenseSpec.advancedAI = true;

    await LicenseHelper.update(user.id, newLicenseSpec);
    const getTest2 = await LicenseHelper.getLicenseByName(license.name);
    expect(getTest2!.name).toBe(getTest!.name);
    expect(getTest2!.advancedAI).toBe(true); // 默认不开启高级AI
    expect(getTest2!.licenseCode.createdBy).toBe(user.id);

    // 删除测试的
    await license.delete();
  });

  /**
   * 通过这个test流程，确保staging和production的license code是存在的、正确的
   */
  test('official license code ensure', async () => {
    const user = await MockContext.createUser('jack');

    // await LicenseHelper.delete('dev.bika.ai'); // 删除旧的，确保是最新的
    let intLicense = await LicenseHelper.getLicenseByName('dev.bika.ai');
    if (!intLicense) {
      intLicense = await LicenseHelper.create(user.id, {
        name: 'dev.bika.ai',
        expiredAt: dayjs().add(100, 'year').toISOString(),
        seat: -1,
        offline: true,
        plan: undefined,
        trial: false,
        advancedAI: true,
      });
    }
    expect(intLicense.advancedAI).toBe(true);
    expect(intLicense.name).toBe('dev.bika.ai');

    // await LicenseHelper.delete('staging.bika.ai'); // 删除旧的，确保是最新的
    let stLicense = await LicenseHelper.getLicenseByName('staging.bika.ai');
    if (!stLicense) {
      stLicense = await LicenseHelper.create(user.id, {
        name: 'staging.bika.ai',
        expiredAt: dayjs().add(100, 'year').toISOString(),
        seat: -1,
        offline: true,
        plan: undefined,
        trial: false,
        advancedAI: true,
      });
    }
    expect(stLicense.advancedAI).toBe(true);
    expect(stLicense.name).toBe('staging.bika.ai');

    // await LicenseHelper.delete('bika.ai'); // 删除旧的，确保是最新的
    let licenseCode = await LicenseHelper.getLicenseByName('bika.ai');
    if (!licenseCode) {
      licenseCode = await LicenseHelper.create(user.id, {
        name: 'bika.ai',
        expiredAt: dayjs().add(100, 'year').toISOString(),
        seat: -1,
        plan: undefined,
        offline: true,
        trial: false,
        advancedAI: true,
      });
    }
    // expect(licenseCode.advancedAI).toBe(true);
    expect(licenseCode.name).toBe('bika.ai');
  });

  test('offline license code test', async () => {
    const bCode =
      'mQQSOg7tptg7El3TmGw6mzmKGS7Nn32KvdUKch8jBoF6GRLxnq4ll6OjUB7pDtii9ibePJgs0aFlhnBXWGvlvh74c7QjONR5oLnZWWegRsRFGomDiH4Nch4T9rESJZ6dXotHX6kFASHPjmAEXEAwv07vghjgYVoni1ngKKvIQ++N2Or2fybmhtrOzE63fcZuILdVLoSYRMydKOHtk6OfF/dYQ3eRn1ANcc2/swJKRHUzFsVBGEt3uErQmYm9Cb7zBFOLyxcIql/PNCdi/o6FwKFIjLZytN5MU5eK8ZRkIgNMoaH4beSMt4Y9Fm8a35uZ/2Yle2Ii75y7ZDjTZ1xmrQ==';
    const dCode = decrypt(bCode);
    // dev.bika.ai license code
    const theCode = LicenseCodeSpecSchema.parse(JSON.parse(dCode));
    expect(theCode.offline).toBe(true);
    process.env.LICENSE_CODE = bCode;
    const license = await LicenseHelper.getLicenseStore();
    expect(license!.name).toBe('dev.bika.ai');
    expect(license!.advancedAI).toBe(true);
    // hit cache
    const license2 = await LicenseHelper.getLicenseStore();
    expect(license2!.name).toBe('dev.bika.ai');
    expect(license2!.advancedAI).toBe(true);
  });
});
