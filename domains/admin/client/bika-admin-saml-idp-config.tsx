import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useApiCaller } from '@bika/api-caller';
import { HeaderTitleTabs } from '@bika/domains/node/client/header/header-title-tabs-component';
import { SiteSsoInfo, DefaultSiteSsoInfo } from '@bika/types/system';
import { getAppEnv } from 'sharelib/app-env';
import { Button } from '@bika/ui/button';
import { Radio, Input, Switch, Textarea, Link } from '@bika/ui/form-components';
import CommentOutlined from '@bika/ui/icons/components/comment_outlined';
import { Stack, Box, Divider } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';

export function BikaAdminSamlIdPConfigPage() {
  const { trpcQuery } = useApiCaller();

  const [ssoConfig, setSsoConfig] = useState<SiteSsoInfo>(DefaultSiteSsoInfo);

  const { data: ssoConfigInfo, refetch } = trpcQuery.admin.sso.info.useQuery();
  const updateSSOConfig = trpcQuery.admin.sso.update.useMutation();
  const resetSSOConfig = trpcQuery.admin.sso.reset.useMutation();

  useEffect(() => {
    if (ssoConfigInfo) {
      setSsoConfig(ssoConfigInfo);
    }
  }, [ssoConfigInfo]);

  const onUpdateSSOConfig = () => {
    const appEnv = getAppEnv();
    if (!['LOCAL', 'SELF-HOSTED'].includes(appEnv)) {
      toast.error('You can not update SSO configuration in this environment.');
      return;
    }
    updateSSOConfig.mutate(ssoConfig, {
      onSuccess: () => {
        toast.success('Update SSO configuration successfully.');
        refetch();
      },
    });
  };

  const onResetSSOConfig = () => {
    resetSSOConfig.mutate(undefined, {
      onSuccess: () => {
        toast.success('Reset SSO configuration successfully.');
        refetch();
      },
    });
  };

  return (
    <>
      <HeaderTitleTabs
        startDecorator={<CommentOutlined color={'var(--text-secondary)'} />}
        name="SSO Identify Provider Configuration"
      />
      <Divider />
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          padding: '30px 30px',
        }}
      >
        <Stack width="100%" display="flex" direction="column">
          <Typography
            component="label"
            mb={2}
            level="title-lg"
            endDecorator={
              <Switch
                sx={{ ml: 1 }}
                color={ssoConfig?.enabled ? 'primary' : 'danger'}
                checked={ssoConfig?.enabled}
                onChange={(e) => {
                  const { checked } = e.target;
                  setSsoConfig((prevConfig) => ({
                    ...prevConfig,
                    enabled: checked,
                  }));
                }}
              />
            }
          >
            Turn on
          </Typography>
          {/* <Typography level="title-lg">Domain</Typography>
          <Input
            size="lg"
            type="text"
            value={ssoConfig?.domain}
            onChange={(e) => {
              const { value } = e.target;
              setSsoConfig((prevConfig) => ({
                ...prevConfig,
                domain: value,
              }));
            }}
          /> */}
          <Typography level="title-lg">Entity Id</Typography>
          <Input
            sx={{ width: '40%' }}
            size="lg"
            type="text"
            value={ssoConfig?.entityId}
            onChange={(e) => {
              const { value } = e.target;
              setSsoConfig((prevConfig) => ({
                ...prevConfig,
                entityId: value,
              }));
            }}
          />
          <Typography mt={2} level="title-lg">
            Sing-in URL
          </Typography>
          <Input
            sx={{ width: '40%' }}
            size="lg"
            type="text"
            value={ssoConfig?.signInUrl}
            onChange={(e) => {
              const { value } = e.target;
              setSsoConfig((prevConfig) => ({
                ...prevConfig,
                signInUrl: value,
              }));
            }}
          />
          <Typography mt={2} level="title-lg">
            X509 Certificate
          </Typography>
          <Textarea
            style={{
              width: '50%',
              border: '3px solid #E5E5E5',
            }}
            placeholder="Paste your x509 certificate here."
            variant="outlined"
            minRows={5}
            maxRows={5}
            value={ssoConfig?.certificate}
            onChange={(e) => {
              const { value } = e.target;
              setSsoConfig((prevConfig) => ({
                ...prevConfig,
                certificate: value,
              }));
            }}
          />
          {/* account match rule config */}
          <Typography mt={2} level="h4">
            Account Match Rule Configuration
          </Typography>
          {/* Match Against */}
          <Typography mt={2} level="title-lg">
            Match Against
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
            <Radio
              checked={ssoConfig.accountMatchRuleConfig.matchAgainst === 'USER_ID'}
              value="USER_ID"
              label="User Id"
              name="radio-buttons"
              onChange={(_e) => {
                setSsoConfig((prevConfig) => ({
                  ...prevConfig,
                  accountMatchRuleConfig: {
                    ...prevConfig.accountMatchRuleConfig,
                    matchAgainst: 'USER_ID',
                  },
                }));
              }}
            />
            <Radio
              checked={ssoConfig.accountMatchRuleConfig.matchAgainst === 'EMAIL_ADDRESS'}
              value="EMAIL_ADDRESS"
              label="Email Address"
              name="radio-buttons"
              onChange={(_e) => {
                setSsoConfig((prevConfig) => ({
                  ...prevConfig,
                  accountMatchRuleConfig: {
                    ...prevConfig.accountMatchRuleConfig,
                    matchAgainst: 'EMAIL_ADDRESS',
                  },
                }));
              }}
            />
          </Box>
          {/* if user not match */}
          <Typography mt={2} level="title-lg">
            Action if user not match
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
            <Radio
              checked={ssoConfig.accountMatchRuleConfig.notMatchUserAction === 'CREATE_USER'}
              value="CREATE_USER"
              label="Create User"
              name="radio-buttons"
              onChange={(_e) => {
                setSsoConfig((prevConfig) => ({
                  ...prevConfig,
                  accountMatchRuleConfig: {
                    ...prevConfig.accountMatchRuleConfig,
                    notMatchUserAction: 'CREATE_USER',
                  },
                }));
              }}
            />
            <Radio
              checked={ssoConfig.accountMatchRuleConfig.notMatchUserAction === 'REJECT'}
              value="REJECT"
              label="Reject to redirect sign-in page"
              name="radio-buttons"
              onChange={(_e) => {
                setSsoConfig((prevConfig) => ({
                  ...prevConfig,
                  accountMatchRuleConfig: {
                    ...prevConfig.accountMatchRuleConfig,
                    notMatchUserAction: 'REJECT',
                  },
                }));
              }}
            />
          </Box>
        </Stack>

        <Stack mt={2} spacing={2} width="100%" alignItems="center" display="flex" flexDirection="column">
          <Typography mb={2} level="title-md">
            Tip: Bika SAML Service Provider (SP) metadata endpoint is{' '}
            <Link href="/api/site-admin/saml/metadata" target="_blank">
              here
            </Link>
          </Typography>
        </Stack>
        <Stack direction="row" mt={2} spacing={2} width="100%" alignItems="center" justifyContent="center">
          <Button
            sx={{
              width: '10%',
              height: '48px',
              borderRadius: '30px',
            }}
            loading={updateSSOConfig.isLoading}
            onClick={onUpdateSSOConfig}
          >
            Confirm
          </Button>
          <Button
            sx={{
              width: '10%',
              height: '48px',
              borderRadius: '30px',
            }}
            color="danger"
            loading={resetSSOConfig.isLoading}
            onClick={onResetSSOConfig}
          >
            Reset
          </Button>
        </Stack>
      </Box>
    </>
  );
}
