import mime from 'mime-types';
import { z } from 'zod';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { router, protectedProcedure, publicProcedure, TRPCError } from '@bika/server-orm/trpc';
import { AttachmentCreateSchema, GetPresignedPutSchema } from '@bika/types/attachment/dto';

export const attachmentRouter = router({
  /**
   * 批量获取临时预签名URL，客户端PUT上传文件到这个预签名URL
   */
  getPresignedPutUrls: protectedProcedure
    .input(
      z.object({
        // 扩展名，如.xxx
        fileExt: z.string().optional(),
        count: z.number(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const promises = Array.from({ length: opts.input.count }, () =>
        TmpAttachmentSO.getPresignedPut(user, input.fileExt),
      );
      return Promise.all(promises);
    }),

  /**
   * 获取临时预签名上传对象，客户端PUT上传文件到这个预签名URL
   */
  getPresignedPut: publicProcedure.input(GetPresignedPutSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    let user: UserSO | null = null;
    if (ctx.session?.userId) {
      user = await UserSO.init(ctx.session?.userId);
    }
    const { contentType, size, spaceId } = input;
    if (spaceId) {
      // 校验容量
      const space = await SpaceSO.init(spaceId);
      const entitlement = await space.getEntitlement();
      await entitlement.checkUsageExceed({ feature: 'STORAGES', value: size });
    }
    const extension = mime.extension(contentType);
    if (!extension) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: `Invalid contentType ${contentType}` });
    }
    return TmpAttachmentSO.getPresignedPut(user, extension);
  }),

  /**
   * 客户端上传文件之后，拿着PreSignedURL，通知服务器，并记录数据库Attachment
   *
   * 涉及使用的模块：上传头像、上传空间站图标、上传文件夹封面、上传节点图标等
   * 比如，AI有audio2Text接口，客户端上传后，直接内部调用了Create Attachment
   */
  create: publicProcedure.input(AttachmentCreateSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    let user: UserSO | null = null;
    if (ctx.session?.userId) {
      user = await UserSO.init(ctx.session?.userId);
    }
    const attachment = await AttachmentSO.createByPresignedPut(user, input.path, `${input.prefix}/`);
    return attachment.toVO();
  }),

  delete: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input } = opts;
      await AttachmentSO.deleteAttachment(input.id);
    }),

  /**
   * 没用到, 暂时没用到此场景,可能废弃
   */
  bulkCreate: protectedProcedure
    .input(
      z.object({
        paths: z.array(z.string()),
        // 记录模块名如：ai/,  attach/
        prefix: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const attachments = await Promise.all(
        input.paths.map((path) => AttachmentSO.createByPresignedPut(user, path, input.prefix)),
      );
      return attachments.map((attachment) => attachment.toVO());
    }),
});
