// eslint-disable-next-line max-classes-per-file
import assert from 'assert';
import fs from 'node:fs';
import path from 'node:path';
import type { Readable } from 'node:stream';
import os from 'os';
import { generateNanoID } from 'basenext/utils/nano-id';
import mime from 'mime-types';
import { AIImageBOConfigs } from '@bika/contents/config/server/ai/ai-image-config';
import { CoinAccountSO } from '@bika/domains/store/server/coin-account-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, Prisma, type PrismaPromise } from '@bika/server-orm';
import type { AIImageBO, AIGenerateImageProps, AICompletionKind, AICompletionBO, AIUsage } from '@bika/types/ai/bo';
import { AttachmentVO } from '@bika/types/attachment/vo';
import { CONST_PREFIX_ATTACHMENT } from '@bika/types/database/vo';
import type { ITmpAttachmentModel } from './dao/types';
import { ImageSO } from './image-so';
import { bikaAttachmentDAO, TmpAttachmentSO } from './tmp-attachment-so';
import type { AttachmentModel } from './types';
import { AISO } from '../../ai/server/ai-so';
// import { type AIGenerateImageProps } from '../../ai/server/types';

abstract class BaseAttachmentSO {}

/**
 *
 * 附件对象，通常有TmpAttachmentSO转换而来
 *
 */
export class AttachmentSO extends BaseAttachmentSO {
  protected _model: AttachmentModel;

  protected constructor(model: AttachmentModel) {
    super();
    this._model = model;
  }

  get model() {
    return this._model;
  }

  /**
   * 附件标识
   */
  get id() {
    return this.model.id;
  }

  /**
   * 附件扩展名
   */
  get ext(): string {
    return this.model.ext;
  }

  /**
   * 文件大小
   */
  get size(): number {
    return this.model.size;
  }

  /**
   * 被引用数量
   */
  get refCount(): number {
    return this.model.refCount;
  }

  get etag(): string {
    return this.model.etag;
  }

  /**
   * 原件存储路径
   */
  get path(): string {
    return this.model.path;
  }

  /**
   * 预览图存储路径
   */
  get preview(): string | null {
    return this.model.preview;
  }

  /**
   * 缩略图存储路径
   */
  get thumbnail(): string | null {
    return this.model.thumbnail;
  }

  /**
   * Object存储完整路径
   *
   * @returns host/path
   */
  get fullPath(): string {
    return this.getPublicUrlForKey(this.path);
  }

  /**
   * 存储的对象名,比如 folder/1234.png, 返回 1234.png
   */
  get objectName(): string {
    return bikaAttachmentDAO.getObjectName(this.model);
  }

  static async init(id: string): Promise<AttachmentSO> {
    const attachmentPO = await bikaAttachmentDAO.init(id);
    if (!attachmentPO) {
      throw new Error(`Attachment not found, id: ${id}`);
    }
    return new AttachmentSO(attachmentPO);
  }

  static async initMaybeNull(id: string): Promise<AttachmentSO | null> {
    const attachmentPO = await bikaAttachmentDAO.initMaybeNull(id);
    if (!attachmentPO) {
      return null;
    }
    return new AttachmentSO(attachmentPO);
  }

  /**
   * 根据ID查找多个附件
   */
  static async findMany(ids: string[]): Promise<AttachmentSO[]> {
    if (ids.length === 0) {
      return [];
    }
    const models = await bikaAttachmentDAO.list(ids);
    // 按照原来的ID顺序返回
    const attachments: AttachmentSO[] = [];
    for (const id of ids) {
      const model = models.find((m) => m.id === id);
      if (model) {
        attachments.push(new AttachmentSO(model));
      }
    }
    return attachments;
  }

  /**
   * 直接读取附件的二进制Blob
   *
   * @returns Blob
   */
  async getObjectBlob(): Promise<Blob> {
    // try {
    const fileStream: Readable = (await bikaAttachmentDAO.getObject(this.path)) as Readable;
    const chunks: Buffer[] = [];

    return new Promise<Blob>((resolve, reject) => {
      fileStream.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      fileStream.on('end', () => {
        const fileBuffer = Buffer.concat(chunks as unknown as Uint8Array[]);
        const blob = new Blob([new Uint8Array(fileBuffer)], { type: mime.lookup(this.ext) as string });
        resolve(blob);
      });

      fileStream.on('error', (err) => {
        reject(err);
      });
    });
  }

  /**
   * 公开访问存储服务的端点地址, 大多数是CDN加速地址, 如果放在 Cloudfront/Cloudflare 或其他CDN服务上
   * 或者是本地存储服务的地址: http://{storageServerHostname}:{storageServerPort}/{bucketName}
   */
  public static publicEndpoint(): string {
    return bikaAttachmentDAO.publicEndpoint;
  }

  private getPublicUrlForKey(keyPath: string): string {
    const endpoint = AttachmentSO.publicEndpoint();
    // if (!endpoint) {
    //   throw new Error('storage server host not set');
    // }
    return `${endpoint}/${keyPath}`;
  }

  get thumbnailUrl(): string | null {
    if (this.thumbnail) {
      return `${this.thumbnail}`;
    }
    // 没有缩略图？返回源文件
    return ImageSO.isImageExt(this.ext) ? this.path : null;
  }

  /**
   * 获取缩略图的显示网址，几种情况：
   * 1. 有配置缩略图在数据库？thumbnail?返回这个thumbnail附件
   * 1. 图片？没有配置缩略图？返回原图；
   * 2. 非图片？根据扩展名，返回null，建议前端根据扩展名生成图标，.ISO, .PDF等
   */
  get thumbnailFullUrl(): string | null {
    if (this.thumbnail) {
      return this.getPublicUrlForKey(this.thumbnail);
    }
    // 没有缩略图？返回源文件
    return ImageSO.isImageExt(this.ext) ? this.fullPath : null;
  }

  /**
   * 获取预览图的路径
   */
  get previewUrl() {
    if (this.preview) {
      return `${this.preview}`;
    }
    // 没有缩略图？返回源文件
    return ImageSO.isImageExt(this.ext) ? this.path : null;
  }

  get previewFullUrl() {
    if (this.preview) {
      return this.getPublicUrlForKey(this.preview);
    }
    // 没有缩略图？返回源文件
    return ImageSO.isImageExt(this.ext) ? this.fullPath : null;
  }

  /**
   * 获取下载地址
   *
   * 仅用于下载源文件, 1小时有效期
   * 获取能够把附件下载的URL链接，由客户端HTTP GET请求下载
   *
   * @param customFileName 自定义下载名称
   * @returns downloadable url
   */
  async getPresignedGetUrl(customFileName?: string): Promise<string> {
    const fileName = customFileName || this.objectName;

    return bikaAttachmentDAO.presignedGetObject(this.path, 60 * 60, fileName);
  }

  static async findManyByEtags(etags: string[]): Promise<AttachmentSO[]> {
    const attachments = await bikaAttachmentDAO.listByEtags(etags);
    return attachments.map((attachment) => new AttachmentSO(attachment));
  }

  static async findIdsByIds(ids: string[]): Promise<string[]> {
    return bikaAttachmentDAO.listIdsByIds(ids);
  }

  static async generateImages(
    bo: AIImageBO & Pick<AIGenerateImageProps, 'n' | 'prompt' | 'size'>,
    coinAccount: CoinAccountSO,
  ): Promise<AttachmentSO[]> {
    const imageBOConfig = AIImageBOConfigs[bo.type];

    const images = await AISO.generateImages({
      n: bo.n,
      size: bo.size,
      prompt: `
${imageBOConfig.systemPrompt}      
${bo.prompt}
`,
      imageModel: imageBOConfig.imageModel,
    });

    const completionBO: AICompletionBO = {
      kind: 'image',
      image: bo,
    };

    const aiUsage: AIUsage = {
      model: imageBOConfig.imageModel,
      costCredit: imageBOConfig.costCredit,
    };

    const completionId = generateNanoID('aic');
    // 记录 AI Completion 和 Log，可异步
    await Promise.all([
      coinAccount.redeem(imageBOConfig.costCredit, {
        reason: 'cost-ai-completion-credit',
        completionId,
      }),
      db.mongo.aiCompletion.create({
        id: completionId,
        kind: 'image' as AICompletionKind,
        bo: completionBO,
        usage: aiUsage,
      }),
      db.log.write({
        kind: 'AI_LOG',
        data: JSON.stringify({
          type: 'AI_COMPLETION',
          completion: completionBO,
        }),
        usage: JSON.stringify(aiUsage),
      }),
    ]);

    const attachments: AttachmentSO[] = [];
    for (const image of images) {
      const imageBytes = image.uint8Array;
      // imageBytes to Buffer
      const buffer = Buffer.from(imageBytes);

      const attachment = await AttachmentSO.createByBufferObject(
        {
          buffer,
          contentType: 'image/png', // Assuming PNG, adjust if needed
          size: buffer.length,
        },
        'ai-images/',
      );
      assert(attachment, 'Attachment creation failed');
      attachments.push(attachment);
    }

    return attachments;
  }

  /**
   * 通过二进制创建文件，场景：邮件附件
   *
   * @param obj
   * @param prefix
   * @returns
   */
  static async createByBufferObject(
    obj: {
      buffer: Buffer;
      contentType: string;
      fileName?: string;
      size: number;
    },
    prefix: string = 'default/',
  ): Promise<AttachmentSO | undefined> {
    const { buffer, contentType, fileName, size } = obj;
    const extension = mime.extension(contentType) || (fileName && path.extname(fileName));
    if (!extension) {
      return undefined;
    }
    const ext = extension.startsWith('.') ? extension : `.${extension}`;
    const storePath = generateNanoID(prefix) + ext;
    // 直接上传
    const info = await bikaAttachmentDAO.putObjectBuffer(storePath, buffer);
    const attachmentCreateInput: Prisma.AttachmentCreateInput = {
      id: generateNanoID(CONST_PREFIX_ATTACHMENT),
      path: storePath,
      size,
      ext,
      etag: info.etag,
    };

    const attachmentPO = await bikaAttachmentDAO.create({ data: attachmentCreateInput });
    const attach = new AttachmentSO(attachmentPO);

    if (ImageSO.isImageExt(ext)) {
      await attach.generateThumbnailAndPreview();
    }
    return attach;
  }

  static createByCallback(
    buffer: Buffer,
    ext: string,
    prefix: string = 'default/',
  ): {
    id: string;
    path: string;
    uploadFile: () => Promise<void>;
  } {
    const extName = ext.startsWith('.') ? ext : `.${ext}`;
    const storePath = generateNanoID(prefix) + extName;
    const id = generateNanoID(CONST_PREFIX_ATTACHMENT);
    const uploadFile = async () => {
      // 直接上传
      const info = await bikaAttachmentDAO.putObjectBuffer(storePath, buffer);
      const attachmentCreateInput: Prisma.AttachmentCreateInput = {
        id,
        path: storePath,
        size: buffer.length,
        ext,
        etag: info.etag,
      };
      const attachmentPO = await bikaAttachmentDAO.create({ data: attachmentCreateInput });
      const attach = new AttachmentSO(attachmentPO);
      if (ImageSO.isImageExt(ext)) {
        await attach.generateThumbnailAndPreview();
      }
    };

    return {
      id,
      path: storePath,
      uploadFile,
    };
  }

  /**
   * 通过本地文件创建附件
   *
   * 常见使用场景：
   * - 单元测试
   * - 第三方帐号头像URL快速下载到本地并上传附件
   *
   * @param localFilePath 本地文件路径
   * @param prefix 附件路径前缀，保存的bucket地方，默认是default/
   * @returns attachment
   */
  static async createByLocalFile(localFilePath: string, prefix: string = 'default/') {
    if (!fs.existsSync(localFilePath)) {
      throw new Error(`File not exist: ${localFilePath}`);
    }
    // 存储路径
    const baseName = generateNanoID(prefix);
    const ext = path.extname(localFilePath);
    const storePath = baseName + ext;
    // 直接上传
    await bikaAttachmentDAO.fPutObject(storePath, localFilePath);
    // 获取文件信息
    const attachStat = await bikaAttachmentDAO.statObject(storePath);
    const attachmentCreateInput: Prisma.AttachmentCreateInput = {
      id: generateNanoID(CONST_PREFIX_ATTACHMENT),
      path: storePath,
      etag: attachStat.etag,
      size: attachStat.size,
      ext,
    };
    const attachmentPO = await bikaAttachmentDAO.create({ data: attachmentCreateInput });
    const attach = new AttachmentSO(attachmentPO);
    if (ImageSO.isImageExt(ext)) {
      await attach.generateThumbnailAndPreview();
    }
    return attach;
  }

  /**
   * Create an attachment via PreSignedURL (/tmp/XXX)
   *
   * 这个动作只能由用户完成, 系统不能自己完成这个流程
   *
   * 注意，默认都是/tmp，
   * 在创建附件的时候，进行"重命名操作"，命名规则：
   * /{首次引用的模块名}/{附件标识}.{扩展名}
   *
   * 会自动把/tmp/xxx临时文件删除，如果/tmp/XXX 有残留，就是异常、或被用户滥用了，因为上传完就肯定被移走，定期清理删除
   * 会自动生成缩略图
   *
   * @param user 创建者
   * @param tmpUploadPath 上传的临时文件路径
   * @param prefix 附件路径前缀，保存的bucket地方，默认是default/
   */
  static async createByPresignedPut(
    user: UserSO | null,
    tmpUploadPath: string,
    prefix: string = 'default/',
  ): Promise<AttachmentSO> {
    // 验证数据库是否有这个临时文件URL记录？
    const tmpAttachment = await TmpAttachmentSO.init(tmpUploadPath);
    if (!tmpAttachment) {
      throw new Error(`TmpAttachment not found: ${tmpUploadPath}`);
    }
    const tmpAttachmentModel: ITmpAttachmentModel = tmpAttachment.model;
    const attachmentModel = await bikaAttachmentDAO.getOrCreateAttachmentModel(
      tmpAttachmentModel,
      prefix,
      user ? user.id : undefined,
    );

    const attachment2 = new AttachmentSO(attachmentModel as AttachmentModel);

    if (ImageSO.isImageExt(attachmentModel.ext)) {
      await attachment2.generateThumbnailAndPreview();
    }
    return attachment2;
  }

  /**
   * 生成Thumbnail缩略图和Preview图，通过sharp(ImageSO)进行resize
   *
   * @param sourceFilePath 源文件本地路径
   * @param objectPath 对象存储名称
   */
  protected async generateThumbnailAndPreview() // sourceFilePath: string,
  // objectPath: string,
  : Promise<{ thumbnailPath: string; previewPath: string }> {
    // 下载到临时文件
    let thumbnailLocalPath = '';
    let previewLocalPath = '';
    let sourceFilePath = '';

    try {
      sourceFilePath = await this.downloadToTmpFile();

      const objectPath = this.path;
      assert(fs.existsSync(sourceFilePath), `file not exist: ${sourceFilePath}`);

      const dirname = path.dirname(objectPath);
      const extname = path.extname(objectPath);
      const correctExtname = extname === '.svg' ? '.png' : extname;

      // 进行resize
      const thumbnailImage = new ImageSO(sourceFilePath);
      thumbnailLocalPath = path.join(os.tmpdir(), generateNanoID('thumb') + correctExtname);
      const previewImage = new ImageSO(sourceFilePath);
      previewLocalPath = path.join(os.tmpdir(), generateNanoID('preview') + correctExtname);
      try {
        // 使用顺序执行代替Promise.all，避免类型错误
        await thumbnailImage.resize(thumbnailLocalPath, 80, null);
        await previewImage.resize(previewLocalPath, 150, null);
      } catch (error) {
        console.error(`sharp resize image failed, this image is ${sourceFilePath}, error:`, error);
        throw error;
      }

      // 缩略图生成完毕，重新上传
      // 将path重新分解成，文件名 和 扩展名，对文件名增加_thumbnail，再重新组装出path
      const filename = path.basename(objectPath, extname);

      const thumbnailNewPath = path.join(dirname, `${filename}_thumbnail${correctExtname}`);
      const previewNewPath = path.join(dirname, `${filename}_preview${correctExtname}`);
      try {
        // 使用顺序执行代替Promise.all，避免类型错误
        await bikaAttachmentDAO.fPutObject(thumbnailNewPath, thumbnailLocalPath);
        await bikaAttachmentDAO.fPutObject(previewNewPath, previewLocalPath);
      } catch (error) {
        console.error(`fPutObject failed, this image is ${thumbnailNewPath}, ${previewNewPath}, error:`, error);
        throw error;
      }

      // 二次发起DB请求细改
      const newPO = await bikaAttachmentDAO.update({
        where: { id: this.id },
        data: {
          thumbnail: thumbnailNewPath,
          preview: previewNewPath,
        },
      });
      this._model = newPO;
      return { thumbnailPath: thumbnailNewPath, previewPath: previewNewPath };
    } catch (err) {
      console.error(`generateThumbnailAndPreview error: `, err);
      throw err;
    } finally {
      // 上传完毕，记得删本地
      if (thumbnailLocalPath && fs.existsSync(thumbnailLocalPath)) {
        fs.unlinkSync(thumbnailLocalPath);
      }
      if (previewLocalPath && fs.existsSync(previewLocalPath)) {
        fs.unlinkSync(previewLocalPath);
      }
      if (sourceFilePath && fs.existsSync(sourceFilePath)) {
        fs.unlinkSync(sourceFilePath);
      }
    }
  }

  /**
   * 删除附件
   */
  async delete(): Promise<void> {
    await bikaAttachmentDAO.delete(this.id);
  }

  static async deleteAttachment(id: string): Promise<void> {
    const attachment = await AttachmentSO.init(id);
    if (!attachment) {
      throw new Error(`Attachment not found, id: ${id}`);
    }
    // 删除数据库记录
    await bikaAttachmentDAO.delete(id);
    // 删除对象存储
    await bikaAttachmentDAO.removeObject(attachment.path);
    // 删除缩略图
    if (attachment.thumbnail) {
      await bikaAttachmentDAO.removeObject(attachment.thumbnail);
    }
    // 删除预览图
    if (attachment.preview) {
      await bikaAttachmentDAO.removeObject(attachment.preview);
    }
  }

  /**
   * 调整引用计数
   * @param adjustRefCount 调整递增或递减的数量，默认是递增+1, 可设置负值作为递减
   */
  async adjustRefCount(adjustRefCount: number = 1): Promise<void> {
    this._model = await AttachmentSO.adjustRefCountOperation(this.id, adjustRefCount);
  }

  /**
   * 调整引用计数的数据库操作
   * @param attachmentId attachment id
   * @param refCount 递增或递减的数量
   * @returns attachment db operation
   */
  static adjustRefCountOperation(attachmentId: string, refCount: number = 1): PrismaPromise<AttachmentModel> {
    if (refCount === 0) {
      throw new Error('refCount must be non-zero');
    }
    const refCountInput: Prisma.IntFieldUpdateOperationsInput =
      refCount > 0 ? { increment: refCount } : { decrement: -refCount };

    return bikaAttachmentDAO.update({
      where: { id: attachmentId },
      data: { refCount: refCountInput },
    });
  }

  toVO(): AttachmentVO {
    return {
      id: this.id,
      path: this.path,
      size: this.size,
      bucket: bikaAttachmentDAO.minioBucketName,
      mimeType: mime.lookup(this.ext) || 'application/octet-stream',
      name: this.objectName,
    };
  }

  /**
   * 下载文件成本地文件
   * @returns file path
   */
  async downloadToTmpFile() {
    // 从minio下载到本地文件里, 调用者记得删掉
    let ext = path.extname(this.path);
    if (ext.startsWith('.')) {
      ext = ext.slice(1);
    }
    const fileName = `tmp.${ext}`;
    const dirPath = path.join(os.tmpdir(), generateNanoID(''));
    const localFilePath = path.join(dirPath, fileName);
    await fs.promises.mkdir(dirPath, { recursive: true });
    await bikaAttachmentDAO.fGetObject(this.path, localFilePath);
    return localFilePath;
  }
}
