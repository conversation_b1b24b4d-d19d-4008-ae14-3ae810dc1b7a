import { generateNanoID } from 'basenext/utils/nano-id';
import type { CopyConditions } from 'minio';
import type { ClientSession } from 'mongoose';
import { db, type Prisma, type PrismaPromise, TmpAttachmentModel } from '@bika/server-orm';
import type { AttachmentModel } from '../types';
import { BaseAttachmentDAO } from './base-attachment-dao';
import type { ITmpAttachmentModel } from './types';

export class BikaAttachmentDAO extends BaseAttachmentDAO {
  async deleteTmpAttachmentModel(id: string, session: ClientSession | undefined) {
    await db.mongo.tmpAttachment.deleteOne({ id }, { session });
  }

  async getTmpAttachmentModelByPath(path: string): Promise<ITmpAttachmentModel> {
    const model = await db.mongo.tmpAttachment.findOne({ path });
    return model;
  }

  async getTmpAttachmentModelById(id: string): Promise<ITmpAttachmentModel> {
    const model = await db.mongo.tmpAttachment.findOne({ id });
    return model;
  }

  async createTmpAttachmentModel(presignedPutUrl: string, storePath: string, userId: string | undefined) {
    const data: TmpAttachmentModel = {
      id: generateNanoID('tmp'),
      presignedPutUrl,
      path: storePath,
      createdBy: userId,
      updatedBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    const model = await db.mongo.tmpAttachment.create(data);
    return model;
  }

  getMinioInternalEndpoint(): string {
    return db.minioEndpoint;
  }

  getMinioPublicEndpoint(): string {
    return db.minioPublicEndpoint;
  }

  async minioGetPresignedPut(objectName: string, expiry: number): Promise<string> {
    const presignedPutUrl = await db.minio.presignedPutObject(db.minioBucketName, objectName, expiry); // 1 hour
    return presignedPutUrl;
  }

  async copyObject(newPath: string, sourceObject: string, copyCond: CopyConditions) {
    await db.minio.copyObject(this.minioBucketName, newPath, sourceObject, copyCond);
  }

  async getObject(path: string) {
    const stream = await db.minio.getObject(db.minioBucketName, path);
    return stream;
  }

  removeObject(path: string) {
    return db.minio.removeObject(this.minioBucketName, path);
  }

  async statObject(storePath: string) {
    const attachStat = await db.minio.statObject(this.minioBucketName, storePath);
    return attachStat;
  }

  async fGetObject(path: string, localFilePath: string) {
    return db.minio.fGetObject(this.minioBucketName, path, localFilePath);
  }

  async presignedGetObject(
    path: string,
    expiry: number,
    customFileName: string,
  ): Promise<string | PromiseLike<string>> {
    // 设置下载时的文件名
    const responseHeaders = {
      'response-content-disposition': `attachment; filename="${customFileName}"`,
    };
    const presignedGetUrl = await db.minio.presignedGetObject(this.minioBucketName, path, expiry, responseHeaders);
    // 内网服务端点替换
    if (this.publicEndpoint.startsWith('/')) {
      const replacePresignedGetUrl = presignedGetUrl.replace(this.getMinioInternalEndpoint(), '');
      return replacePresignedGetUrl;
    }

    return presignedGetUrl;
  }

  get minioBucketName() {
    return db.minioBucketName;
  }

  get publicEndpoint() {
    return db.minioPublicEndpoint;
  }

  putObjectBuffer(storePath: string, buffer: Buffer) {
    return db.minio.putObject(this.minioBucketName, storePath, buffer);
  }

  async fPutObject(objectName: string, filePath: string) {
    return db.minio.fPutObject(db.minioBucketName, objectName, filePath);
  }

  async delete(id: string) {
    await db.prisma.attachment.delete({ where: { id } });
  }

  update(arg: {
    where: { id: string };
    data: { thumbnail?: string; preview?: string; refCount?: Prisma.IntFieldUpdateOperationsInput };
  }): PrismaPromise<AttachmentModel> {
    //  二次发起DB请求细改
    return db.prisma.attachment.update({
      where: { id: arg.where.id },
      data: {
        thumbnail: arg.data.thumbnail,
        preview: arg.data.preview,
        refCount: arg.data.refCount,
      },
    });
  }

  async createAttachmentFromTmp(arg0: { tmpAttachment: ITmpAttachmentModel; data: Prisma.AttachmentCreateInput }) {
    // 确保文件操作完成后再落库
    const attachmentPO = await db.prisma.$transaction(async (transaction) => {
      const [model] = await Promise.all([
        // 保存附件记录
        transaction.attachment.create({ data: arg0.data }),
        // 删除临时附件记录
        this.deleteTmpAttachmentModel(arg0.tmpAttachment.id, undefined),
      ]);
      return model;
    });
    return attachmentPO;
  }

  async create(args: { data: Prisma.AttachmentCreateInput }) {
    const attachmentPO = await db.prisma.attachment.create({ data: args.data });
    return attachmentPO;
  }

  async get(etag: string): Promise<AttachmentModel | null> {
    const existAttachModel = await db.prisma.attachment.findFirst({ where: { etag } });
    return existAttachModel;
  }

  async init(id: string): Promise<AttachmentModel> {
    const attachmentPO = await db.prisma.attachment.findUnique({ where: { id } });
    if (!attachmentPO) {
      throw new Error(`Attachment not found, id: ${id}`);
    }
    return attachmentPO;
  }

  async initMaybeNull(id: string): Promise<AttachmentModel | null> {
    const attachmentPO = await db.prisma.attachment.findUnique({ where: { id } });
    if (!attachmentPO) {
      return null;
    }
    return attachmentPO;
  }

  async listByEtags(etags: string[]): Promise<AttachmentModel[]> {
    const attachments = await db.prisma.attachment.findMany({ where: { etag: { in: etags } } });
    return attachments;
  }

  async list(ids: string[]): Promise<AttachmentModel[]> {
    if (ids.length === 0) {
      return [];
    }
    const models = await db.prisma.attachment.findMany({ where: { id: { in: ids } } });
    return models;
  }

  async listIdsByIds(ids: string[]): Promise<string[]> {
    return db.prisma.attachment
      .findMany({ where: { id: { in: ids } }, select: { id: true } })
      .then((attachments) => attachments.map((attachment) => attachment.id));
  }
}
