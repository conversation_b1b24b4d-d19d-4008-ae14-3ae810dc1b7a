import assert from 'assert';
import path from 'node:path';
import { generateNanoID } from 'basenext/utils/nano-id';
import mime from 'mime-types';
import type { CopyConditions } from 'minio';
import { mongo } from 'mongoose';
import { Minio, type Prisma } from '@bika/server-orm';
import type { AttachmentVO, PresignedPutVO } from '@bika/types/attachment/vo';
import { CONST_PREFIX_ATTACHMENT } from '@bika/types/database/vo';
import type { IAttachmentModel, ITmpAttachmentModel } from './types';

export abstract class BaseAttachmentDAO {
  abstract statObject(storePath: string): Promise<{ metaData: Record<string, string>; etag: string; size: number }>;

  abstract deleteTmpAttachmentModel(id: string, session: mongo.ClientSession | undefined): Promise<void>;

  /**
   * 存储的对象名,比如 folder/1234.png, 返回 1234.png
   */
  getObjectName(model: IAttachmentModel): string {
    const pathName = path.basename(model.path);
    return path.extname(pathName) === '' ? pathName + model.ext : pathName;
  }

  attachmentModelToVO(model: IAttachmentModel): AttachmentVO {
    return {
      id: model.id,
      path: model.path,
      size: model.size,
      bucket: this.minioBucketName,
      mimeType: mime.lookup(model.ext) || 'application/octet-stream',
      name: this.getObjectName(model),
    };
  }

  /**
   * PresignedPutVO
   *
   * @param model
   * @returns
   */
  tmpAttachmentModelToVO(model: ITmpAttachmentModel): PresignedPutVO {
    return {
      id: model.id,
      presignedPutUrl: model.presignedPutUrl,
      path: model.path,
    };
  }

  abstract get(etag: string): Promise<IAttachmentModel | null>;

  abstract removeObject(path: string): Promise<void>;

  abstract get minioBucketName(): string;

  abstract delete(id: string): Promise<void>;

  abstract copyObject(newPath: string, sourceObject: string, copyCond: CopyConditions): Promise<void>;

  abstract createAttachmentFromTmp(arg0: {
    tmpAttachment: ITmpAttachmentModel;
    data: Prisma.AttachmentCreateInput;
  }): Promise<IAttachmentModel>;

  async getOrCreateAttachmentModel(
    tmpAttachmentModel: ITmpAttachmentModel,
    prefix: string,
    userId: string | undefined,
  ): Promise<IAttachmentModel> {
    const tmpAttachment = tmpAttachmentModel;
    // 继续，验证是否已经真的上传？ 这里会抛异常，证明没有上传成功
    const attachStat = await this.statObject(tmpAttachment.path);
    let ext = path.extname(tmpAttachment.path);
    // 没有扩展名的情况，根据content-type获取
    const remoteExt = mime.extension(attachStat.metaData['content-type']) ?? '';
    ext = ext === '' && remoteExt !== '' ? `.${remoteExt}` : ext;

    // etag重复校对，如果已经存在了，不用新创建，返回旧的就好了
    const existAttachModel = await this.get(attachStat.etag);
    if (existAttachModel) {
      // 数据库已经存在，但是，再次检查数据库的条目，对象存储是否真的存在
      // const existAttachSO = new AttachmentSO(existAttachModel);
      try {
        const existAttachStat = await this.statObject(existAttachModel.path);
        assert(existAttachStat);
        // 真的数据库存在+对象文件存在，删除临时文件，返回原来的附件吧
        await this.removeObject(tmpAttachment.path);
        await this.deleteTmpAttachmentModel(tmpAttachment.id, undefined);
        // await tmpAttachment.delete();
        return existAttachModel;
      } catch (err) {
        // 非常特殊情况，数据库存在，但是对象文件不存在，这个时候，就是数据库记录有问题了，更新数据库记录，重新创建
        console.error(`file: ${attachStat.etag} not exist in storage server but exist in database`, err);
        await this.delete(existAttachModel.id);
        // await existAttachSO.delete();
      }
    }

    // 走到这里，没异常，就是有了，移动附件，并创建Attachment
    const baseName = generateNanoID(prefix);
    const newPath = baseName + ext;
    const attachmentCreateInput: Prisma.AttachmentCreateInput = {
      id: generateNanoID(CONST_PREFIX_ATTACHMENT),
      path: newPath,
      etag: attachStat.etag,
      size: attachStat.size,
      ext,
      createdBy: userId,
      updatedBy: userId,
    };

    // 执行拷贝操作,不需要重复下载上传
    // 注意: 由于文件可能不小,目前200多MB的文件,拷贝动作在transaction里执行会超时
    const sourceObject = `${this.minioBucketName}/${tmpAttachment.path}`;
    const copyCond = new Minio.CopyConditions();
    copyCond.setMatchETag(attachStat.etag);

    await this.copyObject(newPath, sourceObject, copyCond);
    // 删除临时文件
    await this.removeObject(tmpAttachment.path);

    // 确保文件操作完成后再落库
    const newAttachmentPO = await this.createAttachmentFromTmp({
      tmpAttachment: tmpAttachmentModel,
      data: attachmentCreateInput,
    });

    // const newAttachmentPO = await db.prisma.attachment.create({
    //   data: attachmentCreateInput,
    // });

    // const attachment = new AttachmentSO(attachmentPO);

    // if (ImageSO.isImageExt(ext)) {
    //   await attachment.generateThumbnailAndPreview();
    // }
    return newAttachmentPO;
  }

  abstract getTmpAttachmentModelByPath(path: string): Promise<ITmpAttachmentModel>;

  abstract getTmpAttachmentModelById(id: string): Promise<ITmpAttachmentModel>;

  abstract createTmpAttachmentModel(
    presignedPutUrl: string,
    storePath: string,
    userId: string | undefined,
  ): Promise<ITmpAttachmentModel>;

  abstract minioGetPresignedPut(objectName: string, expiry: number): Promise<string>;

  abstract getMinioPublicEndpoint(): string;

  abstract getMinioInternalEndpoint(): string;

  public async getPresignedPut(ext?: string): Promise<{
    presignedPutUrl: string;
    tmpStorePath: string;
  }> {
    let extension;
    if (ext) {
      extension = ext.startsWith('.') ? ext : `.${ext}`;
    } else {
      extension = ext;
    }
    // 存储在s3桶的临时位置
    const tmpStorePath = generateNanoID('tmp/') + (extension ?? '');
    const publicEndpoint = this.getMinioPublicEndpoint();
    const presignedPutUrl = await this.minioGetPresignedPut(tmpStorePath, 1 * 60 * 60);
    // const presignedPutUrl = await db.minio.presignedPutObject(db.minioBucketName, tmpStorePath, 1 * 60 * 60); // 1 hour
    // const publicEndpoint = db.minioPublicEndpoint;
    // console.log(`原上传端点: ${this.getMinioInternalEndpoint()}, 公开访问端点: ${this.getMinioPublicEndpoint()}`);
    if (publicEndpoint.startsWith('/')) {
      // 内网服务端点替换
      const replacePresignedPutUrl = presignedPutUrl.replace(this.getMinioInternalEndpoint(), '');
      // console.log(`替换后的上传地址: ${replacePresignedPutUrl}`);
      return {
        presignedPutUrl: replacePresignedPutUrl,
        tmpStorePath,
      };
    }
    return {
      presignedPutUrl,
      tmpStorePath,
    };
  }

  // delete(id: string): Promise<void>;
}
