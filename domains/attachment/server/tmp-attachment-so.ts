// eslint-disable-next-line max-classes-per-file
import fs from 'node:fs';
import path from 'node:path';
import os from 'os';
import { generateNanoID } from 'basenext/utils/nano-id';
import { UserSO } from '@bika/domains/user/server/user-so';
import { TmpAttachmentModel, mongoose } from '@bika/server-orm';
import { PresignedPutVO } from '@bika/types/attachment/vo';
import { BikaAttachmentDAO } from './dao/bika-attachment-dao';

export const bikaAttachmentDAO = new BikaAttachmentDAO();

/**
 * 临时文件，使用场景：
 * 用户上传的东西暂存，然后才转成AttachmentSO
 * Excel导出的文件暂存
 * 等等等...
 */
export class TmpAttachmentSO {
  private readonly _model: TmpAttachmentModel;

  public get model() {
    return this._model;
  }

  constructor(model: TmpAttachmentModel) {
    this._model = model;
  }

  get presignedPutUrl() {
    return this._model.presignedPutUrl;
  }

  get path(): string {
    return this._model.path;
  }

  static async init(key: string) {
    const model = await bikaAttachmentDAO.getTmpAttachmentModelByPath(key);
    return new TmpAttachmentSO(model);
  }

  static async initById(id: string) {
    const model = await bikaAttachmentDAO.getTmpAttachmentModelById(id);
    return new TmpAttachmentSO(model);
  }

  static async create(user: UserSO | null, presignedPutUrl: string, storePath: string) {
    const model = await bikaAttachmentDAO.createTmpAttachmentModel(presignedPutUrl, storePath, user?.id);
    return new TmpAttachmentSO(model);
  }

  async delete(session?: mongoose.ClientSession) {
    await bikaAttachmentDAO.deleteTmpAttachmentModel(this._model.id, session);
  }

  /**
   * 下载文件成本地文件
   * @returns file path
   */
  async getObjectAsLocalFile() {
    // 从minio下载到本地文件里, 调用者记得删掉
    const ext = path.extname(this.path);
    const fileName = `tmp.${ext}`;
    const dirPath = path.join(os.tmpdir(), generateNanoID(''));
    const localFilePath = path.join(dirPath, fileName);
    await fs.promises.mkdir(dirPath, { recursive: true });
    await bikaAttachmentDAO.fGetObject(this.path, localFilePath);
    return localFilePath;
  }

  toVO(): PresignedPutVO {
    return bikaAttachmentDAO.tmpAttachmentModelToVO(this._model);
  }

  /**
   * 获取临时上传附件的签名地址,交由前端自己上传
   * Generate a pre-signed url for uploading attachment
   * The key prefix is `/tmp/${randomId}`
   * 通过此链接上传key为/tmp/${randomId}.{ext}
   */
  static async getPresignedPut(user: UserSO | null, ext?: string): Promise<PresignedPutVO> {
    const { presignedPutUrl, tmpStorePath } = await bikaAttachmentDAO.getPresignedPut(ext);

    const tmpAttachment = await TmpAttachmentSO.create(user, presignedPutUrl, tmpStorePath);
    return tmpAttachment.toVO();
  }

  /**
   * 上传本地文件到服务器，返回下载一次性URL，供用户下载，
   * 跳过附件存储,直接上传到存储服务,然后提供下载
   *
   * 使用场景：下载excel文件
   *
   * FIX/TODO: 不要直接上传，改TmpAttachment
   *
   * @param localFilePath 本地文件路径
   * @param customFileName 自定义
   */
  static async uploadLocalFileAndGetDownloadUrl(localFilePath: string, customFileName?: string): Promise<string> {
    const ext = path.extname(localFilePath);
    const tmpStorePath = generateNanoID('tmp/') + (ext ?? '');
    // 上传到临时文件存储器
    await bikaAttachmentDAO.fPutObject(tmpStorePath, localFilePath);

    // 设置下载时的文件名
    const fileName = customFileName || path.basename(localFilePath);
    return bikaAttachmentDAO.presignedGetObject(tmpStorePath, 60 * 60, fileName);
  }

  static async uploadLocalFile(localFilePath: string, customFileName: string) {
    const tmpStorePath = `tmp/${customFileName}`;
    await bikaAttachmentDAO.fPutObject(tmpStorePath, localFilePath);
  }
}

/**
 * 下载presignedPutUrl到本地
 *
 * @param presignedPutUrl
 * @param specifiedFileName
 *
 * @deprecated 废弃,放到TmpAttachment里直接调用下载 (getObjectAsLocalFile)
 */
export async function downloadPresignedPutUrlToLocal(presignedPutUrl: string): Promise<string> {
  // 验证数据库是否有这个临时文件URL记录？
  const tmpAttach = await TmpAttachmentSO.init(presignedPutUrl);

  if (!tmpAttach) {
    throw new Error(`TmpAttachment not found, presignedPutURL: ${presignedPutUrl}`);
  }
  // 下载到临时文件夹下面
  const dirPath = path.join(os.tmpdir(), generateNanoID(''));
  const localFilePath = path.join(dirPath, tmpAttach.path);
  await fs.promises.mkdir(dirPath, { recursive: true });
  const file = fs.createWriteStream(localFilePath);
  const stream = await bikaAttachmentDAO.getObject(tmpAttach.path);

  stream.pipe(file);
  return new Promise((resolve, reject) => {
    stream.on('end', () => {
      resolve(localFilePath);
    });

    stream.on('error', (err: Error) => {
      reject(err);
    });
  });
}
