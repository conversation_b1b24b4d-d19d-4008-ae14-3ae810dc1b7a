import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import { test, expect } from 'vitest';
import { db } from '@bika/server-orm';

test('Test Basic OpenObserve Log', async () => {
  const resData = await db.log.write({
    kind: 'ADMIN_LOG',
    type: 'SET_SPACE',
    spaceId: 'test',
  });
  expect(resData).toBe(true);

  const res2 = await db.log.write({
    kind: 'ACCESS_LOG',
    spaceId: 'test',
    headers: {
      test: 'test',
    },
    server: {
      hostname: 'test hostname',
      platform: 'mock test',
    },
    client: {
      hostname: 'test hostname',
      ip: 'test ip',
      version: 'test version',
    },
    action: {
      type: 'http',
      path: 'test spath',
    },
    createdBy: 'test user id',
    createdAt: new Date().toISOString(),
  });
  expect(res2).toBe(true);

  const eventName = `test:${generateNanoID()}`;
  const eventValue = `test:value:${generateNanoID()}`;
  const res3 = await db.log.write({
    kind: 'TRACK_LOG',
    type: 'CUSTOM',
    eventName,
    eventValue,
  });
  expect(res3).toBe(true);

  // OpenObserve has delay for log writing
  const res4 = await db.log.paginationSearch('TRACK_LOG', {
    startTime: dayjs().subtract(5, 'second').toDate(),
    endTime: dayjs().toDate(),
  });

  expect(res4.records.length).toBeGreaterThan(0);
  expect(res4.total).toBeGreaterThan(0);

  // 指定搜索
  const res5 = await db.log.paginationSearch('TRACK_LOG', {
    where: {
      eventname: eventName,
      eventvalue: eventValue,
    },
  });
  expect(res5.records.length).toBe(1);
  expect(res5.total).toBe(1);
});
