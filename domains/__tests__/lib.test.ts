import { modNanoId } from 'basenext/utils/nano-id-crypto';
import _ from 'lodash';
import { mapDirectoriesToFiles } from 'sharelib/fs-utils';
import * as ts from 'typescript';
import { expect, test } from 'vitest';
import { utils } from '@bika/domains/shared/server';
import { SpaceSettingsVO } from '@bika/types/space/vo';

test('Email Format Test', () => {
  expect(utils.isValidEmail('Kelly <PERSON>eilin <PERSON>')).toBe(null);
  expect(utils.isValidEmail('<PERSON> <<EMAIL>>')).not.toBeNull();
  expect(utils.isValidEmail('<EMAIL>')).not.toBeNull();
});

test('Bika Date Time test', async () => {
  // 时间日期输出：2021-08-01 00:00:00
  // 重复输出：At 04:05 on day-of-month 20 and on Tuesday in March.
  // 时间段输出：2021-08-01 00:00:00 ~ 2023-08-01 00:00:00
  // 季度段输出: 2023Q4 ~ 2024Q1
});
test('nanoid mod test', () => {
  const mod = modNanoId('spcVg2SlATmrpO1iK0TQTs1g', 2048);
  expect(mod).toBe(BigInt(156));
});

test('Generate Example Email', () => {
  const email = utils.generateEmail();
  expect(utils.isValidEmail(email)).not.toBeNull();
});

test('JSON parse test', async () => {
  const testJSONParse = `{
    "type": "CREATE_REMINDER", 
    "name": "喊我起床", 
    "datetime": "明天七点" }
  `;
  const testJSONParseObj = JSON.parse(testJSONParse);
  expect(testJSONParseObj.type).toBe('CREATE_REMINDER');
});

test('Lodash Template If', () => {
  const tpl = _.template('Hello, <%= name %>.  <% if (age > 18) { %>You are an adult.<% } %>');

  const str = tpl({ name: 'Fred', age: 19 });
  expect(str).toBe('Hello, Fred.  You are an adult.');
});

test('Lodash Diff Keys', () => {
  const oldVO: SpaceSettingsVO = {
    allowEmailDomains: ['bika.ai', 'vikadata.com', 'aitable.ai'],
    // memberJoinAnnouncement: '欢迎加入我们的空间站！这是standup空间站！',
    watermark: true,
    onboardingStage: 'DONE',
  };

  const newVO: SpaceSettingsVO = {
    allowEmailDomains: ['bika.ai', 'aitable.ai'],
    // memberJoinAnnouncement: '欢迎加入我们的空间站！',
    watermark: false,
    onboardingStage: 'INIT',
  };
  // 获取差异的key，即哪些key被修改了? 注意，被删了，是察觉不到的
  const diffKeys = _.differenceWith(Object.keys(newVO), Object.keys(oldVO), (keyA, keyB) =>
    _.isEqual((oldVO as any)[keyA], (newVO as any)[keyB]),
  );

  expect(diffKeys).toStrictEqual(['allowEmailDomains', 'watermark', 'onboardingStage']);
});

test('Test content loader basic', async () => {
  const pages = mapDirectoriesToFiles('../contents/pages');
  const cnHome = 'zh-CN/index';
  expect(_.includes(pages, cnHome)).toBeTruthy();

  const page = await import(`@bika/contents/pages/${cnHome}`);
  expect(page).toBeDefined();
  expect(page.default.metadata.description).toContain('AI');

  const code = `
const test = {
  a: 1,
  b: 2,
};

export default test;
`;
  const script = ts.transpile(code);
  // eslint-disable-next-line no-eval
  const runnalbe: any = eval(script);
  expect(runnalbe.a).toBe(1);
});
