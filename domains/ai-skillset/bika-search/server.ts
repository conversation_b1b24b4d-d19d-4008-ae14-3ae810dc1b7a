import { tavily, type Tavi<PERSON><PERSON>lient } from '@tavily/core';
import { inputFieldsToZodSchema } from '@toolsdk.ai/sdk-ts/utils';
import type { Tool, ToolSet } from 'ai';
import { z } from 'zod';
import { isInCI } from 'sharelib/app-env';
import { ToolSDKAISO } from '../../automation-nodes/toolsdk-action/toolsdk-ai-so';
import { ToolSetHandler } from '../types';

const fetchBraveSearchMCPToolSet = async (): Promise<ToolSet> => {
  const packageKey = 'brave-search-mcp';
  const packageVersion = undefined;
  const brave = await ToolSDKAISO.getPackage(packageKey, packageVersion);

  const imageSearchToolKey = 'brave_image_search';
  const braveImageSearch = brave.tools.find((tool) => tool.key === imageSearchToolKey);
  if (!braveImageSearch) {
    return {};
  }
  const tool: Tool = {
    description: braveImageSearch.description,
    parameters: inputFieldsToZodSchema(braveImageSearch.inputFields),
    execute: async (params) =>
      ToolSDKAISO.runPackageTool({
        packageKey,
        packageVersion,
        envs: {
          BRAVE_API_KEY: process.env.BRAVE_API_KEY || '',
        },
        body: {
          toolKey: imageSearchToolKey,
          inputData: params,
        },
      }),
  };
  return { bika_search_images: tool };
};

const createBikaSearchPagesTool = (tvly: TavilyClient): Tool => ({
  description: 'Search for web pages based on a query.',
  parameters: z.object({
    query: z.string().describe('The search query to search pages'),
    auto_parameters: z
      .boolean()
      .optional()
      .describe(
        'When auto_parameters is enabled, Tavily automatically configures search parameters based on your query’s content and intent.',
      ),
    searchDepth: z
      .enum(['basic', 'advanced'])
      .default('basic')
      .describe('The depth of the search. It can be "basic" or "advanced".'),
    topic: z
      .enum(['general', 'news'])
      .default('general')
      .describe('The category of the search. Determines which agent will be used.'),
    days: z
      .number()
      .optional()
      .describe(
        'The number of days back from the current date to include in the results. Available only when using the "news" topic.',
      ),
    timeRange: z
      .string()
      .optional()
      .describe(
        'The time range back from the current date. Accepted values include "day", "week", "month", "year" or shorthand values "d", "w", "m", "y".',
      ),
    maxResults: z.number().int().min(0).max(20).default(5).describe('The maximum number of search results to return.'),
    chunksPerSource: z
      .number()
      .int()
      .default(3)
      .describe('Chunks are short content snippets (maximum 500 characters each) pulled directly from the source.'),
    includeImages: z.boolean().default(false).describe('Include a list of query-related images in the response.'),
    includeImageDescriptions: z
      .boolean()
      .default(false)
      .describe('Include a list of query-related images and their descriptions in the response.'),
    includeAnswer: z
      .union([z.boolean(), z.string()])
      .default(false)
      .describe('Include an answer to the query generated by an LLM based on search results.'),
    includeRawContent: z
      .union([z.boolean(), z.string()])
      .default(false)
      .describe('Include the cleaned and parsed HTML content of each search result.'),
    includeDomains: z
      .array(z.string())
      .default([])
      .describe('A list of domains to specifically include in the search results.'),
    excludeDomains: z
      .array(z.string())
      .default([])
      .describe('A list of domains to specifically exclude from the search results.'),
    country: z.string().optional().describe('Boost search results from a specific country.'),
    timeout: z.number().default(60).describe('A timeout to be used in requests to the Tavily API.'),
  }),
  execute: async (params) => {
    const { query, ...restParams } = params;
    const response = await tvly.search(query, {
      includeFavicon: true,
      ...restParams,
    });
    return response;
  },
});
const tools: ToolSetHandler = async () => {
  if (isInCI()) {
    return Promise.resolve({});
  }

  const [braveToolSet] = await Promise.all([fetchBraveSearchMCPToolSet()]);

  const TAVILY_API_KEY = process.env.TAVILY_API_KEY || '';
  const tvly = tavily({ apiKey: TAVILY_API_KEY });

  return {
    bika_search_pages: createBikaSearchPagesTool(tvly),
    ...braveToolSet,
  };
};

export default tools;
