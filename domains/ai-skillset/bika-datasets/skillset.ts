import type { Tool } from 'ai';
import { z } from 'zod';
import { AIMockSO } from '@bika/domains/ai/server/ai-mock/ai-mock-so';
import { getAppEnv } from 'sharelib/app-env';
import { SkillsetHandler } from '../types';
import { ExaWebsetsSO } from './exa-websets-so';
import { buildGridArtifactData } from './helper';
import { companyToolMockData, peopleToolMockData } from './tool-mock-data';

const fetchWebsetTool = (entityType: 'company' | 'person', description: string): Tool => ({
  description,
  parameters: z.object({
    query: z.string().min(1).max(5000).describe('Natural language search query describing what you are looking for.'),
    count: z
      .number()
      .min(1)
      .default(10)
      .describe(
        'Number of Items will attempt to find. The actual number of Items found may be less than this number depending on the search complexity.',
      ),
    criteria: z
      .object({
        description: z.string().describe('The description of the criterion'),
      })
      .array()
      .max(5)
      .optional()
      .describe('Criteria every item is evaluated against.'),
    recall: z
      .boolean()
      .optional()
      .describe(
        'Whether to provide an estimate of how many total relevant results could exist for this search. Result of the analysis will be available in the `recall` field within the search request.',
      ),
    enrichments: z
      .object({
        description: z
          .string()
          .describe('Provide a description of the enrichment task you want to perform to each Webset Item.'),
        format: z
          .enum(['text', 'date', 'number', 'options', 'email', 'phone'])
          .describe('Format of the enrichment response.'),
      })
      .array()
      .optional()
      .describe('Add enrichments to extract additional data from found items.'),
    timeout: z
      .number()
      .default(180000)
      .optional()
      .describe('Timeout in milliseconds for the search operation. Defaults to 180 seconds.'),
  }),
  execute: async (params) => {
    const { query, count, criteria, enrichments, timeout } = params;

    if (['LOCAL', 'INTEGRATION'].includes(getAppEnv())) {
      return entityType === 'company' ? companyToolMockData : peopleToolMockData;
    }

    const isMockAI = await AIMockSO.isMockAI();
    if (isMockAI) {
      return entityType === 'company' ? companyToolMockData : peopleToolMockData;
    }

    // Create a Webset with search and enrichments
    const webset = await ExaWebsetsSO.createWebset({
      search: {
        query,
        count,
        criteria,
        entity: {
          type: entityType,
        },
      },
      enrichments,
    });

    // Wait until Webset completes processing
    await ExaWebsetsSO.waitWebsetUntilIdle(webset.id, timeout);

    // Retrieve Webset Items
    const items = await ExaWebsetsSO.getWebsetAllItems(webset.id, {
      limit: count,
    });

    return buildGridArtifactData(items, entityType);
  },
});

const skillsetHandler: SkillsetHandler = async (_ctx) => ({
  key: 'bika-dataset',
  display: {
    label: 'Bika Datasets',
    description: 'Datasets includes stocks, people, companies, and more',
  },
  logo: {
    type: 'PRESET',
    url: '/assets/ai/skillset/document.png',
  },
  toolsetHandler: async () => ({
    companies: fetchWebsetTool(
      'company',
      'Search websets(datasets) of companies, like Crunchbase, LinkedIn, and more.',
    ),
    people: fetchWebsetTool('person', 'Search people datasets, like LinkedIn, and more.'),
  }),
});

export default skillsetHandler;
