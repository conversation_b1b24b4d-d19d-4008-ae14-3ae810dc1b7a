/* eslint-disable max-classes-per-file */
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { getAiSDKHttpMcpClient, getAiSDKSseMcpClient } from '@toolsdk.ai/orm/prisma/dao/mcp-helper';
import { PackageListRequestScope } from '@toolsdk.ai/sdk-ts/types/dto';
import { inputFieldsToZodSchema, integrationTemplateToVO } from '@toolsdk.ai/sdk-ts/utils';
import type { Tool, ToolSet } from 'ai';
import { iStringParse } from 'basenext/i18n/i-string';
import { Locale } from '@bika/contents/i18n';
import { PresetSkillsetType, PresetSkillsetTypes, SkillsetSelectBO } from '@bika/types/skill/bo';
import { SkillsetVO, SkillVO } from '@bika/types/skill/vo';
// import { isInCI } from 'sharelib/app-env';
import { SkillsetHandlerContext, SkillsetHandler } from './types';
import { ToolSDKAISO } from '../automation-nodes/toolsdk-action/toolsdk-ai-so';
import type { UserSO } from '../user/server/user-so';

interface SkillsetSearchArgs {
  query?: string;
  locale?: Locale;
  pageNo: number;
  pageSize: number;
  // 前面分类累计已加载的总数
  prevTotal: number;
  scope?: PackageListRequestScope;
}

interface KindSearchResult {
  currentPageSkillsets: SkillsetVO[];
  kindTotal: number;
}

export abstract class SkillsetKind {
  /**
   * Get the SkillsetVO based on the provided select object.
   * Returns a SkillsetVO or undefined if not found.
   */
  abstract getSkillsetVO(select: SkillsetSelectBO, locale?: Locale): Promise<SkillsetVO | undefined>;

  /**
   * Search for skillsets based on the provided arguments.
   * Returns a paginated result or undefined if no matches are found.
   */
  abstract searchSkillsetVO(args: SkillsetSearchArgs): Promise<KindSearchResult | undefined>;

  abstract getToolset(select: SkillsetSelectBO, props: SkillsetHandlerContext): Promise<ToolSet>;
}

// export class AutomationSkillsetKind extends SkillsetKind {
//   override async getSkillsetVO(select: SkillsetSelectBO, locale?: Locale): Promise<SkillsetVO> {
//     const skills = this.getActionSkills(locale);
//     return {
//       kind: 'automation',
//       key: select.key,
//       name: 'Automation Actions',
//       description: 'Automation Actions',
//       logo: {
//         type: 'PRESET',
//         url: '/assets/ai/skillset/automation_actions.png',
//       },
//       skills,
//     };
//   }

//   override searchSkillsetVO(args: SkillsetSearchArgs): SkillsetVOPagination | undefined {
//     const { query, locale, pageNo, pageSize } = args || {};

//     // automation only has one skillset actions now, so pageNo must be 1
//     if (pageNo !== 1) {
//       return undefined;
//     }

//     const skills = this.getActionSkills(locale);
//     const matchedSkills = skills.filter((skill) => {
//       if (!query) {
//         return true;
//       }
//       const keyword = query.toLowerCase();
//       return skill.name.toLowerCase().includes(keyword) || skill.description?.toLowerCase().includes(keyword);
//     });
//     if (matchedSkills.length === 0) {
//       return undefined;
//     }
//     const actionSkillset: SkillsetVO = {
//       kind: 'automation',
//       key: 'automation-actions',
//       name: 'Automation Actions',
//       description: 'Automation Actions',
//       skills: matchedSkills,
//     };
//     return {
//       pagination: {
//         pageNo,
//         pageSize,
//         total: 1,
//       },
//       data: [actionSkillset],
//     };
//   }

//   override async getToolset(select: SkillsetSelectBO, _props: SkillsetHandlerContext): Promise<ToolSet> {
//     if (select.kind !== 'automation') {
//       throw new Error(`Skillset kind ${select.kind} is not supported for Automation`);
//     }
//     const { includes } = select;
//     if (!includes || includes.length === 0) {
//       return {};
//     }
//     return {};
//   }

//   private getActionSkills(locale?: Locale): SkillVO[] {
//     const skills: SkillVO[] = [];
//     const localeContext = getServerLocaleContext(locale);
//     for (const [key, value] of Object.entries(getActionTypesConfig(localeContext))) {
//       if (value.display !== 'SHOW' || key === 'TOOLSDK_AI') {
//         continue;
//       }
//       skills.push({
//         key,
//         name: value.label,
//         description: value.description,
//         logo: {
//           type: 'PRESET',
//           url: value.iconPath,
//         },
//       });
//     }
//     return skills;
//   }
// }

export class ToolsetSkillsetKind extends SkillsetKind {
  private _user: UserSO;

  constructor(user: UserSO) {
    super();
    this._user = user;
  }

  override async getSkillsetVO(select: SkillsetSelectBO): Promise<SkillsetVO | undefined> {
    return this.findSkillsetVO(select.key);
  }

  override async searchSkillsetVO(args: SkillsetSearchArgs): Promise<KindSearchResult> {
    const { query, pageNo, pageSize, prevTotal } = args;

    const offset = (pageNo - 1) * pageSize;

    const spliceKindPageSkillsets = async (
      kindTotal: number,
      fetchCurrentPageSkillsets: (start: number, end: number) => Promise<SkillsetVO[]> | SkillsetVO[],
    ): Promise<KindSearchResult> => {
      // 前面的分类已经加载了 prevTotal 个技能集，加上本分类 kindTotal 个技能集，
      // 需要返回 (pageNo-1) * pageSize  - pageNo * pageSize 范围的技能集
      const total = prevTotal + kindTotal;
      // 如果本分类没有技能集，或者技能集数量小于等于偏移量，或者前面的分类已经加载了足够的分页数据，那么直接返回本分类的数量，用于总数累计
      if (kindTotal === 0 || total <= offset || prevTotal >= offset + pageSize) {
        return { currentPageSkillsets: [], kindTotal };
      }

      /**
       * 在本分类中，截取当前分页的技能集数据。
       *
       * 场景说明：
       * 假设 kindTotal = 7，prevTotal = 0，pageSize = 5，pageNo = 1 (offset = 0)，
       * 那么从当前分类中获取第 1 个到第 5 个技能集。
       *
       * 假设 kindTotal = 7，prevTotal = 0，pageSize = 5，pageNo = 2 (offset = 5)，
       * 那么从当前分类中获取第 6 个到第 7 个技能集。
       *
       * 假设 kindTotal = 7，prevTotal = 7，pageSize = 5，pageNo = 2 (offset = 5)，
       * 那么从当前分类中获取第 1 个到第 3 个技能集。
       */

      // 计算技能集的起始和结束下标
      const startIndex = Math.max(0, offset - prevTotal);
      const endIndex = Math.min(kindTotal, startIndex + pageSize, offset + pageSize - prevTotal);
      // 加载技能集
      const currentPageSkillsets: SkillsetVO[] = await fetchCurrentPageSkillsets(startIndex, endIndex);
      return { currentPageSkillsets, kindTotal };
    };

    // 如果没有查询条件，返回所有预设的技能集
    if (!query || query.trim().length === 0) {
      // 遍历 PresetSkillsetSelectTypes，从 startIndex 到 endIndex 获取技能集
      const fetchCurrentPageSkillsets = async (startIndex: number, endIndex: number): Promise<SkillsetVO[]> => {
        const skillsetPromises = [];
        for (let i = startIndex; i < endIndex; i++) {
          skillsetPromises.push(this.findSkillsetVO(PresetSkillsetTypes[i], true));
        }
        const skillsets = (await Promise.all(skillsetPromises)).filter(Boolean);
        return skillsets as SkillsetVO[];
      };
      return spliceKindPageSkillsets(PresetSkillsetTypes.length, fetchCurrentPageSkillsets);
    }

    const matchSkillsetVO = (skillset: SkillsetVO): SkillsetVO | undefined => {
      const keyword = query.trim().toLowerCase();
      // match skillset name or description, return all skill
      if (skillset.name.toLowerCase().includes(keyword) || skillset.description.toLowerCase().includes(keyword)) {
        return skillset;
      }
      // otherwise, match skillset skills
      const skills = skillset.skills.filter(
        (skill) => skill.name.toLowerCase().includes(keyword) || skill.description?.toLowerCase().includes(keyword),
      );
      // if no skill matched, return undefined
      if (skills.length === 0) {
        return undefined;
      }
      return {
        ...skillset,
        skills,
      };
    };
    const fetchMatchedSkillsets = async (type: PresetSkillsetType | string): Promise<SkillsetVO | undefined> => {
      const skillset = await this.findSkillsetVO(type, true);
      if (!skillset) {
        return undefined;
      }
      return matchSkillsetVO(skillset);
    };
    // 如果存在查询条件，遍历所有预设的技能集，匹配技能集名称或技能名称，得到技能集列表和匹配数量
    const skillsetPromises = [];
    for (const type of PresetSkillsetTypes) {
      skillsetPromises.push(fetchMatchedSkillsets(type));
    }
    const matchedSkillsets: SkillsetVO[] = (await Promise.all(skillsetPromises)).filter(Boolean) as SkillsetVO[];
    // 拼接技能集列表，返回分页结果
    return spliceKindPageSkillsets(matchedSkillsets.length, (start, end) => matchedSkillsets.slice(start, end));
  }

  override async getToolset(select: SkillsetSelectBO, props: SkillsetHandlerContext): Promise<ToolSet> {
    if (select.kind !== 'preset') {
      throw new Error(`Skillset kind ${select.kind} is not supported for Toolset`);
    }
    const { key, configuration } = select;
    const skillsetHandler = await ToolsetSkillsetKind.getSkillsetHandlerStrict(key);
    if (!skillsetHandler) {
      return {};
    }
    const ctx = { ...props, configuration };
    const skillset = await skillsetHandler(ctx);
    const toolset = (await skillset.toolsetHandler?.(ctx)) || {};
    return toolset;
  }

  private async findSkillsetVO(
    type: PresetSkillsetType | string,
    filterHidden?: boolean,
  ): Promise<SkillsetVO | undefined> {
    const skillsetHandler = await ToolsetSkillsetKind.getSkillsetHandlerStrict(type as PresetSkillsetType);
    if (!skillsetHandler) {
      return undefined;
    }
    const ctx = { user: this._user };
    const skillset = await skillsetHandler(ctx);
    const { display, logo, authentication, toolsetHandler } = skillset;
    if (display.hidden && filterHidden) {
      return undefined;
    }

    const toolset = (await toolsetHandler?.(ctx)) || {};
    // length === 0 也返回显示
    // if (Object.keys(toolset).length === 0) {
    //   return undefined;
    // }
    const skills: SkillVO[] = [];
    for (const [key, value] of Object.entries(toolset)) {
      skills.push({
        key,
        name: key,
        description: value.description,
      });
    }
    const locale = this._user.locale;
    return {
      kind: 'preset',
      key: type,
      name: iStringParse(display.label, locale) || type,
      description: iStringParse(display.description, locale),
      logo,
      skills,
      configuration: authentication ? integrationTemplateToVO(authentication) : undefined,
    };
  }

  /**
   *  适合内部注册过的函数使用，强类型绑定
   *
   * @param name
   * @returns
   */
  static async getSkillsetHandlerStrict(name: PresetSkillsetType): Promise<SkillsetHandler | undefined> {
    // 判断 ./${name}/server.ts 是否存在
    const currentDir = path.dirname(fileURLToPath(import.meta.url));
    const serverFilePath = path.join(currentDir, name, 'skillset.ts');
    if (!fs.existsSync(serverFilePath)) {
      throw new Error(`skillset ${name}/skillset.ts not found: ${serverFilePath}`);
      // return undefined;
    }
    const toolset = await import(`./${name}/skillset`);
    if (toolset.default) {
      return toolset.default as SkillsetHandler;
    }
    console.debug(`skillset ${name}/skillset.ts not found default export`);
    return undefined;
  }
}

export class ToolSDKSkillsetKind extends SkillsetKind {
  override async getSkillsetVO(select: SkillsetSelectBO): Promise<SkillsetVO | undefined> {
    if (select.kind !== 'toolsdk') {
      throw new Error(`Skillset kind ${select.kind} is not supported yet`);
    }
    return this.findSkillsetVO(select.key, select.version);
  }

  override async searchSkillsetVO(args: SkillsetSearchArgs): Promise<KindSearchResult | undefined> {
    // if (isInCI()) {
    //   return undefined;
    // }
    const { query, pageNo, pageSize, scope } = args;
    // todo 合并前面分类的技能集数据，现在直接拿，未截取当前kind 数据，会导致首页的技能集数量超出预期
    const page = await ToolSDKAISO.getPackagePage({
      query,
      pageNo,
      pageSize,
      scope: scope || 'ALL_VALIDATED',
    });
    const vos = await Promise.all(page.data.map(async (pkg) => this.findSkillsetVO(pkg.key, pkg.version)));
    return {
      kindTotal: page.pagination.total,
      currentPageSkillsets: vos.filter((vo) => !!vo),
    };
  }

  override async getToolset(select: SkillsetSelectBO, _props: SkillsetHandlerContext): Promise<ToolSet> {
    // if (isInCI()) {
    //   return {};
    // }
    if (select.kind !== 'toolsdk') {
      throw new Error(`Skillset kind ${select.kind} is not supported yet`);
    }
    const { key, version, configurationInstanceId, includes } = select;
    const pkg = await ToolSDKAISO.getPackage(key, version);

    const toolset: ToolSet = {};
    for (const tool of pkg.tools) {
      const toolKey = tool.key;
      if (includes && includes.length > 0 && !includes.includes(toolKey)) {
        continue;
      }
      const aiTool: Tool = {
        description: tool.description,
        parameters: inputFieldsToZodSchema(tool.inputFields),
        execute: async (params) =>
          ToolSDKAISO.runPackageTool({
            packageKey: key,
            packageVersion: version,
            envs: undefined,
            body: {
              toolKey,
              inputData: params,
              configurationInstanceId,
            },
          }),
      };
      toolset[toolKey] = aiTool;
    }
    return toolset;
  }

  private async findSkillsetVO(packageKey: string, packageVersion?: string): Promise<SkillsetVO | undefined> {
    // if (isInCI()) {
    //   return undefined;
    // }
    const pkg = await ToolSDKAISO.getPackage(packageKey, packageVersion);
    return {
      kind: 'toolsdk',
      key: pkg.key,
      version: pkg.version,
      name: pkg.name,
      description: pkg.description,
      logo: pkg.logo,
      configuration: pkg.configuration,
      skills: pkg.tools.map((tool) => ({
        key: tool.key,
        name: tool.name,
        description: tool.description,
        logo: tool.logo,
      })),
    };
  }
}

export class CustomSkillsetKind extends SkillsetKind {
  override async getSkillsetVO(select: SkillsetSelectBO): Promise<SkillsetVO | undefined> {
    if (select.kind !== 'custom') {
      throw new Error(`Skillset kind ${select.kind} is not supported yet`);
    }
    switch (select.key) {
      case 'mcp':
        return {
          kind: 'custom',
          key: select.key,
          name: 'Custom MCP Server',
          description: 'Custom MCP Server',
          logo: {
            type: 'PRESET',
            url: '/assets/ai/skillset/mcp.png',
          },
          serverConfig: select.serverConfig,
          skills: [],
        };
      case 'call-automation':
        return {
          kind: 'custom',
          key: select.key,
          name: 'Call Automation',
          description: 'Call Automation Node',
          logo: {
            type: 'PRESET',
            url: '/assets/ai/skillset/call_automation.png',
          },
          nodeId: select.nodeId,
          skills: [],
        };
      // todo
      case 'zapier':
      default:
        break;
    }
    return undefined;
  }

  override async searchSkillsetVO(_args: SkillsetSearchArgs): Promise<KindSearchResult | undefined> {
    // todo
    return undefined;
  }

  override async getToolset(select: SkillsetSelectBO): Promise<ToolSet> {
    const { kind, key } = select;
    if (kind !== 'custom') {
      throw new Error(`Skillset kind ${kind} is not supported yet`);
    }
    switch (key) {
      case 'mcp': {
        const mcpServer = select.serverConfig?.mcpServers;
        if (!mcpServer) {
          break;
        }
        const fetchCustomMcpServerTools = async (url: string) => {
          let mcpClient;
          // todo add protocol tag
          if (url.includes('/sse')) {
            mcpClient = await getAiSDKSseMcpClient(url);
          } else {
            mcpClient = await getAiSDKHttpMcpClient(url);
          }
          return mcpClient.tools();
        };
        const toolsets = await Promise.all(
          Object.entries(mcpServer).map(async ([, value]) => fetchCustomMcpServerTools(value.url)),
        );
        const toolset: ToolSet = toolsets.reduce((acc, curr) => ({ ...acc, ...curr }), {});
        return toolset;
      }
      case 'call-automation':
      case 'zapier':
      default:
        break;
    }
    return {};
  }

  private async fetchCustomMcpServer(serverKey: string, serverUrl: string): Promise<SkillsetVO> {
    const mcpClient = await getAiSDKSseMcpClient(serverUrl);
    const skills: SkillVO[] = [];
    const toolset = await mcpClient.tools();
    for (const [key, value] of Object.entries(toolset)) {
      skills.push({
        key,
        name: key,
        description: value.description,
      });
    }
    return {
      kind: 'custom',
      key: serverKey,
      name: serverKey,
      description: `Custom MCP Server ${serverKey}`,
      skills,
    };
  }
}

export class SkillsetKindFactory {
  static createSkillsetKind(kindType: SkillsetSelectBO['kind'], ctx: { user: UserSO }): SkillsetKind {
    switch (kindType) {
      // case 'automation':
      //   return new AutomationSkillsetKind();
      case 'preset':
        return new ToolsetSkillsetKind(ctx.user);
      case 'toolsdk':
        return new ToolSDKSkillsetKind();
      case 'custom':
        return new CustomSkillsetKind();
      default:
        throw new Error(`Skillset kind ${kindType} is not supported yet`);
    }
  }
}
