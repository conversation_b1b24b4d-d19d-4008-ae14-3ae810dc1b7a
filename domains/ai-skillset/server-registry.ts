import { PackageListRequestScope } from '@toolsdk.ai/sdk-ts/types/dto';
import type { ToolSet } from 'ai';
import { SKILLSET_IGNORE_LIST } from '@bika/contents/config/server/ai/ai-skillset-config';
import { Locale } from '@bika/contents/i18n';
import { Action } from '@bika/types/automation/bo';
import { Pagination, PaginationSchema } from '@bika/types/shared/pagination';
import { SkillsetSelectBO, SkillsetKinds } from '@bika/types/skill/bo';
import { SkillSelectDTO } from '@bika/types/skill/dto';
import { SkillsetVO, SkillsetVOPagination } from '@bika/types/skill/vo';
import { isInUnitTest } from 'sharelib/app-env';
import { FeaturedSkillsetSelects } from './server-config';
import { SkillsetKindFactory } from './skillset-kind-factory';
import { SkillsetHandlerContext } from './types';
import type { UserSO } from '../user/server/user-so';

/**
 * Bika.ai 中的Skillset，是若干个 tool 的集合，影响一个 ai agent 的具体能力，既可能是预置，也可能交给用户去配置。
 *
 * Skillset，有 几种定义方式：
 * 1. 本地代码(bika-code），就是写在这个目录的；
 * 2. 预置MCP(mcp-local)，就是在 server-config.ts 里定义的,已经配好了环境变量的了；
 * 3. (待定)Automation Actions 转换成 skillsets
 * 4. (待定)远程MCP(toolsdk-remote)，暂时未实现，由于 toolsdk还没上线...
 * 5. (待定)MCP Client，MCP 官方 SDK直接使用 MCP Server 的工具集
 *
 */
export class AISkillsetServerRegistry {
  private static readonly IGNORED_PACKAGES = new Set(SKILLSET_IGNORE_LIST.packageNames);

  static async search(
    user: UserSO,
    args?: {
      pagination?: Partial<Pagination>;
      category?: 'featured';
      selectedSkillset?: SkillsetSelectBO; // 选中的skillset
      query?: string;
      locale?: Locale;
      scope?: PackageListRequestScope;
    },
  ): Promise<SkillsetVOPagination> {
    const { pagination, category, selectedSkillset, query, locale, scope } = args || {};
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});

    const isSkillsetIgnored = (skillset: SkillsetVO): boolean => this.IGNORED_PACKAGES.has(skillset.key);

    // 如果有选中的skillset，则优先返回对应的 SkillsetVO
    const fetchSelectedSkillset = async (): Promise<SkillsetVO | undefined> => {
      if (!selectedSkillset) {
        return undefined;
      }
      return this.getSkillsetVO(user, selectedSkillset, locale);
    };

    const fetchFeaturedSkillsets = async (start: number): Promise<SkillsetVO[]> => {
      const total = FeaturedSkillsetSelects.length;
      if (total === 0 || total <= start) {
        return [];
      }
      const skillsets: SkillsetVO[] = [];
      let processedCount = 0;

      // 从下标 start 开始，遍历最多 pageSize 次，获取 FeaturedSkillsetSelects 的数据
      for (let i = start; processedCount < pageSize && i < total; i++) {
        const select = FeaturedSkillsetSelects[i];
        // 如果是选中的skillset，则跳过
        if (selectedSkillset && selectedSkillset.key === select.key) {
          continue;
        }
        const kind = SkillsetKindFactory.createSkillsetKind(select.kind, { user });
        const skillset = await kind.getSkillsetVO(select, locale);
        if (!skillset || isSkillsetIgnored(skillset)) {
          continue;
        }

        skillsets.push(skillset);
        processedCount++;
      }
      return skillsets;
    };

    // 如果是 featured category，直接返回 FeaturedSkillsetSelects 的数据
    if (!query && category) {
      const start = (pageNo - 1) * pageSize;
      const [selectedSkillsetVO, featuredSkillsets] = await Promise.all([
        fetchSelectedSkillset(),
        fetchFeaturedSkillsets(start),
      ]);
      return {
        pagination: {
          pageNo,
          pageSize,
          total: selectedSkillsetVO ? 1 + featuredSkillsets.length : featuredSkillsets.length,
        },
        data: [...(selectedSkillsetVO ? [selectedSkillsetVO] : []), ...featuredSkillsets],
      };
    }

    // 有序查询不同 kind，获取各自的总数和当前分页 skillsets 数据，合并分页结果
    let total: number = 0;
    const skillsets: SkillsetVO[] = [];
    for (const kind of SkillsetKinds) {
      const skillsetKind = SkillsetKindFactory.createSkillsetKind(kind, { user });
      const kindSearchResult = await skillsetKind.searchSkillsetVO({
        query,
        locale,
        pageNo,
        pageSize,
        prevTotal: total,
        scope,
      });
      if (!kindSearchResult) {
        continue;
      }
      const { currentPageSkillsets, kindTotal } = kindSearchResult;

      // 过滤掉忽略清单中的 skillset
      const filteredSkillsets = currentPageSkillsets.filter((skillset) => !isSkillsetIgnored(skillset));

      // 累计总数
      total += kindTotal;
      // 当前页的数据可能来自于多个 kind，所以需要合并
      skillsets.push(...filteredSkillsets);
    }
    return {
      pagination: {
        pageNo,
        pageSize,
        total,
      },
      data: skillsets,
    };
  }

  static async getSkillsetVO(user: UserSO, select: SkillsetSelectBO, locale?: Locale): Promise<SkillsetVO | undefined> {
    const kind = SkillsetKindFactory.createSkillsetKind(select.kind, { user });
    return kind.getSkillsetVO(select, locale);
  }

  // Merge AI SDK Toolset
  static mergeToolSet(records: ToolSet[]): ToolSet {
    return records.reduce((acc, curr) => ({ ...acc, ...curr }), {});
  }

  static async getAutomationAction(select: SkillSelectDTO, props?: SkillsetHandlerContext): Promise<Action> {
    return {
      actionType: 'FIND_MEMBERS',
      input: {
        type: 'MEMBER',
        by: [],
      },
    };
  }

  static async parseAISDKToolset(
    select: SkillsetSelectBO,
    props: SkillsetHandlerContext,

    // 是否不需要过滤，默认是 true，用于 executeTool 的时候确保拿回 execute
    dontFilter?: boolean,
  ): Promise<ToolSet> {
    const kind = SkillsetKindFactory.createSkillsetKind(select.kind, { user: props.user });
    let toolset = await kind.getToolset(select, props);
    if (dontFilter !== true && 'includes' in select && select.includes) {
      const filteredEntries = Object.entries(toolset)
        .filter(([name]) => select.includes?.includes(name))
        .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
      toolset = filteredEntries;
    }

    if (dontFilter !== true && 'needApprovals' in select && select.needApprovals) {
      // 需要审批，那么，遍历 toolset，找到同名的
      // 将它的 execute 干掉，服务端不自动执行，客户端会卡在 tool-call
      // for (const key in toolset) {
      for (const [toolKey, toolEntry] of Object.entries(toolset)) {
        if (select.needApprovals.includes(toolKey) && toolEntry.execute) {
          // 清理掉 execute 函数, 服务端挂起不执行
          if (isInUnitTest()) {
            console.log(
              `Tool ${toolKey} in skillset ${select.key} has execute function, but it is needApproval, so we remove it.`,
            );
          }
          delete toolEntry.execute;
        }
      }
    }
    return toolset;
  }

  /**
   * Parse SkillSelectBO into AI SDK ToolSet
   *
   * 对于 needApprovals 的 tools，会被清理掉 execute 函数，交到客户端主动处理
   *
   * @param selects
   * @param props
   * @returns
   */
  static async parseAISDKToolsets(
    selects: SkillsetSelectBO[],
    props: SkillsetHandlerContext,
    dontFilter?: boolean,
  ): Promise<ToolSet> {
    const skillsets: ToolSet[] = [];
    for (const name of selects) {
      const toolset = await this.parseAISDKToolset(name, props, dontFilter);
      skillsets.push(toolset);
    }
    return this.mergeToolSet(skillsets);
  }
}
