'use client';

import type React from 'react';
import { useMemo, useImperativeHandle, useRef, forwardRef } from 'react';
import { useTRPC } from '@bika/api-caller';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
// import { FormAppAIActionBoInput } from '@bika/domains/automation-nodes/formapp-action/bo-input';
import { AIModelActionBOInput } from '@bika/domains/automation-nodes/ai-model-action/bo-input';
import { ToolSDKAIActionBoInput } from '@bika/domains/automation-nodes/toolsdk-action/bo-input';
import { CreateMissionActionBOInput } from '@bika/domains/mission/client/create-mission-action-bo-input';
import {
  type Action,
  ActionSchema,
  type ActionType,
  type SlackWebhookAction,
  type DingtalkWebhookAction,
  type WecomWebhookAction,
  type FeishuWebhookAction,
  type RunScriptAction,
  type CreateRecordAction,
  type SendEmailAction,
  type TelegramSendMessageAction,
  type SendReportAction,
  type FindRecordsAction,
  type FilterAction,
  type ToolSDKAIAction,
} from '@bika/types/automation/bo';
import type { AutomationActionVO } from '@bika/types/automation/vo';
import type { IResourceEditorUIHandler } from '@bika/types/editor/context';
import { SurveyInput } from '@bika/ui/admin/types-form/survey-input';
import { CallAgentActionBOInput } from '@bika/ui/automation/types-form/actions/call-agent-action-bo-input';
import { CreateTweetActionBOInput } from '@bika/ui/automation/types-form/actions/create-tweet-action-bo-input';
import { DeepSeekGenerateTextActionBOInput } from '@bika/ui/automation/types-form/actions/deepseek-generate-text-action-bo-input';
import { DelayActionBOInput } from '@bika/ui/automation/types-form/actions/delay-action-bo-input';
import { DingtalkWebhookActionBOInput } from '@bika/ui/automation/types-form/actions/dingtalk-webhook-action-bo-input';
import { FeishuWebhookActionBOInput } from '@bika/ui/automation/types-form/actions/feishu-webhook-action-bo-input';
import { FindDashboardActionBOInput } from '@bika/ui/automation/types-form/actions/find-dashboard-action-bo-input';
import { FindMembersActionBOInput } from '@bika/ui/automation/types-form/actions/find-members-action-bo-input';
import { FindWidgetActionBOInput } from '@bika/ui/automation/types-form/actions/find-widget-action-bo-input';
import { LoopActionBOInput } from '@bika/ui/automation/types-form/actions/loop-action-bo-input';
import { OpenAIGenerateTextActionBOInput } from '@bika/ui/automation/types-form/actions/openai-generate-text-action-bo-input';
import { RoundRobinActionViewBOInput } from '@bika/ui/automation/types-form/actions/round-robin-action-bo-input';
import { RunScriptActionBOInput } from '@bika/ui/automation/types-form/actions/run-script-action-bo-input';
import { SendEmailActionBOInput } from '@bika/ui/automation/types-form/actions/send-email-action-bo-input';
import { SendReportActionBOInput } from '@bika/ui/automation/types-form/actions/send-report-action-bo-input';
import { SlackWebhookActionBOInput } from '@bika/ui/automation/types-form/actions/slack-webhook-action-bo-input';
import { TelegramSendMessageActionBOInput } from '@bika/ui/automation/types-form/actions/telegram-send-message-action-bo-input';
import { TwitterUploadMediaActionBOInput } from '@bika/ui/automation/types-form/actions/twitter-upload-media-action-bo-input';
import { WebhookActionBOInput } from '@bika/ui/automation/types-form/actions/webhook-action-bo-input';
import { WecomWebhookActionBOInput } from '@bika/ui/automation/types-form/actions/wecom-webhook-action-bo-input';
import { getActionTypesOptions } from '@bika/ui/automation/types-form/constant';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { Stack, Box } from '@bika/ui/layouts';
import { IStringInput } from '@bika/ui/shared/types-form/i-string-input';
import { RunTest } from '@bika/ui/shared/types-form/run-test';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import type { ResourceFormBase } from '@bika/ui/shared/types-form/types';
import { CreateRecordActionBOInput } from './create-record-action-bo-input';
import { FilterActionBOInput } from './filter-action-bo-input';
import { FindRecordsActionBOInput } from './find-records-action-bo-input';
import { UpdateRecordActionBOInput } from './update-record-action-bo-input';
import { useResourceEditorStore } from '../store';

export interface AutomationActionBOInputRef {
  validate?: () => Promise<boolean>;
}

interface Props extends ResourceFormBase {
  value: Action;
  onChange: (newAction: Action | ((_preAction: Action) => Action)) => void;
  otherActions?: AutomationActionVO[];
  uiHandler: IResourceEditorUIHandler;
  automationId?: string;
  onSave?: () => void;
  parentActionId?: string;
  actionsLength?: number;
}

const AutomationActionBOInputForm: React.ForwardRefRenderFunction<AutomationActionBOInputRef, Props> = (props, ref) => {
  const { locale, api, uiHandler, automationId, value, parentActionId, actionsLength } = props;
  const { errors, updateError, clearErrors } = useResourceEditorStore();
  const { t } = locale;
  const formRef = useRef<AutomationActionBOInputRef>(null);

  const trpc = useTRPC();

  const setValue = props.onChange;

  const actionsConfig = useMemo(() => getActionTypesConfig(props.locale), [props.locale]);

  const actionTypesOptions = useMemo(() => {
    const options = getActionTypesOptions(props.locale);
    // loop action 不允许嵌套 loop action 和 delay action
    if (parentActionId) {
      return options.filter((option) => !['LOOP', 'DELAY'].includes(option.value));
    }
    return options;
  }, [props.locale, parentActionId]);

  const { searchParams } = useUIFrameworkContext();

  const isDebugQuery = useMemo(() => searchParams.get('debug') || 0, []);
  const isDebug = Boolean(isDebugQuery);

  let isComingSoon: boolean = (value.actionType && actionsConfig[value.actionType].display === 'COMING_SOON') || false;
  if (isDebug) {
    isComingSoon = false;
  }

  const restInput = {
    locale,
    api,
    setErrors: updateError,
    errors,
    // parentActionId 用户在 LOOP 中新建子 action 时，需要传递 parentActionId 标记位置，确保变量选择器的变量范围正确
    parentActionId,
  };

  useImperativeHandle(ref, () => ({
    validate: formRef.current?.validate,
  }));

  return (
    <Stack spacing={1}>
      <Box>
        <SelectInput
          label={t.automation.action.type}
          helpLink={{
            text: t.automation.tooltip_learn_more,
            url: `/help/reference/automation-action/${value.actionType?.toLowerCase().replace(/_/g, '-')}`,
          }}
          options={actionTypesOptions}
          value={value.actionType}
          iconSize={24}
          onChange={(newVal) => {
            if (!newVal) {
              return;
            }
            clearErrors();
            // 类型发生变化 重置input
            const cloneAction = ActionSchema.parse({
              ...value,
              actionType: newVal as ActionType,
              input: actionsConfig[newVal as ActionType].defaultValue.input,
            });
            setValue(cloneAction);
          }}
        />

        <IStringInput
          locale={props.locale}
          label={t.automation.action.description}
          helpText={t.automation.description_help_text}
          value={value.description || ''}
          onChange={(newVal) => setValue({ ...value, description: newVal })}
        />
      </Box>

      {value.actionType === 'CALL_AGENT' && (
        <CallAgentActionBOInput value={value} onChange={setValue} locale={locale} />
      )}

      {value.actionType === 'CREATE_MISSION' && (
        <CreateMissionActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'FIND_MEMBERS' && (
        <FindMembersActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'SEND_REPORT' && (
        <SendReportActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<SendReportAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'WEBHOOK' && (
        <WebhookActionBOInput
          value={value}
          onChange={(newVal) => {
            setValue(newVal);
          }}
          {...restInput}
        />
      )}
      {value.actionType === 'WECOM_WEBHOOK' && (
        <WecomWebhookActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<WecomWebhookAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'SLACK_WEBHOOK' && (
        <SlackWebhookActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<SlackWebhookAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'FEISHU_WEBHOOK' && (
        <FeishuWebhookActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<FeishuWebhookAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'DINGTALK_WEBHOOK' && (
        <DingtalkWebhookActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<DingtalkWebhookAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'X_CREATE_TWEET' && (
        <CreateTweetActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'TWITTER_UPLOAD_MEDIA' && (
        <TwitterUploadMediaActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'TELEGRAM_SEND_MESSAGE' && (
        <TelegramSendMessageActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<TelegramSendMessageAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'RUN_SCRIPT' && (
        <RunScriptActionBOInput
          value={value}
          onChange={props.onChange as React.Dispatch<React.SetStateAction<RunScriptAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'FIND_RECORDS' && (
        <FindRecordsActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<FindRecordsAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'FILTER' && (
        <FilterActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<FilterAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'ROUND_ROBIN' && (
        <RoundRobinActionViewBOInput
          value={value}
          onChange={(newVal) => setValue(newVal)}
          getActions={() => props.otherActions || []}
          {...restInput}
        />
      )}
      {/* {value.actionType === 'FORMAPP_AI' && (
        <FormAppAIActionBoInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<FormAppAIAction>>}
          {...restInput}
          automationId={automationId}
        />
      )} */}
      {value.actionType === 'TOOLSDK_AI' && (
        <ToolSDKAIActionBoInput
          value={value}
          ref={formRef}
          onChange={setValue as React.Dispatch<React.SetStateAction<ToolSDKAIAction>>}
          {...restInput}
          automationId={automationId}
        />
      )}
      {value.actionType === 'SEND_EMAIL' && (
        <SendEmailActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<SendEmailAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'CREATE_RECORD' && (
        <CreateRecordActionBOInput
          value={value}
          onChange={setValue as React.Dispatch<React.SetStateAction<CreateRecordAction>>}
          {...restInput}
        />
      )}
      {value.actionType === 'UPDATE_RECORD' && (
        <UpdateRecordActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'FIND_DASHBOARD' && (
        <FindDashboardActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'FIND_WIDGET' && (
        <FindWidgetActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'OPENAI_GENERATE_TEXT' && (
        <OpenAIGenerateTextActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'DEEPSEEK' && (
        <DeepSeekGenerateTextActionBOInput value={value} onChange={setValue} {...restInput} />
      )}
      {value.actionType === 'AI_MODEL' && <AIModelActionBOInput value={value} onChange={setValue} {...restInput} />}
      {value.actionType === 'LOOP' && (
        <LoopActionBOInput
          value={value}
          onChange={setValue}
          uiHandler={uiHandler}
          automationId={automationId}
          actionsLength={actionsLength}
          updateAction={trpc.automation.updateAction.mutate}
          {...restInput}
        />
      )}
      {value.actionType === 'DELAY' && <DelayActionBOInput value={value} onChange={setValue} locale={locale} />}

      {isComingSoon && <SurveyInput surveyType={'COMING_SOON_AUTOMATION_ACTION'} />}
      {!isComingSoon && (
        <RunTest
          locale={locale}
          disabled={!props.value.id}
          disabledPreview={props.value.id ? !api.automation.getAutomationTestResult(props.value.id) : true}
          runTest={async () => {
            if (!props.value.id) {
              return;
            }
            await props.onSave?.();
            await api.automation.testAction(props.value.id, props.value.actionType);
            uiHandler.onClickPreview({
              actionId: props.value.id,
            });
          }}
          previewTest={() => {
            uiHandler.onClickPreview({
              actionId: props.value.id,
            });
          }}
        />
      )}
    </Stack>
  );
};

export const AutomationActionBOInput = forwardRef(AutomationActionBOInputForm);
