import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { SseSO } from '@bika/domains/event/server/sse/sse-so';
import { IRelationIdOpts } from '@bika/domains/node/server/types';
import { pathHelper } from '@bika/domains/shared/server';
import { isArrayOfType, isNullOrUndefined } from '@bika/domains/shared/shared';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UnitFactory } from '@bika/domains/unit/server';
import { UserSO } from '@bika/domains/user/server/user-so';
import { DatabaseRecordModel, db, mongoose } from '@bika/server-orm';
import {
  type DatabaseField,
  type DatabaseRecord,
  type RecordData,
  DatabaseCreatedTimeField,
  DatabaseModifiedTimeField,
  DatabaseCreatedByField,
  DatabaseModifiedByField,
  DatabaseFieldWithId,
  RecordValue,
  DatabaseFormulaField,
} from '@bika/types/database/bo';
import { CellValue, CellValues } from '@bika/types/database/bo';
import {
  RecordDetailVO,
  RecordVO,
  RecordRenderOpts,
  CellVO,
  RecordRenderVO,
  CellRenderVO,
  CONST_PREFIX_FIELD,
  CONST_PREFIX_RECORD,
  CellRenderOpts,
  CellValueVO,
  OpenAPIRecordRenderOpts,
} from '@bika/types/database/vo';
import { ToTemplateOptions } from '@bika/types/node/bo';
import { ApiRecordVO, OpenAPIFieldCellValue } from '@bika/types/openapi/vo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { DateTimeSO } from '@bika/types/system';
import { CellSOFactory } from './cells/cell-factory';
import { CellKey, CellRenderModel } from './cells/types';
import { DatabaseSO } from './database-so';
import { ModifiedTimeFieldSO } from './fields/date-time-field';
import { FieldSO } from './fields/field-so';
import { FormulaUtil } from './fields/formula';
import { CreateRecordContext } from './fields/types';
import { ModifiedByFieldSO } from './fields/user-field';
import { CommentSO } from './record/comment-so';
import { FieldCellModelMap, RecordCellModelMap } from './types';
import { ViewSO } from './views/view-so';
import { ChangeLogFactory } from '../../change/server/change-log-factory';
import { RecordChangeSO } from '../../change/server/record-change-so';

export class RecordSO {
  private _model: DatabaseRecordModel;

  /**
   * 为什么初始化对象需要传入Space对象?
   * 原因:
   * 当你需要批量记录的单元格值时, 它总会需要Space对象去检查各种限制,
   * 当你去批量初始化一个Record对象时, 你不期望在每次去获取Space对象, 这样效率非常低, 所以在初始化时就传入Space对象
   * 单个Record的更新则不需要这么频繁获取Space对象
   */
  private readonly _space: SpaceSO;

  private _database: DatabaseSO;

  // 当行记录发生改变时, 记录改变的单元格
  // 注意: 每执行一个单元格的更新, 就会覆盖上一次的值, 以便于找到变化的值
  private _changes: { [fieldId: string]: { field: FieldSO; from: CellValue; to: CellValue } } = {};

  protected constructor(space: SpaceSO, database: DatabaseSO, model: DatabaseRecordModel) {
    this._space = space;
    this._database = database;
    this._model = model;
  }

  /**
   * 初始化
   */
  static async init(recordId: string, spaceId: string): Promise<RecordSO> {
    const recordPO = await db.mongo.databaseRecord(spaceId).findOne({
      id: recordId,
    });
    if (!recordPO) {
      throw new ServerError(errors.database.record_not_found);
    }
    if (!recordPO.databaseId) {
      throw new Error(`record ${recordId} has no belong any database`);
    }
    const database = await DatabaseSO.init(recordPO.databaseId);
    const space = await database.getSpace();
    return new RecordSO(space, database, recordPO);
  }

  /**
   * 初始化
   */
  static initWithModel(space: SpaceSO, database: DatabaseSO, recordModel: DatabaseRecordModel): RecordSO {
    return new RecordSO(space, database, recordModel);
  }

  get model() {
    return this._model;
  }

  get spaceId() {
    return this.model.spaceId;
  }

  get space(): SpaceSO {
    return this._space;
  }

  get database(): DatabaseSO {
    return this._database;
  }

  get fields() {
    return this._database.getFields();
  }

  get id(): string {
    return this.model.id;
  }

  get databaseId() {
    return this.model.databaseId!;
  }

  get revision() {
    return this.model.revision;
  }

  get data() {
    return this.model.data;
  }

  get computed() {
    return this.model.computed;
  }

  get values() {
    return this.model.values;
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get createdAt() {
    return this.model.createdAt;
  }

  get updatedAt() {
    return this.model.updatedAt;
  }

  get objectId(): mongoose.Types.ObjectId {
    return (this.model as unknown as { _id: mongoose.Types.ObjectId })._id;
  }

  addChange(field: FieldSO, from: CellValue, to: CellValue) {
    this._changes[field.id] = { field, from, to };
  }

  changes() {
    return this._changes;
  }

  clearChanges() {
    this._changes = {};
  }

  async getComments(pagination?: Pagination): Promise<{ pagination: PaginationInfo; list: CommentSO[] }> {
    return CommentSO.getRecordComments({ recordId: this.id }, pagination);
  }

  async getComment(commentId: string): Promise<CommentSO> {
    return CommentSO.init(commentId);
  }

  async getChangeLogs(pagination?: Pagination): Promise<{ pagination: PaginationInfo; list: RecordChangeSO[] }> {
    const { pageNo, pageSize } = pagination ?? PaginationSchema.parse({});
    const { list, total } = await ChangeLogFactory.searchLog('DATABASE_RECORD', {
      id: this.id,
      pagination: { pageNo, pageSize },
    });
    return { pagination: { pageNo, pageSize, total }, list };
  }

  async addComment(user: UserSO, content: string): Promise<CommentSO> {
    const member = await user.getMember(this.spaceId);
    const commentSO = CommentSO.create(user.id, 'RECORD', this.id, content, this.spaceId, member?.id);
    await SseSO.emit(user.id, {
      name: 'record-comment',
      spaceId: this.spaceId,
      nodeId: this.databaseId,
      recordId: this.id,
    });
    return commentSO;
  }

  /**
   * 获取指定字段对应单元格Cell对象
   */
  getCellSO(fieldKey: string) {
    const fieldSO = this._database.getFieldByFieldKey(fieldKey);
    return CellSOFactory.getCell(this.model, fieldSO, this.fields);
  }

  /**
   * 获取某个字段的值，如果是计算字段，会获取"computed"的值，否则获取"data"的值
   *
   * @param fieldKey
   */
  getCellData(fieldKey: string): CellValue | undefined {
    const cellSO = this.getCellSO(fieldKey);
    return cellSO.getData();
  }

  /**
   * 获取指定字段对应单元格的值
   */
  getCellValue(fieldKey: string): CellValues | null | undefined {
    const cellSO = this.getCellSO(fieldKey);
    return cellSO.getValue();
  }

  /**
   * 获取主字段的单元格渲染值
   * 主字段没有IO额外读取, 直接读取记录数据
   * @param opts 渲染选项
   * @returns 主字段的单元格值VO
   */
  getPrimaryCellValue(opts?: CellRenderOpts): CellValueVO {
    const primaryField = this.database.getPrimaryField();
    const cell = this.getCellSO(primaryField.id);
    return cell.getValue(opts) ?? null;
  }

  /**
   * 一行记录的渲染VO, 包含字段, 可选视图
   */
  async toDetailVO(opts?: RecordRenderOpts): Promise<RecordDetailVO> {
    // 获取 view 的 fields
    const view: ViewSO = opts?.viewId ? await this._database.getView(opts.viewId) : await this._database.firstView();
    const record = await this.toRenderVO(opts);
    return {
      fields: view.getFields().map((fieldSO) => fieldSO.toVO({ locale: opts?.locale, viewFields: view.fields })),
      revision: this.revision,
      record,
    };
  }

  private getCellVOMap(opts?: RecordRenderOpts): Record<string, CellVO> {
    const { withTemplateId = false, locale, timeZone, fieldIds, returnFieldName = false } = opts ?? {};

    const cellVOMap: Record<string, CellVO> = {};

    for (const field of this.fields) {
      if (fieldIds && !fieldIds.includes(field.id)) {
        continue;
      }
      const cell = CellSOFactory.getCell(this.model, field, this.fields);
      const cellVO = cell.toVO({ locale, timeZone });
      cellVOMap[field.toCellKey(returnFieldName, locale)] = cellVO;

      if (field.templateId && withTemplateId) {
        // 补充 templateId 作为标识返回
        cellVOMap[field.templateId] = cellVO;
      }
    }

    return cellVOMap;
  }

  /**
   * 内部使用的VO,不可使用到客户端上
   * @param opts present option
   * @returns RecordVO
   */
  toVO(opts?: RecordRenderOpts): RecordVO {
    const cells = this.getCellVOMap(opts);
    return {
      id: this.id,
      databaseId: this.databaseId,
      revision: this.revision,
      cells,
      url: opts?.withUrl ? pathHelper.getRecordUrl(this.spaceId, this.databaseId, this.id) : undefined,
    };
  }

  toBO(): DatabaseRecord {
    return {
      id: this.id,
      templateId: this.templateId,
      data: this.data || {},
      values: (this.model.values as RecordData) || undefined,
    };
  }

  /**
   * 获取指定字段的单元格渲染模型
   * 专门提供给批量计算更新时使用
   */
  async getCellRenderModel(fieldKey: string, opts?: CellRenderOpts): Promise<CellRenderModel> {
    const cell = this.getCellSO(fieldKey);
    // 渲染值
    const cellRenderValue = await cell.getCellValueVO(opts);
    return {
      data: cell.getModelData(),
      computed: cell.getModelComputed(),
      values: cellRenderValue,
    };
  }

  /**
   * 输出记录的渲染VO, 供前端使用, 或者转换时使用
   * @param opts 渲染选项
   * @returns 记录渲染VO
   */
  async toRenderVO(opts?: RecordRenderOpts): Promise<RecordRenderVO> {
    const {
      // withTemplateId = false, // 这个方法用不上
      locale, // 用户语言环境
      timeZone, // 时区
      fieldIds = [], // 指定字段输出
      returnFieldName = false, // 是否作为字段名称返回
    } = opts ?? {};
    // 如果指定了字段ID, 则只返回这些字段的值, 没有这些字段的值则返回全部
    const includedFields =
      fieldIds.length > 0 ? this.fields.filter((field) => fieldIds.includes(field.id)) : this.fields;
    const cellVOEntries: [string, CellRenderVO][] = await Promise.all(
      includedFields.map(async (field) => {
        const cell = CellSOFactory.getCell(this.model, field, this.fields);
        const cellRenderVO = await cell.toRenderVO({ locale, timeZone });
        return [field.toCellKey(returnFieldName, locale), cellRenderVO];
      }),
    );
    const cells = Object.fromEntries(cellVOEntries);
    return {
      id: this.id,
      databaseId: this.databaseId,
      revision: this.revision,
      cells,
    };
  }

  async toApiRecordVO(opts?: OpenAPIRecordRenderOpts): Promise<ApiRecordVO> {
    const {
      // withTemplateId = false, // 这个方法用不上
      locale, // 用户语言环境
      timeZone, // 时区
      fieldIds = [], // 指定字段输出
      returnFieldName = false, // 是否作为字段名称返回
      cellFormat = 'json', // 单元格格式
    } = opts ?? {};
    // 如果指定了字段ID, 则只返回这些字段的值, 没有这些字段的值则返回全部
    const includedFields =
      fieldIds.length > 0 ? this.fields.filter((field) => fieldIds.includes(field.id)) : this.fields;
    const cellVOEntries: [string, OpenAPIFieldCellValue][] = await Promise.all(
      includedFields.map(async (field) => {
        const cell = CellSOFactory.getCell(this.model, field, this.fields);
        const cellRenderVO = await cell.toOpenAPICellValue({ locale, timeZone, cellFormat });
        return [field.toCellKey(returnFieldName, locale), cellRenderVO];
      }),
    );
    const cells = Object.fromEntries(cellVOEntries);
    return {
      id: this.id,
      fields: cells,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt?.toISOString(),
    };
  }

  async toTemplate(opts?: ToTemplateOptions): Promise<Omit<DatabaseRecord, 'id'>> {
    // todo need to set record templateId?
    const record = _.omit(this.toBO(), 'id');

    const fieldIdMap = _.keyBy(
      this.fields.map((field) => field.toBO()),
      'id',
    );
    if (!record.templateId) {
      record.templateId = this.id;
    }
    if (opts?.getTemplateId) {
      for (const fieldId of Object.keys(record.data)) {
        // key must be field id
        const fieldTemplateId = opts.getTemplateId(fieldId);
        if (fieldTemplateId) {
          const field = fieldIdMap[fieldId];
          if (field.type === 'LINK' || field.type === 'ONE_WAY_LINK') {
            if (_.isArray(record.data[fieldId])) {
              const recordTemplateIds: string[] = [];
              for (const recordId of Object.values(record.data[fieldId])) {
                const recordTemplateId = opts.getTemplateId(recordId as string);
                if (!recordTemplateId) {
                  throw new Error(`cannot find the linked record templateId: ${recordId}`);
                }
                recordTemplateIds.push(recordTemplateId);
              }
              record.data[fieldTemplateId] = recordTemplateIds;
            }
          } else {
            record.data[fieldTemplateId] = record.data[fieldId];
          }
        }
        // 因为是用fieldId作为的templateId, 防止删除计算后的数据
        if (fieldTemplateId !== fieldId) {
          delete record.data[fieldId];
        }
      }
      // value 和data 存入的列可能不一样，所以分开替换
      if (record.values) {
        for (const fieldId of Object.keys(record.values)) {
          // key must be field id
          const fieldTemplateId = opts.getTemplateId(fieldId);
          if (fieldTemplateId) {
            record.values[fieldTemplateId] = record.values[fieldId];
            const field = fieldIdMap[fieldId];
            // 公式字段没有data, 模版需要将compoted值放到data 里面
            if (field.type === 'FORMULA') {
              record.data[fieldTemplateId] = this.computed[fieldId];
            }
          }
          if (fieldTemplateId !== fieldId) {
            delete record.values[fieldId];
          }
        }
      }
    }

    return record;
  }

  static getFakeRecordVO(database: DatabaseSO, opts?: RecordRenderOpts): RecordVO {
    const recordId = 'record_id';
    const { id: databaseId, spaceId } = database;
    // fake cells
    const cells = this.fetchFakeCellData(database.getFields(), opts);
    // fake record
    return {
      id: recordId,
      databaseId,
      revision: 0,
      cells,
      url: pathHelper.getRecordUrl(spaceId, databaseId, recordId),
    };
  }

  static fetchFakeCellData(fields: FieldSO[], opts?: RecordRenderOpts): Record<string, CellVO> {
    return fields.reduce<Record<string, CellVO>>((acc, field) => {
      const { data, value } = field.getFakeCellData();
      return {
        ...acc,
        [field.id || field.templateId!]: {
          id: field.id || field.templateId!,
          name: field.getName(opts?.locale),
          fieldType: field.type, // 变量编辑器，展示不同的字段类型icon需要
          data,
          value,
        },
      };
    }, {});
  }

  /**
   * 本地覆盖单元格数据生成新的recordModel
   */
  mergeCellModelToNewModel(fieldCellModelMap: FieldCellModelMap): DatabaseRecordModel {
    const newRecordModel: DatabaseRecordModel = _.cloneDeep(this._model);
    // Ensure data/computed/values are initialized
    if (!newRecordModel.data) newRecordModel.data = {};
    if (!newRecordModel.computed) newRecordModel.computed = {};
    if (!newRecordModel.values) newRecordModel.values = {};
    // 覆盖单元格值到record model的data/computed/values
    for (const [fieldId, cellModel] of Object.entries(fieldCellModelMap)) {
      // 确认是否存在于当前表的字段
      const field = this._database.findFieldByFieldKey(fieldId);
      if (!field) {
        // 以防外表合并到本表记录里导致逻辑错误
        throw new Error(`can not merge cell to this record, field: ${fieldId} not in this database`);
      }
      // 覆盖字段的单元格值
      const { data, computed, values } = cellModel.cell;
      if (data !== undefined) {
        newRecordModel.data[fieldId] = data;
      } else if (newRecordModel.data) {
        const fieldIds = Object.keys(newRecordModel.data);
        const fieldKey = fieldIds.find((id) => id === fieldId);
        if (fieldKey) {
          // 如果data是undefined, 如果有这个字段值,则删除这个字段的值
          delete newRecordModel.data[fieldId];
        }
      }
      if (computed !== undefined) {
        newRecordModel.computed[fieldId] = computed;
      } else if (newRecordModel.computed) {
        // 如果computed是undefined, 则删除这个字段的值
        const fieldIds = Object.keys(newRecordModel.computed);
        const fieldKey = fieldIds.find((id) => id === fieldId);
        if (fieldKey) {
          delete newRecordModel.computed[fieldId];
        }
      }

      if (values !== undefined) {
        newRecordModel.values[fieldId] = values;
      } else if (newRecordModel.values) {
        // 如果values是undefined, 则删除这个字段的值
        const fieldIds = Object.keys(newRecordModel.values);
        const fieldKey = fieldIds.find((id) => id === fieldId);
        if (fieldKey) {
          delete newRecordModel.values[fieldId];
        }
      }
    }
    return newRecordModel;
  }

  /**
   * 字段创建/修改/删除, 记录单元格修改, 都会触发此方法操作
   * 给指定单元格设置数据, 构造并返回更新操作
   * 如果没有数据变化，则返回 null
   * 注意: 一旦设置了指定单元格数据, 单元格的data/computed/values都会被传入值覆盖,
   * 所以传入时, 如果data不期望变化, 保留原始值传入即可, 一旦传入null/undefined, 则会被删除, 因为存储这些值毫无意义
   * 例如:
   * 改之前 data: 1, computed: 2, values: 3
   * 传入时 data: 1, computed: 4, values: 5
   * 更新后 data: 1, computed: 4, values: 5
   *
   * 调用时需注意:
   * 1. 自动编号字段/创建人/修改人/创建时间/修改时间 等字段传入时, 只允许转换其他类型时才会出现
   */
  buildUpdateQuery(
    user: UserSO | null, // 由于存在匿名创建记录的场景, 可能是创建记录时触发的记录更新
    fieldCellModelMap: FieldCellModelMap,
  ): mongoose.UpdateOneModel<DatabaseRecordModel> | null {
    const unsetUpdate: Record<string, true> = {};
    const setUpdate: Record<string, CellValue> = {};

    // 这一行统一使用这个更新时间, 保证一致性
    const now = new Date();

    // 遍历需要修改的单元格
    for (const [fieldId, fieldCellModel] of Object.entries(fieldCellModelMap)) {
      // 单元格的数据
      const {
        // 更新后的字段类型
        bo: toFieldBO,
        cell: { data, computed, values },
      } = fieldCellModel;

      const sourceField = this._database.findFieldByFieldKey(fieldId);
      // 只检查已有字段的单元格数据, 这种情况只会发生在字段修改/删除
      if (sourceField) {
        // 自动编号字段, 不允许值为空
        if (toFieldBO.type === 'AUTO_NUMBER') {
          if (sourceField.type === 'AUTO_NUMBER') {
            // 判断是否是删除字段操作
            if (!isNullOrUndefined(data) || !isNullOrUndefined(values)) {
              // 非空, 不能修改, 直接抛异常, 不允许落库
              throw new Error('autoNumber field can not be modified');
            }
          } else if (isNullOrUndefined(data) || isNullOrUndefined(values)) {
            // 转换操作, 检查是否有传入值, 不然数据错误
            throw new Error('autoNumber field must have data and values');
          }
        }

        // 创建时间字段, 不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'CREATED_TIME') {
          if (sourceField.type === 'CREATED_TIME') {
            // 判断是否是删除字段操作
            if (!isNullOrUndefined(data) || !isNullOrUndefined(values)) {
              // 非空, 不能修改, 直接抛异常, 不允许落库
              throw new Error('createdTime field can not be modified');
            }
          } else {
            // 转换操作, 检查是否有传入值, 不然数据错误
            if (isNullOrUndefined(data) || isNullOrUndefined(values)) {
              throw new Error('createdTime field must have data and values');
            }
            // 跳过
            continue;
          }
        }

        // 修改时间字段, 不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'MODIFIED_TIME') {
          if (sourceField.type === 'MODIFIED_TIME') {
            // 判断是否是删除字段操作
            if (!isNullOrUndefined(data) || !isNullOrUndefined(values)) {
              // 非空, 不能修改, 直接抛异常, 不允许落库
              throw new Error('modifiedTime field can not be modified');
            }
          } else {
            // 转换操作, 检查是否有传入值, 不然数据错误
            if (isNullOrUndefined(data) || isNullOrUndefined(values)) {
              throw new Error('modifiedTime field must have data and values');
            }
            // 跳过
            continue;
          }
        }

        // 创建人字段不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'CREATED_BY') {
          if (sourceField.type === 'CREATED_BY') {
            // 判断是否是删除字段操作
            if (!isNullOrUndefined(data) || !isNullOrUndefined(values)) {
              // 非空, 不能修改, 直接抛异常, 不允许落库
              throw new Error('createdBy field can not be modified');
            }
          } else {
            // 转换操作, 检查是否有传入值, 不然数据错误
            if (isNullOrUndefined(data)) {
              throw new Error('createdBy field must have data and values');
            }
            // 跳过
            continue;
          }
        }

        // 修改人字段不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'MODIFIED_BY') {
          if (sourceField.type === 'MODIFIED_BY') {
            // 判断是否是删除字段操作
            if (!isNullOrUndefined(data) || !isNullOrUndefined(values)) {
              // 非空, 不能修改, 直接抛异常, 不允许落库
              throw new Error('modifiedBy field can not be modified');
            }
          } else {
            // 检查是否有传入值, 不然数据错误
            if (isNullOrUndefined(data)) {
              throw new Error('modifiedBy field must have data and values');
            }
            // 跳过
            continue;
          }
        }
      }

      // data
      if (isNullOrUndefined(data)) {
        unsetUpdate[`${CellKey.DATA}.${fieldId}`] = true;
      } else {
        setUpdate[`${CellKey.DATA}.${fieldId}`] = data;
      }

      // computed
      if (isNullOrUndefined(computed)) {
        unsetUpdate[`${CellKey.COMPUTED}.${fieldId}`] = true;
      } else {
        setUpdate[`${CellKey.COMPUTED}.${fieldId}`] = computed;
      }

      // values
      if (isNullOrUndefined(values)) {
        unsetUpdate[`${CellKey.VALUES}.${fieldId}`] = true;
      } else {
        setUpdate[`${CellKey.VALUES}.${fieldId}`] = values;
      }
    }

    if (Object.keys(setUpdate).length === 0 && Object.keys(unsetUpdate).length === 0) {
      // 没有数据变化
      return null;
    }

    const updatedFieldIds = Object.keys(fieldCellModelMap);
    // 如果有系统自动更新字段(modifiedTime, modifiedBy), 需要更新, 但是要忽略输入修改的字段
    const modifiedTimeFields = this.fields.filter(
      (field): field is ModifiedTimeFieldSO => !updatedFieldIds.includes(field.id) && field.type === 'MODIFIED_TIME',
    );
    for (const modifiedTimeField of modifiedTimeFields) {
      const updatedDateTime = new DateTimeSO(now);
      setUpdate[`${CellKey.DATA}.${modifiedTimeField.id}`] = updatedDateTime.toISOString();
      // 根据字段属性计算值
      const { property } = modifiedTimeField;
      const newTimeZone = property.timeZone && property.timeZone !== 'AUTO' ? property.timeZone : user?.timeZone;
      setUpdate[`${CellKey.VALUES}.${modifiedTimeField.id}`] = updatedDateTime.format({
        ...property,
        timeZone: newTimeZone,
      });
    }

    if (user) {
      const modifiedByFields = this.fields.filter(
        (field): field is ModifiedByFieldSO => !updatedFieldIds.includes(field.id) && field.type === 'MODIFIED_BY',
      );
      for (const modifiedByField of modifiedByFields) {
        setUpdate[`${CellKey.DATA}.${modifiedByField.id}`] = user.id;
        // 这里的初始化值, 在实际读取记录时不会使用, 会实时读取
        setUpdate[`${CellKey.VALUES}.${modifiedByField.id}`] = user.name;
      }
    }

    // console.log(`========> 此次更新的数据: ${JSON.stringify(setUpdate)}`);
    // console.log(`========> 此次删除的数据: ${JSON.stringify(unsetUpdate)}`);

    return {
      filter: { id: this.id, databaseId: this.databaseId },
      update: {
        $set: { ...setUpdate, updatedBy: user?.id, updatedAt: now },
        $unset: unsetUpdate,
        $inc: { revision: 1 },
      },
    };
  }

  static getUpdateQuery(
    user: UserSO | null,
    fields: DatabaseFieldWithId[],
    fieldCellModelMap: FieldCellModelMap,
  ): mongoose.UpdateQuery<DatabaseRecordModel> | null {
    const unsetUpdate: Record<string, true> = {};
    const setUpdate: Record<string, CellValue> = {};

    // 这一行统一使用这个更新时间, 保证一致性
    const now = new Date();

    // 遍历需要修改的单元格
    for (const [fieldId, fieldCellModel] of Object.entries(fieldCellModelMap)) {
      // 单元格的数据
      const {
        // 更新后的字段类型
        bo: toFieldBO,
        cell: { data, computed, values },
      } = fieldCellModel;

      const field = fields.find((f) => f.id === fieldId);
      // 只检查已有字段的单元格数据
      if (field) {
        // 自动编号字段, 不允许值为空
        if (toFieldBO.type === 'AUTO_NUMBER') {
          if (field.type === 'AUTO_NUMBER') {
            // 直接抛异常, 不允许落库
            throw new Error('autoNumber field can not be modified');
          }
          // 检查是否有传入值, 不然数据错误
          if (isNullOrUndefined(data) || isNullOrUndefined(values)) {
            throw new Error('autoNumber field must have data and values');
          }
        }

        // 创建时间字段, 不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'CREATED_TIME') {
          if (field.type === 'CREATED_TIME') {
            // 直接抛异常, 不允许落库
            throw new Error('createdTime field can not be modified');
          }
          // 检查是否有传入值, 不然数据错误
          if (isNullOrUndefined(data) || isNullOrUndefined(values)) {
            throw new Error('createdTime field must have data and values');
          }
          continue;
        }

        // 修改时间字段, 不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'MODIFIED_TIME') {
          if (field.type === 'MODIFIED_TIME') {
            // 直接抛异常, 不允许落库
            throw new Error('modifiedTime field can not be modified');
          }
          // 检查是否有传入值, 不然数据错误
          if (isNullOrUndefined(data) || isNullOrUndefined(values)) {
            throw new Error('modifiedTime field must have data and values');
          }
          continue;
        }

        // 创建人字段不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'CREATED_BY') {
          if (field.type === 'CREATED_BY') {
            // 直接抛异常, 不允许落库
            throw new Error('createdBy field can not be modified');
          }
          // 检查是否有传入值, 不然数据错误
          if (isNullOrUndefined(data)) {
            throw new Error('createdBy field must have data and values');
          }
          continue;
        }

        // 修改人字段不能被修改, 只允许转为其他类型单元格数据
        if (toFieldBO.type === 'MODIFIED_BY') {
          if (field.type === 'MODIFIED_BY') {
            // 直接抛异常, 不允许落库
            throw new Error('modifiedBy field can not be modified');
          }
          // 检查是否有传入值, 不然数据错误
          if (isNullOrUndefined(data)) {
            throw new Error('modifiedBy field must have data and values');
          }
          continue;
        }
      }

      // data
      if (isNullOrUndefined(data)) {
        unsetUpdate[`${CellKey.DATA}.${fieldId}`] = true;
      } else {
        setUpdate[`${CellKey.DATA}.${fieldId}`] = data;
      }

      // computed
      if (isNullOrUndefined(computed)) {
        unsetUpdate[`${CellKey.COMPUTED}.${fieldId}`] = true;
      } else {
        setUpdate[`${CellKey.COMPUTED}.${fieldId}`] = computed;
      }

      // values
      if (isNullOrUndefined(values)) {
        unsetUpdate[`${CellKey.VALUES}.${fieldId}`] = true;
      } else {
        setUpdate[`${CellKey.VALUES}.${fieldId}`] = values;
      }
    }

    if (Object.keys(setUpdate).length === 0 && Object.keys(unsetUpdate).length === 0) {
      // 没有数据变化
      return null;
    }

    const updatedFieldIds = Object.keys(fieldCellModelMap);
    // 如果有系统自动更新字段(modifiedTime, modifiedBy), 需要更新, 但是要忽略输入修改的字段
    const modifiedTimeFields = fields.filter(
      (field): field is DatabaseFieldWithId<DatabaseModifiedTimeField> =>
        !updatedFieldIds.includes(field.id) && field.type === 'MODIFIED_TIME',
    );
    for (const modifiedTimeField of modifiedTimeFields) {
      const updatedDateTime = new DateTimeSO(now);
      setUpdate[`${CellKey.DATA}.${modifiedTimeField.id}`] = updatedDateTime.toISOString();
      // 根据字段属性计算值
      const { property } = modifiedTimeField;
      const newTimeZone = property.timeZone && property.timeZone !== 'AUTO' ? property.timeZone : user?.timeZone;
      setUpdate[`${CellKey.VALUES}.${modifiedTimeField.id}`] = updatedDateTime.format({
        ...property,
        timeZone: newTimeZone,
      });
    }

    if (user) {
      const modifiedByFields = fields.filter(
        (field): field is DatabaseFieldWithId<DatabaseModifiedByField> =>
          !updatedFieldIds.includes(field.id) && field.type === 'MODIFIED_BY',
      );
      for (const modifiedByField of modifiedByFields) {
        setUpdate[`${CellKey.DATA}.${modifiedByField.id}`] = user.id;
        // 这里的初始化值, 在实际读取记录时不会使用, 会实时读取
        setUpdate[`${CellKey.VALUES}.${modifiedByField.id}`] = user.name;
      }
    }

    // console.log(`========> 此次更新的数据: ${JSON.stringify(setUpdate)}`);
    // console.log(`========> 此次删除的数据: ${JSON.stringify(unsetUpdate)}`);

    return {
      $set: { ...setUpdate, updatedBy: user?.id, updatedAt: now },
      $unset: unsetUpdate,
      $inc: { revision: 1 },
    };
  }

  /**
   * 构建新的记录
   */
  static buildNewRecordModel(
    spaceId: string,
    databaseId: string,
    fieldCellModelMap: FieldCellModelMap,
    context: CreateRecordContext,
  ): DatabaseRecordModel {
    const dataOfRecord: RecordData = {};
    const computedOfRecord: RecordData = {};
    const valueOfRecord: RecordValue = {};
    for (const [fieldId, fieldCellModel] of Object.entries(fieldCellModelMap)) {
      dataOfRecord[fieldId] = fieldCellModel.cell.data;
      computedOfRecord[fieldId] = fieldCellModel.cell.computed;
      valueOfRecord[fieldId] = fieldCellModel.cell.values;
    }
    const recordModel: DatabaseRecordModel = {
      id: generateNanoID(CONST_PREFIX_RECORD),
      spaceId,
      revision: 0,
      databaseId,
      subscribers: context.member ? [context.member.id] : [],
      data: dataOfRecord,
      computed: computedOfRecord,
      values: valueOfRecord,
      status: 'OPEN',
      createdBy: context.user?.id,
      updatedBy: context.user?.id,
      createdAt: context.now,
      updatedAt: context.now,
    };

    return recordModel;
  }

  /**
   * 获取自动注入的字段单元格数据
   * 更新一批记录之前, 由于系统字段(更新时间)的存在, 它需要批次处理保证更新时间的统一性
   * @param records 记录对象列表
   */
  static async initAutoInjectFieldCellModel(
    user: UserSO,
    spaceId: string,
    fieldId: string,
    records: RecordSO[],
    updateFieldBO:
      | DatabaseCreatedTimeField
      | DatabaseModifiedTimeField
      | DatabaseCreatedByField
      | DatabaseModifiedByField,
  ): Promise<RecordCellModelMap> {
    const recordCellModelMap: RecordCellModelMap = {};
    // 这一行统一使用这个更新时间, 保证一致性
    const now = new Date();
    switch (updateFieldBO.type) {
      case 'CREATED_TIME': {
        const { property } = updateFieldBO;
        const newTimeZone = property.timeZone && property.timeZone !== 'AUTO' ? property.timeZone : user.timeZone;
        for (const record of records) {
          const createdAt = new DateTimeSO(record.model.createdAt);
          recordCellModelMap[record.id] = {
            [fieldId]: {
              bo: updateFieldBO,
              cell: {
                data: createdAt.toISOString(),
                computed: undefined,
                values: createdAt.format({ ...updateFieldBO.property, timeZone: newTimeZone }),
              },
            },
          };
        }
        break;
      }
      case 'MODIFIED_TIME': {
        const { property } = updateFieldBO;
        const newTimeZone = property.timeZone && property.timeZone !== 'AUTO' ? property.timeZone : user.timeZone;
        for (const record of records) {
          const updatedDateTime = new DateTimeSO(now);
          recordCellModelMap[record.id] = {
            [fieldId]: {
              bo: updateFieldBO,
              cell: {
                data: updatedDateTime.toISOString(),
                computed: undefined,
                values: updatedDateTime.format({ ...updateFieldBO.property, timeZone: newTimeZone }),
              },
            },
          };
        }
        break;
      }
      case 'CREATED_BY': {
        // 取记录的创建人
        const userIds = records
          .map((record) => record.model.createdBy)
          .filter((userId) => userId !== null)
          .filter((userId) => userId !== undefined);
        const members = await UnitFactory.findMembers(userIds, spaceId);
        for (const record of records) {
          const createdBy = record.model.createdBy;
          const member = members.find((m) => m.userId === createdBy);
          if (member) {
            recordCellModelMap[record.id] = {
              [fieldId]: {
                bo: updateFieldBO,
                cell: {
                  data: member.userId,
                  computed: undefined,
                  values: member.getName(),
                },
              },
            };
          }
        }
        break;
      }
      case 'MODIFIED_BY': {
        // 取当前修改人
        const member = await UnitFactory.findMember(user.id, spaceId);
        if (member) {
          for (const record of records) {
            recordCellModelMap[record.id] = {
              [fieldId]: {
                bo: updateFieldBO,
                cell: {
                  data: member.userId,
                  computed: undefined,
                  values: member.getName(),
                },
              },
            };
          }
        }
        break;
      }
      default: {
        return recordCellModelMap;
      }
    }

    return recordCellModelMap;
  }

  /**
   * 初始化公式字段单元格数据
   */
  static async initFormulaCellModel(
    database: DatabaseSO,
    records: RecordSO[],
    fieldId: string,
    updateFieldBO: DatabaseFormulaField,
  ) {
    const recordCellModelMap: RecordCellModelMap = {};
    const databaseFields = database.getFields();
    const allFieldBO = databaseFields.map((field) => field.toBO());
    for (const record of records) {
      const cellModel = FormulaUtil.computeCellModel({ id: fieldId, ...updateFieldBO }, allFieldBO, record.model);
      recordCellModelMap[record.id] = {
        [fieldId]: { bo: updateFieldBO, cell: cellModel },
      };
    }
    return recordCellModelMap;
  }

  static async exists(recordId: string, spaceId: string): Promise<boolean> {
    const count = await db.mongo.databaseRecord(spaceId).countDocuments({ id: recordId, status: 'OPEN' });
    return count > 0;
  }

  /**
   * 获取指定表格、指定字段的单元格值，大于等于指定时间的最早/最接近的一条记录
   */
  static async findDatetimeFieldCellClosestRecordData(
    field: FieldSO,
    datetime: string,
  ): Promise<{ recordId?: string; cellData?: string }> {
    const { id: fieldId, spaceId, databaseId } = field;
    const path = `data.${fieldId}`;
    const records = await db.mongo
      .databaseRecord(spaceId)
      .aggregate([
        { $match: { databaseId, [path]: { $exists: true, $gte: datetime }, status: 'OPEN' } },
        { $sort: { [path]: 1 } },
        { $limit: 1 },
      ]);
    if (records.length === 0) {
      return {};
    }
    const record = records[0];
    return { recordId: record.id, cellData: record.data[fieldId] };
  }

  static async findDatetimeFieldRecords(field: FieldSO, begin: string, end?: string): Promise<RecordSO[]> {
    const { id: fieldId, spaceId, databaseId } = field;
    const path = `data.${fieldId}`;
    const matchQuery = end
      ? { databaseId, [path]: { $exists: true, $gte: begin, $lt: end }, status: 'OPEN' }
      : { databaseId, [path]: begin, status: 'OPEN' };
    const records = await db.mongo.databaseRecord(spaceId).aggregate([{ $match: matchQuery }]);
    if (records.length === 0) {
      return [];
    }
    const database = await field.getDatabase();
    const space = await database.getSpace();
    return records.map((record) => new RecordSO(space, database, record));
  }

  static countRecords(spaceId: string, databaseId?: string): Promise<number> {
    const query = databaseId ? { databaseId, status: 'OPEN' } : { spaceId, status: 'OPEN' };
    return db.mongo.databaseRecord(spaceId).countDocuments(query);
  }

  private static covertToInstanceId(
    databaseKey: string,
    fields: DatabaseField[],
    records: DatabaseRecord[],
    convertToInstanceId: (templateId: string) => string,
  ) {
    const fieldMap = _.keyBy(fields, 'templateId');

    const convertRecordData = (data: RecordData) => {
      for (const [key, value] of Object.entries(data)) {
        const field = fieldMap[key];
        if (field) {
          const newKey = convertToInstanceId(`${databaseKey}:${key}`);
          if (newKey) {
            let newValue = value;
            if (field.type === 'LINK' || field.type === 'ONE_WAY_LINK') {
              if (_.isArray(value)) {
                newValue = value.map((i) => convertToInstanceId(`${field.property.foreignDatabaseTemplateId}:${i}`));
              }
            }

            if (field.type === 'SINGLE_SELECT' || field.type === 'MULTI_SELECT') {
              if (isArrayOfType(value, (v) => typeof v === 'string')) {
                // 所有选项的 templateId
                const optionTemplateIds = field.property.options.map((opt) => opt.templateId);

                newValue = value.map((opt) => {
                  // TODO：兼容部分旧模板，部分模板直接使用 option id 而非 option template id
                  if (optionTemplateIds.includes(opt)) {
                    return convertToInstanceId(`${databaseKey}:${field.templateId}:${opt}`);
                  }
                  return opt;
                });
              }
            }

            // eslint-disable-next-line no-param-reassign
            data[newKey] = newValue;
            // eslint-disable-next-line no-param-reassign
            delete data[key];
          }
        }
      }
      return data;
    };
    const convertRecordValues = (values: RecordData) => {
      for (const [key, value] of Object.entries(values)) {
        const field = fieldMap[key];
        if (field) {
          const newKey = convertToInstanceId(`${databaseKey}:${key}`);
          if (newKey) {
            // eslint-disable-next-line no-param-reassign
            values[newKey] = value;
            // eslint-disable-next-line no-param-reassign
            delete values[key];
          }
        }
      }
      return values;
    };

    for (const record of records) {
      record.data = convertRecordData(record.data);
      if (record.values) {
        record.values = convertRecordValues(record.values);
      }
    }
  }

  private static replaceInstanceId(
    databaseKey: string,
    fields: DatabaseField[],
    records: DatabaseRecord[],
    replaceInstanceId: (id: string) => string,
  ) {
    const fieldMap = _.keyBy(fields, 'id');

    const convertRecordData = (data: RecordData) => {
      for (const [key, value] of Object.entries(data)) {
        const newKey = key.startsWith(CONST_PREFIX_FIELD)
          ? replaceInstanceId(key)
          : replaceInstanceId(`${databaseKey}:${key}`);
        let newValue = value;
        // fieldId
        if (newKey) {
          const field = fieldMap[newKey];
          if (field) {
            if (field.type === 'LINK' || field.type === 'ONE_WAY_LINK') {
              if (_.isArray(value)) {
                newValue = value.map((i) =>
                  (i as string).startsWith(CONST_PREFIX_RECORD)
                    ? replaceInstanceId(i as string)
                    : replaceInstanceId(`${field.property.foreignDatabaseTemplateId}:${i}`),
                );
              }
            }
            if (field.type === 'MEMBER' && _.isArray(value)) {
              newValue = value
                .map((i) => {
                  const isExist = replaceInstanceId(i as string);
                  if (parseInt(isExist, 10)) {
                    return i;
                  }
                  return null;
                })
                .filter((i) => i);
            }
          }
          // eslint-disable-next-line no-param-reassign
          data[newKey] = newValue;
          if (newKey !== key) {
            // eslint-disable-next-line no-param-reassign
            delete data[key];
          }
        }
      }
      return data;
    };

    const convertRecordValues = (values: RecordData) => {
      for (const [key, value] of Object.entries(values)) {
        const newKey = key.startsWith(CONST_PREFIX_FIELD)
          ? replaceInstanceId(key)
          : replaceInstanceId(`${databaseKey}:${key}`);
        let newValue = value;
        if (newKey) {
          const field = fieldMap[newKey];
          if (field) {
            if (['MEMBER'].includes(field.type)) {
              newValue = null;
            }
          }
          // eslint-disable-next-line no-param-reassign
          values[newKey] = newValue;
          if (newKey !== key) {
            // eslint-disable-next-line no-param-reassign
            delete values[key];
          }
        }
      }
      return values;
    };

    for (const record of records) {
      record.data = convertRecordData(record.data);
      if (record.values) {
        record.values = convertRecordValues(record.values);
      }
    }
  }

  static relationInstanceId(
    databaseKey: string,
    fields: DatabaseField[],
    records: DatabaseRecord[],
    opts: IRelationIdOpts,
  ): boolean {
    const { convertToInstanceId, replaceInstanceId } = opts;
    if (convertToInstanceId) {
      this.covertToInstanceId(databaseKey, fields, records, convertToInstanceId);
    }
    if (replaceInstanceId) {
      this.replaceInstanceId(databaseKey, fields, records, replaceInstanceId);
    }
    return true;
  }
}
