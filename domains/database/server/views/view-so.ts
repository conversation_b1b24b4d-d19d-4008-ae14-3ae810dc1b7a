import assert from 'assert';
import _ from 'lodash';
import type { AggregateOptions, FilterQuery, PipelineStage } from 'mongoose';
import { generateNanoID } from 'basenext/utils/nano-id';
import { IRelationIdOpts } from '@bika/domains/node/server/types';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  DatabaseRecordModel,
  DatabaseViewAttachType,
  DatabaseViewType,
  db,
  mongoose,
  Prisma,
  PrismaPromise,
  DatabaseFieldType,
} from '@bika/server-orm';
import { AggregateType } from '@bika/types/dashboard/bo';
import {
  BasicValueType,
  FilterCondition,
  SortedField,
  ViewFilter,
  ViewSortArray,
  View,
  ViewField,
  ViewType,
  ViewFieldSchema,
  ViewGroupArray,
  ViewExtra,
  ViewGroup,
  DatabaseField,
  DatabaseFieldWithId,
  CellValue,
  ViewSortArraySchema,
  ViewFilterSchema,
  ViewGroupArraySchema,
} from '@bika/types/database/bo';
import { DatabaseViewCreateDTO, DatabaseViewUpdateDTO } from '@bika/types/database/dto';
import { ViewRenderOpts, ViewProperty, ViewVO, CONST_PREFIX_VIEW, ViewSimpleVO } from '@bika/types/database/vo';
import { ToTemplateOptions } from '@bika/types/node/bo';
import { iString, iStringParse, LocaleType } from '@bika/types/system';
import { DatabaseLazyFix } from '../database-lazyfix';
import { FilterSO } from './filter-so';
import { SearchSO } from './search-so';
import { SortSO } from './sort-so';
import { DatabaseViewModel, DefaultViewName } from './types';
import { RecordGroupByLocation } from '../../shared/fields/field-type-helper/base';
import { FieldTypeHelper } from '../../shared/fields/field-type-helper/factory';
import { CellValueConvertorFactory, FieldContext } from '../cells/cellvalue-convertor/factory';
import { DatabaseSO } from '../database-so';
import { FieldSO } from '../fields/field-so';
import { RecordSO } from '../record-so';
import { databaseWithIncludeView } from '../types';

export class ViewSO {
  private _model: DatabaseViewModel;

  private readonly _database: DatabaseSO;

  /**
   * @deprecated 废弃
   */
  private readonly _ctx: FieldContext;

  /**
   * @deprecated 废弃
   */
  private keyword?: string;

  /**
   * @deprecated 废弃
   */
  private startRow: number;

  /**
   * @deprecated 废弃
   */
  private endRow: number;

  /**
   * 视图的过滤器, 保存到数据库中
   */
  private filter: FilterSO;

  /**
   * @deprecated 废弃
   */
  private additionalFilter?: FilterSO;

  /**
   * 视图的排序, 保存到数据库中
   */
  private sorts: SortSO[] = [];

  /**
   * @deprecated 废弃
   */
  private additionalSorts: SortSO[] = [];

  /**
   * 视图的分组, 保存到数据库中
   */
  private groups: ViewGroupArray;

  /**
   * @deprecated 废弃
   */
  private additionalGroups: ViewGroupArray = [];

  /**
   * @deprecated 废弃
   */
  private groupsSelect: CellValue[] = [];

  /**
   * @deprecated 废弃
   */
  private recordIds?: string[];

  /**
   * default constructor
   * @param database database service object
   * @param viewModel view model
   */
  constructor(database: DatabaseSO, viewModel: DatabaseViewModel) {
    this._database = database;
    this._model = viewModel;
    this.startRow = 0;
    this.endRow = 20;

    // Init Field Context
    this._ctx = {
      fields: database.getFields().map((field) => field.toBO()),
    };

    // Init sorts
    const sortsParsed = ViewSortArraySchema.safeParse(viewModel.sorts);
    if (sortsParsed.success) {
      for (const sort of sortsParsed.data) {
        if (sort.fieldId) {
          this.doAddSortByField(this.sorts, sort.fieldId, sort.asc);
        }
      }
    }

    // Init filter
    const filterParsed = ViewFilterSchema.safeParse(viewModel.filters);
    const viewFilter = filterParsed.success ? filterParsed.data : null;
    this.filter = new FilterSO(database, viewFilter);

    // Init Group
    const groupsParsed = ViewGroupArraySchema.safeParse(viewModel.groups);
    this.groups = groupsParsed.success ? groupsParsed.data : [];
  }

  public static async init(viewId: string): Promise<ViewSO> {
    const view = await db.prisma.databaseView.findUnique({
      where: {
        id: viewId,
      },
      include: {
        database: databaseWithIncludeView,
      },
    });
    if (!view) {
      throw new Error('View not found');
    }
    view.database.fields = await DatabaseLazyFix.fixFields(view.database.fields);
    return new ViewSO(DatabaseSO.initWithModel(view.database), view);
  }

  public static async initMaybeNull(viewId: string): Promise<ViewSO | null> {
    const view = await db.prisma.databaseView.findUnique({
      where: {
        id: viewId,
      },
      include: {
        database: databaseWithIncludeView,
      },
    });
    if (!view) {
      return null;
    }
    view.database.fields = await DatabaseLazyFix.fixFields(view.database.fields);
    return new ViewSO(DatabaseSO.initWithModel(view.database), view);
  }

  public static async initWithModel(viewModel: DatabaseViewModel) {
    const database = await DatabaseSO.init(viewModel.databaseId);
    return new ViewSO(database, viewModel);
  }

  /**
   * 创建一个临时的视图
   *
   * 目前主要用于关联列查询关联表时，要展示所有数据
   */
  public static createTempView(database: DatabaseSO) {
    const view = new ViewSO(database, {
      name: 'Temp View' as Prisma.JsonValue,
      relationType: 'DATABASE',
      type: 'TABLE',
      id: '',
      spaceId: database.spaceId,
      databaseId: database.id,
      templateId: null,
      description: '' as Prisma.JsonValue,
      style: null,
      updatedBy: null,
      preViewId: null,
      autoSave: true,
      hidden: false,
      filters: [],
      sorts: [],
      relationId: null,
      fields: [],
      lock: null,
      groups: [],
      extra: null,
      revision: 0,
      createdBy: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      frozenColumnCount: null,
      rowHeightLevel: null,
      autoHeadHeight: null,
      displayHiddenColumnWithinMirror: null,
    });
    return view;
  }

  get model() {
    return this._model;
  }

  /**
   * 获取冗余的spaceId
   */
  get spaceId() {
    return this.model.spaceId;
  }

  get database(): DatabaseSO {
    return this._database;
  }

  get id() {
    return this.model.id;
  }

  get preViewId() {
    return this.model.preViewId;
  }

  get name(): iString {
    return this.model.name as iString;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get databaseId() {
    return this.model.databaseId;
  }

  get description(): iString | undefined {
    return (this.model.description as iString) || undefined;
  }

  get type(): ViewType {
    return this.model.type.toString() as ViewType;
  }

  get property(): ViewProperty {
    return {
      autoSave: this.model.autoSave ?? false,
      hidden: this.model.hidden ?? false,
      frozenColumnCount: this.model.frozenColumnCount ?? undefined,
      rowHeightLevel: this.model.rowHeightLevel ?? undefined,
      autoHeadHeight: this.model.autoHeadHeight ?? false,
    };
  }

  /**
   * 视图里的字段, 空值则是全部字段, 因为视图不允许空字段
   * @returns View Field BO
   */
  get fields(): ViewField[] {
    return this.model.fields ? ViewFieldSchema.array().parse(this.model.fields) : [];
  }

  async getPreView(): Promise<ViewSO | null> {
    if (this.preViewId) {
      const view = await db.prisma.databaseView.findFirst({
        where: { id: this.preViewId, databaseId: this.databaseId, relationType: DatabaseViewAttachType.DATABASE },
      });
      if (view) {
        return ViewSO.initWithModel(view);
      }
    }

    return null;
  }

  async getNextView(): Promise<ViewSO | null> {
    const view = await db.prisma.databaseView.findFirst({
      where: { preView: { id: this.id }, databaseId: this.databaseId, relationType: DatabaseViewAttachType.DATABASE },
    });
    if (view) {
      return ViewSO.initWithModel(view);
    }
    return null;
  }

  /**
   * 视图BO
   */
  toBO(): View {
    // 展示所有字段, 区分显示和隐藏
    const viewFields = this.fields;
    const viewFieldMap = Object.fromEntries(viewFields.map((field) => [field.id, field]));
    const fields = this.database.getFields();
    for (const field of fields) {
      if (!viewFieldMap[field.id]) {
        viewFields.push({
          id: field.id,
          // 其他字段设为显示
          hidden: false,
        });
      }
    }
    return {
      name: this.name,
      description: this.description,
      type: this.type,
      filters: this.filter.toBO(),
      sorts: (this.model.sorts as ViewSortArray) || undefined,
      groups: (this.model.groups as ViewGroupArray) || undefined,
      templateId: this.model.templateId || undefined,
      id: this.id,
      // use view fields to keep sort
      fields: viewFields,
      databaseId: this.model.databaseId || undefined,
      extra: (this.model.extra as ViewExtra) || undefined,
    };
  }

  toTemplate(opts?: ToTemplateOptions): View {
    const view = _.omit(this.toBO(), ['databaseId', 'id']) as View;
    if (!view.templateId) {
      view.templateId = this.id;
    }
    for (const field of view?.fields ?? []) {
      if (opts?.getTemplateId && !field.templateId && field.id) {
        field.templateId = opts.getTemplateId(field.id);
      }
      field.id = undefined;
    }
    for (const cond of view?.filters?.conds ?? []) {
      if (opts?.getTemplateId && !cond.fieldTemplateId && cond.fieldId) {
        cond.fieldTemplateId = opts.getTemplateId(cond.fieldId);
        cond.fieldId = undefined;
      }
    }
    for (const sort of view.sorts ?? []) {
      if (opts?.getTemplateId && !sort.fieldTemplateId && sort.fieldId) {
        sort.fieldTemplateId = opts.getTemplateId(sort.fieldId);
        sort.fieldId = undefined;
      }
    }
    for (const group of view.groups ?? []) {
      if (opts?.getTemplateId && !group.fieldTemplateId && group.fieldId) {
        group.fieldTemplateId = opts.getTemplateId(group.fieldId);
        group.fieldId = undefined;
      }
    }
    if (view.extra && opts?.getTemplateId) {
      if (!view.extra.kanbanGroupingFieldTemplateId && view.extra.kanbanGroupingFieldId) {
        view.extra.kanbanGroupingFieldTemplateId = opts?.getTemplateId(view.extra.kanbanGroupingFieldId);
        view.extra.kanbanGroupingFieldId = undefined;
      }
      if (!view.extra.coverFieldTemplateId && view.extra.coverFieldId) {
        view.extra.coverFieldTemplateId = opts?.getTemplateId(view.extra.coverFieldId);
        view.extra.coverFieldId = undefined;
      }
    }
    return view;
  }

  getPublishOperations(): PrismaPromise<Prisma.BatchPayload>[] {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(db.prisma.databaseView.updateMany({ where: { id: this.id }, data: { templateId: this.id } }));
    }
    return operations;
  }

  /**
   * 获取视图的字段对象列表,确保排序是按照view fields排序
   * 1. view.fields = [] 时, 代表全字段
   * 2. view.fields = [field1, field2] 时, 代表只展示这两个字段
   */
  getFields(): FieldSO[] {
    const fieldMap = Object.fromEntries(this.database.getFields().map((field) => [field.id, field]));
    const viewFieldSOList = this.fields
      .map((field) => {
        if (field.id && fieldMap[field.id]) {
          return fieldMap[field.id];
        }
        return null;
      })
      .filter((field) => field !== null);
    const viewFieldMap = Object.fromEntries(viewFieldSOList.map((field) => [field.id, field]));

    const notInViewFields: FieldSO[] = [];
    for (const field of Object.values(fieldMap)) {
      if (!viewFieldMap[field.id]) {
        notInViewFields.push(field);
      }
    }
    // 给没有在 view fields中的列排序（其实 必然在view fields中）
    // const sortedFields = orderBy(notInViewFields, [
    //   'createdAt', // 其余条目按 createdAt 排序，越早创建越前
    //   'sequenceId', // 同一时间创建的条目，按 sequenceId 排序
    // ]);
    const sortedFields = notInViewFields.slice().sort((a, b) => {
      // 首先按 createdAt 排序（Date 类型）
      const timeDiff = a.createdAt.getTime() - b.createdAt.getTime();
      if (timeDiff !== 0) return timeDiff;

      // createdAt 相同时，处理 sequenceId（可能为 null）
      // 规则：null 值排在数字后面
      if (a.sequenceId === null && b.sequenceId === null) return 0;
      if (a.sequenceId === null) return 1; // a 的 sequenceId 为 null，排到后面
      if (b.sequenceId === null) return -1; // b 的 sequenceId 为 null，a 排前面

      // 当两者都是数字时，直接比较
      return a.sequenceId - b.sequenceId;
    });

    // 合并
    const fields = [...viewFieldSOList, ...sortedFields];
    if (fields.length === 0) {
      throw new Error(`No fields found in view: ${this.id}`);
    }
    // 排序，isPrimary排第一个
    // return orderBy(fields, [(field) => (field.primary ? 0 : 1)]);
    return fields.sort((a, b) => {
      // 将 primary 为 true 的元素排在最前面
      if (a.primary && !b.primary) return -1;
      if (!a.primary && b.primary) return 1;

      // 其他情况保持原始顺序（稳定排序）
      return 0;
    });
  }

  /**
   * @deprecated 废弃
   */
  setKeyword(keyword?: string): void {
    this.keyword = keyword;
  }

  /**
   * @deprecated 废弃
   */
  setStartRow(rowNum: number): void {
    this.startRow = rowNum;
  }

  /**
   * @deprecated 废弃
   */
  setEndRow(rowNum: number): void {
    this.endRow = rowNum;
  }

  /**
   * @deprecated 废弃
   */
  getEndRow(): number {
    return this.endRow;
  }

  /**
   * 设置视图的过滤条件
   * @param filter 视图的过滤条件, null 则清空过滤条件
   */
  setFilter(filter: ViewFilter | null) {
    this.filter = new FilterSO(this.database, filter);
  }

  /**
   * @deprecated 废弃
   */
  setAdditionalFilter(filter?: ViewFilter | null) {
    if (filter) {
      this.additionalFilter = new FilterSO(this.database, filter);
    } else {
      this.additionalFilter = undefined;
    }
  }

  /**
   *  @deprecated 废弃
   */
  setRecordIds(recordIds?: string[]) {
    this.recordIds = recordIds;
  }

  /**
   * 设置视图的排序条件
   * @param sorts 视图的排序条件
   */
  setSorts(sorts: SortSO[]) {
    this.sorts = sorts;
  }

  /**
   * @deprecated 废弃
   */
  setAdditionalSort(sorts?: ViewSortArray) {
    if (!sorts?.length) {
      return;
    }
    for (const sort of sorts) {
      this.addAdditionalSort(sort.fieldId!, sort.asc);
    }
  }

  /**
   * @deprecated 废弃
   */
  setAdditionalGroup(groups?: ViewGroupArray) {
    if (!groups?.length) {
      return;
    }
    this.additionalGroups = groups;
  }

  /**
   * @deprecated 废弃
   */
  setGroupSelect(groupsSelect?: CellValue[]) {
    if (!groupsSelect?.length) {
      return;
    }

    const convertGroupSelect = (groups: ViewGroupArray, groupSelect: CellValue[]): CellValue[] => {
      assert(groups.length >= groupSelect.length, 'Invalid group select');

      const newGroupSelect: CellValue[] = _.cloneDeep(groupSelect);

      for (let i = 0; i < groupSelect.length; i++) {
        const group = groups[i];
        const select = groupSelect[i];

        assert(group.fieldId, 'Group fieldId is required');
        const field = this.database.getFieldByFieldKey(group.fieldId);

        // Convert to number
        if (['NUMBER', 'CURRENCY', 'PERCENT', 'RATING', 'AUTO_NUMBER'].includes(field.type)) {
          if (select !== null) {
            newGroupSelect[i] = Number(select);
          }
        }
      }

      return newGroupSelect;
    };

    const groups = this.getGroups();
    this.groupsSelect = convertGroupSelect(groups, groupsSelect);
  }

  public hasFilterConditions(): boolean {
    return (
      !!this.keyword ||
      this.filter.conditions?.length > 0 ||
      (!!this.additionalFilter && this.additionalFilter.conditions.length > 0) ||
      this.groups.length > 0 ||
      this.additionalGroups.length > 0
    );
  }

  /**
   * @deprecated 废弃
   */
  public nextPage() {
    this.startRow += 20;
    this.endRow += 20;
  }

  /**
   * @deprecated 废弃
   */
  addAdditionalSort(fieldKey: string, isAsc: boolean) {
    this.doAddSortByField(this.additionalSorts, fieldKey, isAsc);
  }

  /**
   * Add Sort by Field to specific sort list
   *
   * @param targetSortList sort list
   * @param field field
   * @param isAsc is asc
   * @deprecated 废弃
   */
  private doAddSortByField(targetSortList: SortSO[], fieldKey: string, isAsc: boolean) {
    const field = this.database.findFieldByFieldKey(fieldKey);
    if (!field) {
      return;
    }
    const location = field.sortField();
    const cvt = CellValueConvertorFactory.create(field.toBO(), this._ctx);
    const basicValueType = cvt.basicValueType();

    // 当字段类型是 Array 时，仅将第一个值作为排序依据
    if (basicValueType === BasicValueType.Array) {
      const sortSO = new SortSO(location, `${field.id}.0`, isAsc);
      targetSortList.push(sortSO);
    } else {
      const sortSO = new SortSO(location, field.id, isAsc);
      targetSortList.push(sortSO);
    }
  }

  public async getGroupRows(user?: UserSO): Promise<
    Array<{
      record: RecordSO;
      groupCount: number;
    }>
  > {
    assert(this.isGetGroupList(), 'Invalid group list');

    const pipeline = await this.buildGetGroupListPipeline(user);

    // console.log('getGroupRows pipeline', JSON.stringify(pipeline, null, 2));

    // Do get records
    let recordPOs = await db.mongo
      .databaseRecord(this.spaceId)
      .aggregate(pipeline, { collation: { locale: 'zh' } })
      .exec();
    // Fill ids
    recordPOs = recordPOs.map((record) => ({
      id: generateNanoID('GRP'),
      data: record._id,
      groupCount: record.count,
    }));

    const space = await this.database.getSpace();

    return recordPOs.map((recordPO) => ({
      record: RecordSO.initWithModel(space, this.database, recordPO),
      groupCount: recordPO.groupCount,
    }));
  }

  public async getRecordsWithTotal(user?: UserSO): Promise<{
    records: RecordSO[];
    total: number | null;
  }> {
    // 快速查询 database 的 records 总数
    const recordsCount = await this.database.getRecordsCount();

    // 1. 当 View 没有 Filter conditions 时，直接将 recordsCount 作为 total
    if (!this.hasFilterConditions()) {
      return {
        records: await this.getRecords(user),
        total: recordsCount,
      };
    }

    // 2. 当 recordsCount > 50000 时，total 为 null
    if (recordsCount > 50000) {
      return {
        records: await this.getRecords(user),
        total: null,
      };
    }

    // <= 50000 时，才会计算 total 的实际数
    const [records, total] = await Promise.all([this.getRecords(user), this.getRecordCount(user)]);
    return {
      records,
      total,
    };
  }

  /**
   * @deprecated 待重构,性能问题
   */
  public async getRecords(user?: UserSO, disablePage?: boolean): Promise<RecordSO[]> {
    assert(!this.isGetGroupList(), 'Invalid get records');

    const { pipeline, aggregateOptions } = await this.buildGetRecordsPipeline(user, disablePage);

    // Do get records
    const recordPOs = await db.mongo
      .databaseRecord(this.spaceId)
      .aggregate<DatabaseRecordModel>(pipeline, aggregateOptions)
      .exec();

    const space = await this.database.getSpace();

    return recordPOs.map((recordPO) => RecordSO.initWithModel(space, this.database, recordPO));
  }

  public async getRecordCount(user?: UserSO): Promise<number> {
    // 查询分组列表
    if (this.isGetGroupList()) {
      const pipeline = await this.buildGetGroupListPipeline(user, true);

      const result = await db.mongo
        .databaseRecord(this.spaceId)
        .aggregate([...pipeline, { $count: 'total' }])
        .exec();
      return result[0]?.total ?? 0;
    }
    return this.totalRecordsCountWithoutGroup(user);
  }

  public async totalRecordsCountWithoutGroup(user?: UserSO): Promise<number> {
    const { pipeline } = await this.buildGetRecordsPipeline(user, true);

    // console.debug(`getRecordsCount pipeline`, JSON.stringify(pipeline, null, 2));

    const result = await db.mongo
      .databaseRecord(this.spaceId)
      .aggregate([...pipeline, { $count: 'total' }])
      .exec();
    return result[0]?.total ?? 0;
  }

  /**
   * 判断是否是获取分组列表
   */
  public isGetGroupList(): boolean {
    const groups = this.getGroups();
    if (groups.length) {
      assert(this.groupsSelect.length <= groups.length, 'Invalid group select');
      return groups.length !== this.groupsSelect.length;
    }
    return false;
  }

  private getGroups(): ViewGroupArray {
    if (this.additionalGroups.length) {
      return this.additionalGroups;
    }
    return this.groups;
  }

  private async buildGetRecordsPipeline(
    user?: UserSO,
    disablePage?: boolean,
  ): Promise<{
    pipeline: PipelineStage[];
    aggregateOptions: AggregateOptions;
  }> {
    const conditions: FilterQuery<DatabaseRecordModel>[] = [];

    // Base conditions
    const baseCondition = {
      databaseId: this.databaseId,
    };
    conditions.push(baseCondition);

    // View filter
    const filter = await this.filter.buildQuery(user);
    conditions.push(filter);

    // Temporary filter
    const temporaryFilterQuery = await this.additionalFilter?.buildQuery(user);
    if (temporaryFilterQuery) {
      conditions.push(temporaryFilterQuery);
    }

    // Search
    if (this.keyword) {
      const searchSO = SearchSO.create(this.database);
      const searchQuery = searchSO.buildQuery(this.keyword, this._ctx);
      conditions.push(searchQuery);
    }

    // Specify recordIds
    if (this.recordIds?.length) {
      conditions.push({
        id: {
          $in: this.recordIds,
        },
      });
    }

    // Group by conditions
    // TODO: 待重构分组逻辑
    if (this.groupsSelect.length > 0) {
      const groups = this.getGroups();
      assert(groups.length === this.groupsSelect.length, 'Group select is required');

      const condition: FilterQuery<DatabaseRecordModel> = {};
      for (let i = 0; i < groups.length; i++) {
        const { fieldId } = groups[i];
        const select = this.groupsSelect[i];

        assert(fieldId, 'Group fieldId is required');
        const field = this._ctx.fields.find((f) => f.id === fieldId);
        assert(field, 'Group field not found');

        const fieldHelper = FieldTypeHelper.create(field, this._ctx);
        condition[`data.${fieldId}`] = fieldHelper.groupFilterCondition(select);
      }
      conditions.push(condition);
    }

    // 是否启用中文排序
    let enableSortByZhLocale = true;

    // View Sort
    const sortWay: Record<string, 1 | -1> = {};
    if (this.sorts.length > 0) {
      for (const sort of this.sorts) {
        const [sortField, order] = sort.toRecordSortVO();
        sortWay[sortField] = order;
      }
    }
    // Temporary Sort
    if (this.additionalSorts.length > 0) {
      for (const sort of this.additionalSorts) {
        const [sortField, order] = sort.toRecordSortVO();
        sortWay[sortField] = order;
      }
    }

    // Default sort
    if (Object.keys(sortWay).length === 0) {
      sortWay._id = -1;

      // 使用默认排序时，关闭中文排序
      enableSortByZhLocale = false;
    }

    const pipeline: PipelineStage[] = [
      {
        $match: { $and: conditions, status: 'OPEN' },
      },
      {
        $sort: sortWay,
      },
    ];

    // Enable page
    if (!disablePage) {
      pipeline.push(
        {
          $skip: this.startRow,
        },
        {
          $limit: this.endRow - this.startRow,
        },
      );
    }

    // 启用中文排序
    if (enableSortByZhLocale) {
      return {
        pipeline,
        aggregateOptions: { collation: { locale: 'zh' } },
      };
    }

    return { pipeline, aggregateOptions: {} };
  }

  /**
   * 获取分组列表的 Pipeline
   * TODO: 重构，与 buildGetRecords 逻辑重复
   */
  private async buildGetGroupListPipeline(user?: UserSO, disablePage?: boolean): Promise<PipelineStage[]> {
    const conditions: FilterQuery<DatabaseRecordModel>[] = [];

    // Base conditions
    const baseCondition = {
      databaseId: this.databaseId,
    };
    conditions.push(baseCondition);

    // View filter
    const filter = await this.filter.buildQuery(user);
    conditions.push(filter);

    // Temporary filter
    const temporaryFilterQuery = await this.additionalFilter?.buildQuery(user);
    if (temporaryFilterQuery) {
      conditions.push(temporaryFilterQuery);
    }

    const groupPipeline: PipelineStage.Group['$group'] = {
      _id: {},
      count: { $sum: 1 },
    };
    const groups = this.getGroups();
    assert(groups.length > 0, 'Group is required');
    assert(this.groupsSelect.length <= groups.length, 'Invalid group select');
    const findGroups = groups.slice(0, this.groupsSelect.length + 1);

    // Group by
    for (const group of findGroups) {
      assert(group.fieldId, 'Group fieldId is required');

      const field = this._ctx.fields.find((f) => f.id === group.fieldId);
      assert(field, 'Group field not found');
      const fieldHelper = FieldTypeHelper.create(field, this._ctx);

      groupPipeline._id[group.fieldId] = `$${fieldHelper.groupByPosition()}.${group.fieldId}`;
    }

    // Group select
    if (this.groupsSelect.length > 0) {
      // console.log('groupsSelect', this.groupsSelect);
      const condition: FilterQuery<DatabaseRecordModel> = {};
      for (let i = 0; i < this.groupsSelect.length; i++) {
        const select = this.groupsSelect[i];
        const fieldId = findGroups[i].fieldId;
        assert(fieldId, 'Group fieldId is required');

        const field = this._ctx.fields.find((f) => f.id === fieldId);
        assert(field, 'Group field not found');

        const fieldHelper = FieldTypeHelper.create(field, this._ctx);
        condition[`data.${fieldId}`] = fieldHelper.groupFilterCondition(select);
      }
      conditions.push(condition);
    }

    // Sort by group name
    const sortWay: Record<string, 1 | -1> = {};
    if (findGroups.length) {
      const lastGroup = findGroups[findGroups.length - 1];
      assert(lastGroup.fieldId, 'Group fieldId is required');
      const field = this.database.getFieldByFieldKey(lastGroup.fieldId);

      const cvt = CellValueConvertorFactory.create(field.toBO(), this._ctx);
      const basicValueType = cvt.basicValueType();

      if (basicValueType === BasicValueType.Array) {
        const sortSO = new SortSO('', `${field.id}.0`, lastGroup.asc);
        const [sortField, order] = sortSO.toRecordSortVO();
        sortWay[`_id.${sortField}`] = order;
      } else {
        const sortSO = new SortSO('', field.id, lastGroup.asc);
        const [sortField, order] = sortSO.toRecordSortVO();
        sortWay[`_id.${sortField}`] = order;
      }
    }
    // View Sort
    if (this.sorts.length > 0) {
      for (const sort of this.sorts) {
        const [sortField, order] = sort.toRecordSortVO();
        sortWay[sortField] = order;
      }
    }
    // Temporary Sort
    if (this.additionalSorts.length > 0) {
      for (const sort of this.additionalSorts) {
        const [sortField, order] = sort.toRecordSortVO();
        sortWay[sortField] = order;
      }
    }

    const pipeline: PipelineStage[] = [
      {
        $match: { $and: conditions },
      },
      {
        $group: groupPipeline,
      },
      {
        $sort: sortWay,
      },
    ];

    // Enable page
    if (!disablePage) {
      pipeline.push(
        {
          $skip: this.startRow,
        },
        {
          $limit: this.endRow - this.startRow,
        },
      );
    }

    return pipeline;
  }

  /**
   * @deprecated 升级模板未开放, 不予理会
   */
  updateOperation(userId: string, viewTemplate: View): PrismaPromise<DatabaseViewModel> {
    return db.prisma.databaseView.update({
      where: {
        id: this.id,
      },
      data: {
        name: viewTemplate.name,
        type: DatabaseViewType.TABLE,
        filters: viewTemplate.filters,
        sorts: viewTemplate.sorts,
        updatedBy: userId,
      },
    });
  }

  toVO(opts?: ViewRenderOpts): ViewVO {
    const { locale } = opts ?? {};
    return {
      id: this.id,
      name: this.getName(locale),
      description: iStringParse(this.description, locale),
      type: this.type,
      templateId: this.templateId,
      databaseId: this.databaseId,
      preViewId: this.preViewId,
      filters: this.filter.toVO(),

      groups: this.groups,
      property: this.property,
      columns: this.getFields().map((column) => column.toVO({ locale, viewFields: this.fields })),

      extra: (this.model.extra ?? undefined) as ViewExtra | undefined,
    };
  }

  toSimpleVO(opts?: ViewRenderOpts): ViewSimpleVO {
    return {
      id: this.id,
      name: iStringParse(this.name, opts?.locale),
      description: iStringParse(this.description, opts?.locale),
      type: this.type,
    };
  }

  static countByDatabaseId(databaseId: string): PrismaPromise<number> {
    return db.prisma.databaseView.count({
      where: {
        databaseId,
        relationType: DatabaseViewAttachType.DATABASE,
      },
    });
  }

  static async exists(databaseId: string, viewId: string): Promise<boolean> {
    const count = await db.prisma.databaseView.count({
      where: {
        databaseId,
        id: viewId,
      },
    });
    return count > 0;
  }

  static listByDatabaseId(databaseId: string): PrismaPromise<DatabaseViewModel[]> {
    return db.prisma.databaseView.findMany({
      where: {
        databaseId,
        relationType: DatabaseViewAttachType.DATABASE,
      },
    });
  }

  static findOneById(databaseId: string, viewId: string): PrismaPromise<DatabaseViewModel | null> {
    return db.prisma.databaseView.findUnique({
      where: {
        databaseId,
        id: viewId,
      },
    });
  }

  static findOneByTemplateId(databaseId: string, templateId: string): PrismaPromise<DatabaseViewModel | null> {
    return db.prisma.databaseView.findFirst({
      where: {
        databaseId,
        templateId,
      },
    });
  }

  static findOneByPreViewId(databaseId: string, preViewId: string | null): PrismaPromise<DatabaseViewModel | null> {
    return db.prisma.databaseView.findFirst({
      where: {
        databaseId,
        preViewId,
        relationType: DatabaseViewAttachType.DATABASE,
      },
    });
  }

  public static async createView(userId: string, spaceId: string, view: DatabaseViewCreateDTO): Promise<ViewSO> {
    const lastViewId = await this.databaseLastViewId(view.databaseId);
    const { id, operation } = this.createViewOperation(userId, spaceId, {
      ...view,
      preViewId: lastViewId,
    });
    await operation;
    return this.init(id);
  }

  async delete() {
    await db.prisma.$transaction(async (ctx) => {
      await ctx.databaseView.delete({
        where: {
          id: this.id,
        },
      });
      const nextView = await db.prisma.databaseView.findUnique({
        where: {
          preViewId: this.id,
        },
      });
      if (nextView) {
        await ctx.databaseView.update({
          where: {
            id: nextView.id,
          },
          data: {
            preViewId: this.preViewId,
          },
        });
      }
    });
  }

  async update(userId: string, view: DatabaseViewUpdateDTO): Promise<void> {
    const operations: PrismaPromise<DatabaseViewModel>[] = [
      db.prisma.databaseView.update({
        where: {
          id: this.id,
        },
        data: {
          name: view.name,
          type: view.type,
          filters: view.filters,
          sorts: view.sorts,
          groups: view.groups,
          fields: view.fields,
          extra: view.extra,
          updatedBy: userId,
        },
      }),
    ];
    if (view.preViewId !== undefined) {
      operations.push(...(await this.updateSortByPreViewIdOperation(userId, view.preViewId)));
    }
    await db.prisma.$transaction(operations);
  }

  async updateSortByPreViewIdOperation(userId: string, preViewId: string | null) {
    // sorted views
    const views = await this.database.getViews();
    const operations: PrismaPromise<DatabaseViewModel>[] = [];
    // insert the item to old sorted views
    const sortedViews = views.reduce((prev: ViewSO[], cur, index) => {
      if (index === 0 && preViewId === null) {
        prev.push(this);
      }
      if (cur.id !== this.id) {
        prev.push(cur);
      }
      if (cur.id === preViewId) {
        prev.push(this);
      }

      return prev;
    }, []);
    // 根据顺序修改preViewId
    sortedViews.forEach((view, index) => {
      const newPreViewId = index === 0 ? null : sortedViews[index - 1].id;
      const newNextViewId = index === sortedViews.length - 1 ? null : sortedViews[index + 1].id;
      if (newPreViewId !== view.preViewId) {
        operations.push(
          db.prisma.databaseView.update({
            where: { id: view.id },
            data: {
              preView: newPreViewId
                ? {
                    connect: {
                      id: newPreViewId,
                    },
                  }
                : { disconnect: true },
              nextView: newNextViewId
                ? {
                    connect: {
                      id: newNextViewId,
                    },
                  }
                : undefined,
              updatedBy: userId,
            },
          }),
        );
      }
    });
    return operations;
  }

  static async updateSort(userId: string, views: { id: string }[]): Promise<PrismaPromise<DatabaseViewModel>[]> {
    const operations: PrismaPromise<DatabaseViewModel>[] = [];
    views.forEach((item, index) => {
      const preId = index === 0 ? null : views[index - 1].id;
      const nextId = index === views.length - 1 ? null : views[index + 1].id;
      operations.push(
        db.prisma.databaseView.update({
          where: { id: item.id },
          data: {
            preView: preId ? { connect: { id: preId } } : { disconnect: true },
            nextView: nextId ? { connect: { id: nextId } } : undefined,
            updatedBy: userId,
          },
        }),
      );
    });
    return operations;
  }

  /**
   * 添加视图字段操作集合
   * @returns update view operations
   */
  public addFieldOperations(userId: string, field: ViewField): PrismaPromise<unknown> {
    let updateFields: ViewField[] = [];
    const fields = this.fields;
    if (fields.length > 0) {
      // 有设置视图指定字段
      updateFields = [...fields, field];
    } else {
      // 补全所有字段的配置
      const allFields: ViewField[] = this.database.getFields().map((item) => ({
        id: item.id,
        templateId: item.templateId,
        hidden: false,
      }));
      updateFields = [...allFields, field];
    }

    return db.prisma.databaseView.update({
      where: {
        id: this.id,
      },
      data: {
        fields: updateFields,
        updatedBy: userId,
      },
    });
  }

  /**
   * 移除对某个字段的引用
   */
  public unlinkFieldOperation(userId: string, fieldId: string): PrismaPromise<DatabaseViewModel> {
    let updateFields: ViewField[] | undefined;
    const fields = this.fields;
    if (fields.length > 0) {
      // 视图有设置了自定义字段, 移除 View 中对应的字段
      updateFields = this.fields.filter((field) => field.id && field.id !== fieldId);
    }

    // 移除 Filter 中对应的字段
    const filters = (this.model.filters ?? undefined) as unknown as ViewFilter;
    if (filters?.conds) {
      filters.conds = filters.conds.filter((condition) => condition.fieldId !== fieldId);
    }

    // 移除 Sort 中对应的字段
    let sorts = this.model.sorts as ViewSortArray;
    if (sorts?.length) {
      sorts = sorts.filter((sort) => sort.fieldId !== fieldId);
      this.model.sorts = sorts;
    }

    // 移除 Group 中对应的字段
    let groups = this.model.groups as ViewGroupArray;
    if (groups?.length) {
      groups = groups.filter((group) => group.fieldId !== fieldId);
      this.model.groups = groups;
    }

    return db.prisma.databaseView.update({
      where: {
        id: this.id,
      },
      data: {
        fields: updateFields,
        filters,
        sorts,
        groups,
        updatedBy: userId,
      },
    });
  }

  public static createViewOperation(
    userId: string,
    spaceId: string,
    createParams: DatabaseViewCreateDTO,
  ): { id: string; operation: PrismaPromise<DatabaseViewModel> } {
    const viewInput = this.buildViewCreateWithDatabaseInput(userId, spaceId, createParams);
    const operation = db.prisma.databaseView.create({
      data: viewInput,
    });
    return {
      id: viewInput.id,
      operation,
    };
  }

  public static createViewOperationWithTemplate(
    userId: string,
    createParams: {
      databaseId: string;
      spaceId: string;
      preViewId?: string;
      viewTemplate: View;
    },
  ): { id: string; operation: PrismaPromise<DatabaseViewModel> } {
    const { databaseId, spaceId, preViewId, viewTemplate } = createParams;
    const viewInput = this.buildViewCreateWithDatabaseInput(userId, spaceId, {
      name: viewTemplate.name,
      databaseId,
      templateId: viewTemplate.templateId,
      preViewId,
      filters: viewTemplate.filters,
      sorts: viewTemplate.sorts,
      type: viewTemplate.type,
      extra: viewTemplate.extra,
    });
    const operation = db.prisma.databaseView.create({
      data: viewInput,
    });
    return {
      id: viewInput.id,
      operation,
    };
  }

  /**
   * 构建视图创建数据
   * 需连接已存在的database
   * @param userId user id
   * @param spaceId space id
   * @param createViewParam create view param
   */
  static buildViewCreateWithDatabaseInput(
    userId: string,
    spaceId: string,
    createViewParam: DatabaseViewCreateDTO,
  ): Prisma.DatabaseViewCreateManyInput {
    const {
      name,
      databaseId,
      templateId,
      preViewId,
      fields,
      filters,
      sorts,
      groups,
      type,
      relationId,
      relationType,
      extra,
    } = createViewParam;
    const id = generateNanoID('viw');
    return {
      id: relationId ?? id,
      relationType: relationType ?? DatabaseViewAttachType.DATABASE,
      spaceId,
      databaseId,
      name,
      templateId,
      preViewId,
      type: type || DatabaseViewType.TABLE,
      fields,
      filters: filters as unknown as Prisma.NullableJsonNullValueInput,
      sorts: sorts || [],
      groups: groups || [],
      extra: extra as ViewExtra | undefined,
      revision: 0,
      createdBy: userId,
      updatedBy: userId,
    };
  }

  /**
   * 构建创建视图的数据
   * 无需连接database,直接在database.createMany.data里追加后一起新增
   * @param userId user id
   * @param createViewParam create view param
   */
  public static buildViewCreateWithoutDatabaseInput(
    userId: string,
    createViewParam: {
      name: iString;
      spaceId: string;
      templateId?: string;
      preViewId?: string;
      filters?: ViewFilter;
      sorts?: Prisma.JsonValue;
      fields?: ViewField[];
      id?: string;
      type?: ViewType;
      groups?: ViewGroupArray;
      extra?: ViewExtra;
    },
  ): Prisma.DatabaseViewCreateManyDatabaseInput {
    const { name, spaceId, templateId, preViewId, filters, sorts, fields, id, type, groups, extra } = createViewParam;
    const viewId = id || generateNanoID('viw');
    return {
      id: viewId,
      spaceId,
      relationType: DatabaseViewAttachType.DATABASE,
      name,
      templateId,
      preViewId,
      type: type || DatabaseViewType.TABLE,
      filters: filters as unknown as Prisma.NullableJsonNullValueInput,
      sorts: sorts || [],
      fields,
      groups,
      extra,
      revision: 0,
      createdBy: userId,
      updatedBy: userId,
    };
  }

  /**
   * 根据模板构建数据库类型表视图创建数据
   * @param userId user id
   * @param views views in database template
   */
  public static buildManyViewCreateWithoutDatabaseInput(
    userId: string,
    spaceId: string,
    views: View[],
  ): Prisma.DatabaseViewCreateWithoutDatabaseInput[] {
    const inputs: Prisma.DatabaseViewCreateManyDatabaseInput[] = [];
    if (!views || views.length === 0) {
      // 默认视图
      const viewInput = this.buildViewCreateWithoutDatabaseInput(userId, {
        name: DefaultViewName,
        spaceId,
      });
      inputs.push(viewInput);
    } else {
      // 按配置构建创建视图数据
      let preViewId: string | undefined;
      for (let i = 0; i < views.length; i += 1) {
        const view = views[i];
        const viewInput = this.buildViewCreateWithoutDatabaseInput(userId, {
          id: view.id,
          name: view.name,
          spaceId,
          templateId: view.templateId,
          preViewId,
          filters: view.filters,
          sorts: view.sorts,
          fields: view.fields,
          type: view.type,
          extra: view.extra,
          groups: view.groups,
        });
        inputs.push(viewInput);
        preViewId = viewInput.id;
      }
    }
    return inputs;
  }

  static defaultCreatePayload(
    id: string,
    userId: string,
    spaceId: string,
    databaseId: string,
    relationType: DatabaseViewAttachType,
  ): PrismaPromise<Prisma.BatchPayload> {
    return db.prisma.databaseView.createMany({
      data: [
        {
          id,
          spaceId,
          databaseId,
          relationType,
          name: DefaultViewName,
          type: DatabaseViewType.TABLE,
          revision: 0,
          createdBy: userId,
        },
      ],
    });
  }

  static async databaseLastViewId(id: string): Promise<string | undefined> {
    const result = await db.prisma.databaseView.findFirst({
      where: {
        databaseId: id,
        relationType: DatabaseViewAttachType.DATABASE,
        nextView: {
          is: null,
        },
      },
      select: {
        id: true,
      },
    });
    return result?.id;
  }

  static relationInstanceId(databaseKey: string, views: View[], opts: IRelationIdOpts): boolean {
    const { convertToInstanceId, replaceInstanceId, convertToTemplateId } = opts;

    const processFieldInstanceId = (fieldId: string | undefined, fieldTemplateId: string | undefined) => {
      if (convertToInstanceId && !fieldId && fieldTemplateId) {
        return { fieldId: convertToInstanceId(`${databaseKey}:${fieldTemplateId}`), fieldTemplateId };
      }
      if (replaceInstanceId && fieldId) {
        return { fieldId: replaceInstanceId(fieldId), fieldTemplateId };
      }
      if (convertToTemplateId) {
        return { fieldId: undefined, fieldTemplateId: fieldTemplateId || (fieldId && convertToTemplateId(fieldId)) };
      }
      return { fieldId, fieldTemplateId };
    };

    const relationFieldInstanceId = (field: ViewField) => {
      const { fieldId, fieldTemplateId } = processFieldInstanceId(field.id, field.templateId);
      field.id = fieldId;
      field.templateId = fieldTemplateId;
    };

    const relationObjWithFieldInstanceId = (objWithField: FilterCondition | SortedField | ViewGroup) => {
      const { fieldId, fieldTemplateId } = processFieldInstanceId(objWithField.fieldId, objWithField.fieldTemplateId);
      objWithField.fieldId = fieldId;
      objWithField.fieldTemplateId = fieldTemplateId;
    };

    for (const view of views) {
      for (const field of view?.fields ?? []) {
        relationFieldInstanceId(field);
      }
      for (const cond of view?.filters?.conds ?? []) {
        relationObjWithFieldInstanceId(cond);
      }
      for (const sort of view.sorts ?? []) {
        relationObjWithFieldInstanceId(sort);
      }
      for (const group of view.groups ?? []) {
        relationObjWithFieldInstanceId(group);
      }
      if (view.extra) {
        const { fieldId: kanbanGroupingFieldId, fieldTemplateId: kanbanGroupingFieldTemplateId } =
          processFieldInstanceId(view.extra.kanbanGroupingFieldId, view.extra.kanbanGroupingFieldTemplateId);
        view.extra.kanbanGroupingFieldId = kanbanGroupingFieldId;
        view.extra.kanbanGroupingFieldTemplateId = kanbanGroupingFieldTemplateId;

        const { fieldId: coverFieldId, fieldTemplateId: coverFieldTemplateId } = processFieldInstanceId(
          view.extra.coverFieldId,
          view.extra.coverFieldTemplateId,
        );
        view.extra.coverFieldId = coverFieldId;
        view.extra.coverFieldTemplateId = coverFieldTemplateId;
      }
    }
    return true;
  }

  static boToPO(spaceId: string, databaseId: string, view: View): DatabaseViewModel {
    return {
      id: view.id || generateNanoID(CONST_PREFIX_VIEW),
      name: view.name,
      description: view.description || null,
      filters: (view.filters as Prisma.JsonValue) || null,
      sorts: view.sorts || null,
      fields: view.fields || null,
      spaceId,
      databaseId,
      relationType: 'DATABASE',
      relationId: null,
      templateId: view.templateId || null,
      type: view.type,
      preViewId: null,
      autoSave: null,
      hidden: null,
      lock: null,
      groups: null,
      extra: view.extra || null,
      frozenColumnCount: null,
      rowHeightLevel: null,
      autoHeadHeight: null,
      style: null,
      revision: 0,
      displayHiddenColumnWithinMirror: null,
      createdBy: null,
      updatedBy: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * 这个方法用的是record.values 作为统计值，因此要保证values是正确写入的,如果values不存在，则直接返回原始值
   * @param groupField 分组field
   * @param aggregate 聚合 参数
   * @returns
   */
  async aggregateRecordByFieldKey(
    groupField: DatabaseFieldWithId,
    aggregate?: {
      field: DatabaseField;
      type: AggregateType;
    },
    user?: UserSO,
  ): Promise<{ recordCount: number; data: CellValue | null; values: CellValue | null; aggregateNumber?: number }[]> {
    const getPrefix = (fieldType: DatabaseFieldType): RecordGroupByLocation =>
      fieldType === 'FORMULA' || fieldType === 'LOOKUP' ? RecordGroupByLocation.VALUES : RecordGroupByLocation.DATA;

    const getGroupId = (fieldType: DatabaseFieldType): string => {
      if (fieldType === 'CREATED_TIME') {
        return '$createdAt';
      }
      if (fieldType === 'MODIFIED_TIME') {
        return '$updatedAt';
      }
      if (fieldType === 'CREATED_BY') {
        return '$createdBy';
      }
      if (fieldType === 'MODIFIED_BY') {
        return '$updatedBy';
      }
      const prefix = getPrefix(fieldType);
      return `$${prefix}.${groupField.id}`;
    };

    const groupStage = (): { _id: string } | { [key: string]: mongoose.AccumulatorOperator } => {
      const groupId = getGroupId(groupField.type as DatabaseFieldType);
      const defaultGroup = {
        _id: groupId,
        recordCount: { $sum: 1 },
        values: {
          $first: `$values.${groupField.id}`, // 对应fieldId的values值
        },
      };
      if (!aggregate) {
        return defaultGroup;
      }
      // 如果有聚合参数，则把聚合方法也加上
      const { field, type } = aggregate;

      const aggregatePrefix = getPrefix(field.type as DatabaseFieldType);

      const aggregateFunc = {
        $toDouble:
          field.type === 'LOOKUP' ? { $first: `$${aggregatePrefix}.${field.id}` } : `$${aggregatePrefix}.${field.id}`,
      };
      switch (type) {
        case 'SUM':
          return {
            ...defaultGroup,
            aggregateNumber: {
              $sum: aggregateFunc,
            },
          };
        case 'AVG':
          return {
            ...defaultGroup,
            aggregateNumber: {
              $avg: aggregateFunc,
            },
          };
        case 'MAX':
          return {
            ...defaultGroup,
            aggregateNumber: {
              $max: aggregateFunc,
            },
          };
        case 'MIN':
          return {
            ...defaultGroup,
            aggregateNumber: {
              $min: aggregateFunc,
            },
          };
        default:
          break;
      }
      return defaultGroup;
    };

    const sortStage = (): Record<string, 1 | -1 | mongoose.Expression.Meta> => {
      if (aggregate) {
        return { aggregateNumber: 1 };
      }
      return { recordCount: 1 };
    };

    const projectStage = (): { [field: string]: mongoose.AnyExpression | mongoose.Expression } => {
      const project = {
        _id: 0,
        recordCount: 1,
        data: '$_id',
        values: 1,
      };
      if (aggregate) {
        return { ...project, aggregateNumber: 1 };
      }
      return project;
    };

    const filter = await this.filter.buildQuery(user);

    return db.mongo.databaseRecord(this.spaceId).aggregate([
      {
        $match: {
          databaseId: this.databaseId,
          ...filter,
        },
      },
      {
        $group: groupStage(),
      },
      { $project: projectStage() },
      { $sort: sortStage() },
    ]);
  }
}
