import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { ViewType, ViewExtra, View } from '@bika/types/database/bo';
import { CONST_PREFIX_VIEW, ViewFieldVO, ViewDetailVO, ViewSimpleVO, RecordRenderVO } from '@bika/types/database/vo';
import { NodeRenderOpts, IBOProcessor, BORenderOpts } from '@bika/types/node/vo';
import { iStringParse } from '@bika/types/system';
import { DatabaseBOProcessor } from '../database-bo-processor';
import { ViewFilterBOProcessor } from './filter-bo-processort';

export class ViewBOProcessor implements IBOProcessor<View> {
  private _view: View;

  private _databaseId: string;

  constructor(view: View, databaseId: string) {
    this._view = _.cloneDeep(view);
    if (!this._view.id) {
      this._view.id = generateNanoID(CONST_PREFIX_VIEW);
    }
    this._databaseId = databaseId;
  }

  get bo(): View {
    return this._view;
  }

  get id(): string {
    return this.bo.id!;
  }

  get databaseId(): string {
    return this._databaseId;
  }

  getDatabase(opts?: BORenderOpts): DatabaseBOProcessor {
    const { getProcessor } = opts || {};
    const database = getProcessor?.(this.databaseId);
    if (!database) {
      throw new Error('ViewBOProcessor:database not found');
    }
    return database as DatabaseBOProcessor;
  }

  get filter(): ViewFilterBOProcessor | undefined {
    return this.bo.filters && new ViewFilterBOProcessor(this.bo.filters);
  }

  getExtraVO(opts?: BORenderOpts): ViewExtra | undefined {
    if (!this.bo.extra) {
      return undefined;
    }
    const { kanbanGroupingFieldId, kanbanGroupingFieldTemplateId, coverFieldId, coverFieldTemplateId } = this.bo.extra;
    return {
      kanbanGroupingFieldId:
        kanbanGroupingFieldId || kanbanGroupingFieldTemplateId
          ? this.getDatabase(opts)
              ?.getField(kanbanGroupingFieldId || kanbanGroupingFieldTemplateId!)
              .getId()
          : undefined,
      kanbanGroupOptionIds: this.bo.extra.kanbanGroupOptionIds, // todo
      coverFieldId:
        coverFieldId || coverFieldTemplateId
          ? this.getDatabase(opts)
              ?.getField(coverFieldId || coverFieldTemplateId!)
              .getId()
          : undefined,
    };
  }

  getColumns(opts?: NodeRenderOpts): ViewFieldVO[] {
    if (!this.bo.fields) {
      return this.getDatabase(opts)?.fields.map((f) => f.toVO(opts)) || [];
    }
    const columns = this.bo.fields.map((f) => {
      const field = this.getDatabase(opts)?.getField(f.id || f.templateId!);
      const fieldVO = field?.toVO(opts);
      return {
        ...f,
        ...fieldVO,
      };
    });

    for (const field of this.getDatabase(opts)?.fields || []) {
      if (!columns.find((c) => c.id === field.getId())) {
        columns.push(field.toVO(opts));
      }
    }
    return columns;
  }

  toSimpleVO(opts?: NodeRenderOpts): ViewSimpleVO {
    return {
      id: this.id,
      name: iStringParse(this.bo.name, opts?.locale),
      description: iStringParse(this.bo.description, opts?.locale),
      type: this.bo.type as ViewType,
    };
  }

  toVO<V = ViewDetailVO>(opts?: BORenderOpts): V {
    const records = this.getDatabase(opts)?.records || [];
    const columns = this.getColumns(opts);
    const visibleColumns = columns.filter((c) => !c.hidden);
    // find preView from database.views
    const preViewId = this.getDatabase(opts).views.find((v, index) => {
      if (v.id === this.id) {
        return this.getDatabase(opts).views[index - 1]?.id;
      }
      return null;
    });
    const vo = {
      ...this.toSimpleVO(opts),
      templateId: this.bo.templateId,
      databaseId: this.getDatabase(opts).id,
      preViewId,
      filters: this.filter?.toVO(),
      sorts: this.bo.sorts,
      groups: this.bo.groups,
      columns,
      extra: this.getExtraVO(opts),
      records: {
        rows: records.map((r) => {
          const record = r.toVO<RecordRenderVO>(opts);
          const cells = _.cloneDeep(record.cells);
          // remove cells key which is not in  visible columns
          Object.keys(cells).forEach((key) => {
            if (!visibleColumns.find((c) => c.id === key)) {
              delete cells[key];
            }
          });
          return { ...record, cells };
        }),
        total: records.length,
      },
    };
    return vo as V;
  }
}
