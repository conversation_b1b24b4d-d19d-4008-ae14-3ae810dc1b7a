import { generateNanoID } from 'basenext/utils/nano-id';
import { IRelationIdOpts } from '@bika/domains/node/server/types';
import { DatabaseSingleSelectField } from '@bika/types/database/bo';
import { CONST_PREFIX_OPTION } from '@bika/types/database/vo';
import { AbstractFieldBOProcessor } from './field-bo-processor';

export class SingleSelectFieldBOProcessor extends AbstractFieldBOProcessor<DatabaseSingleSelectField> {
  constructor(bo: DatabaseSingleSelectField) {
    super(bo);

    let filledId = false;

    // 初始化一下选项ID
    const { property } = this.bo;
    for (const option of property?.options ?? []) {
      if (!option.id) {
        option.id = generateNanoID(CONST_PREFIX_OPTION);

        filledId = true;
      }
    }

    if (filledId) {
      const defaultOption = property.options.find((option) => option.templateId === this.bo.property.defaultValue);
      if (defaultOption) {
        this.bo.property.defaultValue = defaultOption.id;
      }
    }
  }

  relationInstanceId(databaseId: string, opts: IRelationIdOpts): void {
    const { property } = this.bo;

    const { convertToInstanceId } = opts;
    if (!convertToInstanceId) {
      return;
    }

    const { defaultValue, options } = property;

    if (defaultValue) {
      const optionTemplateIds = options.map((option) => option.templateId);
      if (optionTemplateIds.includes(defaultValue)) {
        const templateId = `${databaseId}:${this.bo.templateId}:${defaultValue}`;
        property.defaultValue = convertToInstanceId(templateId);
      }
    }
  }
}
