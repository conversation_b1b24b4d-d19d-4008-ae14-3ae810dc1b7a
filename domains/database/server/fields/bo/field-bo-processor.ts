import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { IRelationIdOpts } from '@bika/domains/node/server/types';
import { UserSO } from '@bika/domains/user/server';
import { $Enums, db, mongoose, Prisma, PrismaPromise } from '@bika/server-orm';
import { DatabaseField, DatabaseFieldType } from '@bika/types/database/bo';
import { CONST_PREFIX_FIELD, FieldVO } from '@bika/types/database/vo';
import { NodeRenderOpts } from '@bika/types/node/vo';
import { iStringParse } from '@bika/types/system';
import { FieldBOContext, FieldBOProcessor } from './types';
import { type DatabaseSO } from '../../database-so';
import { FieldAdjacencyTable } from '../field-adjacency-table';
import { FieldDirectedGraph } from '../field-directed-graph';
import { type FieldSO } from '../field-so';

/**
 * 默认的字段BO处理器
 */
export abstract class AbstractFieldBOProcessor<T extends DatabaseField> implements FieldBOProcessor {
  // 继承类修改它即可
  protected bo: T;

  protected id: string;

  // 当前字段依赖的有向图缓存, 涉及到依赖链路更新时无需重新构建依赖链路图, 用于更新字段业务
  protected directedGraph: FieldDirectedGraph | undefined;

  constructor(bo: T) {
    this.bo = _.cloneDeep(bo);
    // 填充当前字段 id （如果没有）
    if (!this.bo.id) {
      this.bo.id = generateNanoID(CONST_PREFIX_FIELD);
    }

    this.id = this.bo.id;
  }

  getBO(): T {
    return this.bo;
  }

  getId(): string {
    return this.id;
  }

  handleProperty(_ctx?: FieldBOContext): void {}

  /**
   * 获取当前字段的依赖字段ID
   * @returns {string[]} 依赖的字段ID
   * @description 目前只有formula/link/lookup会有依赖其他字段
   */
  protected getDependFields(_database: DatabaseSO): FieldSO[] | Promise<FieldSO[]> {
    // 默认没有依赖谁
    return [];
  }

  /**
   * 构建邻接表
   * @param {DatabaseSO} database 数据库对象
   * @returns {FieldAdjacencyTable} 字段邻接表
   * @description 该方法会递归地收集所有依赖关系，构建一个邻接表
   */
  private async buildAdjacencyTable(database: DatabaseSO): Promise<FieldAdjacencyTable> {
    // 邻接表
    const adjacencyTable = new FieldAdjacencyTable();
    // 访问过的节点集合
    const visited = new Set<string>();
    // 收集依赖方法
    const collectRelations = async (
      currentField: FieldSO,
      at: FieldAdjacencyTable,
      visitedFieldIds: Set<string>,
    ): Promise<void> => {
      if (visitedFieldIds.has(currentField.id)) return;
      visitedFieldIds.add(currentField.id);

      // 收集右邻接点（依赖）
      const dependencies = await currentField.getDependencies();
      for (const dep of dependencies) {
        at.addField(currentField);
        at.addEdge(currentField.id, dep);
        await collectRelations(dep, at, visitedFieldIds);
      }

      // 收集左邻接点（被依赖）
      const dependents = await currentField.getDependent();
      for (const parent of dependents) {
        at.addField(parent);
        at.addEdge(parent.id, currentField);
        await collectRelations(parent, at, visitedFieldIds);
      }
    };

    // 双向递归收集(DFS)
    const depFields = await this.getDependFields(database);
    // 这里要是没依赖, 下面不会运行下去
    // console.log(`=========> ${this.bo.name} - depFieldIds: ${depFieldIds}`);
    for (const depField of depFields) {
      adjacencyTable.addEdge(this.id, depField);
      await collectRelations(depField, adjacencyTable, visited);
    }
    // 递归收集完毕,返回邻接表
    return adjacencyTable;
  }

  /**
   * 以当前字段为起点，构建有向图
   * @param {DatabaseSO} database 数据库对象
   * @return {FieldDirectedGraph} 字段有向图
   * @description 该方法会递归地收集所有依赖关系，构建一个有向图
   */
  private async toDirectedGraph(database: DatabaseSO): Promise<FieldDirectedGraph> {
    if (!this.directedGraph) {
      const adjacencyTable = await this.buildAdjacencyTable(database);
      // console.log(`=========> ${this.bo.name} - 邻接表: ${adjacencyTable.visualize()}`);
      this.directedGraph = new FieldDirectedGraph(adjacencyTable);
    }
    return this.directedGraph;
  }

  /**
   * 根据字段的有向图判断是否存在循环引用
   * @param {DatabaseSO} database 数据库对象
   * @return {boolean} 是否存在循环引用
   */
  private async hasCircularReference(database: DatabaseSO): Promise<boolean> {
    // 目前只有formula/link/lookup会检查
    const directedGraph = await this.toDirectedGraph(database);
    // console.log(`=========> ${directedGraph.dfs(this.id)}`);
    return directedGraph.hasCycle();
  }

  /**
   * 是否需要检查循环引用
   * 只有Link/Lookup/Formula字段更改或者被转换此类型会有循环引用风险
   */
  protected needCheckCircularReference(): boolean {
    return false;
  }

  /**
   * 检查当前字段是否存在循环引用, 这个操作只会在更改字段时调用, 其他字段操作情况不需要
   * @param {DatabaseSO} database 数据库对象
   * @throws {ServerError} 如果存在循环引用，则抛出异常
   * @description 该方法会检查当前字段的依赖关系是否存在循环引用
   */
  async checkCircularReference(database: DatabaseSO): Promise<void> {
    if (!this.needCheckCircularReference()) {
      return;
    }
    const hasCycleReference = await this.hasCircularReference(database);
    if (hasCycleReference) {
      throw new ServerError(errors.database.field_cause_circular_reference);
    }
  }

  /**
   * 给模版安装时候的实例化替换, 主要是跨表的templateId变量替换
   */
  relationInstanceId(_databaseId: string, _opts: IRelationIdOpts): void {}

  toPrismaInput(userId: string, database: DatabaseSO, primary?: boolean): Prisma.DatabaseFieldCreateInput {
    return {
      id: this.id,
      templateId: this.bo.templateId,
      spaceId: database.spaceId,
      database: { connect: { id: database.id } },
      primary: primary ?? this.bo.primary ?? false,
      type: this.bo.type as $Enums.DatabaseFieldType,
      name: this.bo.name as Prisma.InputJsonValue,
      description: this.bo.description as Prisma.InputJsonValue,
      required: this.bo.required,
      unique: this.bo.unique,
      property: this.bo.property as Prisma.InputJsonValue,
      validators: this.bo.validators,
      revision: 0,
      createdBy: userId,
      updatedBy: userId,
    };
  }

  toPrismaInputWithoutDatabase(
    userId: string,
    spaceId: string,
    options?: { primary?: boolean; ctx?: FieldBOContext },
  ): Prisma.DatabaseFieldCreateWithoutDatabaseInput {
    const { primary } = options || {};
    return {
      id: this.id,
      templateId: this.bo.templateId,
      spaceId,
      primary: primary ?? this.bo.primary ?? false,
      type: this.bo.type as $Enums.DatabaseFieldType,
      name: this.bo.name as Prisma.InputJsonValue,
      description: this.bo.description as Prisma.InputJsonValue,
      required: this.bo.required,
      unique: this.bo.unique,
      property: this.bo.property as Prisma.InputJsonValue,
      createdBy: userId,
      updatedBy: userId,
    };
  }

  /**
   * 构造默认的创建本字段的DB操作
   */
  protected buildCreateOperation(userId: string, database: DatabaseSO): PrismaPromise<unknown>[] {
    const data = this.toPrismaInput(userId, database);
    return [db.prisma.databaseField.create({ data })];
  }

  getPrismaCreateOperation(
    userId: string,
    database: DatabaseSO,
  ): Promise<PrismaPromise<unknown>[]> | PrismaPromise<unknown>[] {
    return this.buildCreateOperation(userId, database);
  }

  initializeCellAsPage(
    _user: UserSO,
    _database: DatabaseSO,
    _options?: { session?: mongoose.ClientSession },
  ): Promise<void> {
    // 默认不需要更新单元格
    return Promise.resolve();
  }

  getPrismaUpdateOperations(
    _userId: string,
    _database: DatabaseSO,
  ): Promise<PrismaPromise<unknown>[]> | PrismaPromise<unknown>[] {
    return [];
  }

  toVO(opts?: NodeRenderOpts): FieldVO {
    const { locale } = opts || {};
    return {
      id: this.id,
      templateId: this.bo.templateId,
      name: iStringParse(this.bo.name, locale),
      description: iStringParse(this.bo.description, locale),
      type: this.bo.type as DatabaseFieldType,
      property: this.bo.property || undefined,
      primary: this.bo.primary!,
      required: this.bo.required ?? undefined,
      privilege: this.bo.privilege,
    } as FieldVO;
  }
}
