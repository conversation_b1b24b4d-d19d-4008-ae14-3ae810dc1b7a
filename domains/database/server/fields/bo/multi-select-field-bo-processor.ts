import { generateNanoID } from 'basenext/utils/nano-id';
import { IRelationIdOpts } from '@bika/domains/node/server/types';
import { DatabaseMultiSelectField } from '@bika/types/database/bo';
import { CONST_PREFIX_OPTION } from '@bika/types/database/vo';
import { AbstractFieldBOProcessor } from './field-bo-processor';

export class MultiSelectFieldBOProcessor extends AbstractFieldBOProcessor<DatabaseMultiSelectField> {
  constructor(bo: DatabaseMultiSelectField) {
    super(bo);

    let filledId = false;

    // 初始化一下选项ID
    const { property } = this.bo;
    for (const option of property?.options ?? []) {
      if (!option.id) {
        option.id = generateNanoID(CONST_PREFIX_OPTION);

        filledId = true;
      }
    }

    if (filledId) {
      const defaultOption = property.options.find((option) => option.templateId === this.bo.property.defaultValue);
      if (defaultOption) {
        this.bo.property.defaultValue = defaultOption.id;
      }
    }
  }

  relationInstanceId(databaseId: string, opts: IRelationIdOpts): void {
    const { property } = this.bo;

    const { convertToInstanceId } = opts;
    if (!convertToInstanceId) {
      return;
    }

    const { defaultValue, options } = property;
    if (defaultValue) {
      const optionTemplateIds = options.map((option) => option.templateId);
      if (Array.isArray(defaultValue)) {
        // 正确的结构
        const templateIds = defaultValue
          .map((value) => {
            if (optionTemplateIds.includes(value)) {
              return `${databaseId}:${this.bo.templateId}:${value}`;
            }
            return undefined;
          })
          .filter(Boolean) as string[];
        property.defaultValue = templateIds.map((templateId) => convertToInstanceId(templateId));
      } else if (optionTemplateIds.includes(defaultValue)) {
        // 旧结构
        const templateId = `${databaseId}:${this.bo.templateId}:${defaultValue}`;
        property.defaultValue = convertToInstanceId(templateId);
      }
    }
  }
}
