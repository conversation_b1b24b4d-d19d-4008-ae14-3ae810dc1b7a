// eslint-disable-next-line max-classes-per-file
import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import { convertStringToArray } from '@bika/domains/shared/server';
import { isArrayOfType, isNonEmptyString, isNullOrUndefined, isStringArray } from '@bika/domains/shared/shared';
import { UserSO } from '@bika/domains/user/server';
import {
  DatabaseFieldConfigSelectOption,
  DatabaseMultiSelectField,
  DatabaseSingleSelectField,
  getRandomColor,
  MultiSelectFieldProperty,
  SingleSelectFieldProperty,
  CellValue,
} from '@bika/types/database/bo';
import { CONST_PREFIX_OPTION } from '@bika/types/database/vo';
import { iStringParse, type iString } from '@bika/types/i18n/bo';
import { FieldSO } from './field-so';
import type {
  BuildCellModelOptions,
  CreateRecordContext,
  RecordSortLocation,
  UpdateCellModelOptions,
  UpdateRecordContext,
} from './types';
import { CellModel } from '../cells/types';
import { RecordSO } from '../record-so';
import { RecordCellModelMap } from '../types';

abstract class SelectFieldSO<T extends DatabaseSingleSelectField | DatabaseMultiSelectField> extends FieldSO<T> {
  get options() {
    return this.property.options;
  }

  public findOptionById(id: string): DatabaseFieldConfigSelectOption | null {
    if (!this.options) {
      return null;
    }
    return this.options.find((option) => option.id === id) ?? null;
  }

  public findOptionByName(name: string): DatabaseFieldConfigSelectOption | null {
    if (!this.options) {
      return null;
    }
    return this.options.find((option) => option.name === name) ?? null;
  }

  protected createOption(name: string): DatabaseFieldConfigSelectOption {
    // 已使用的颜色
    const usedColors = this.options.map((option) => option.color).filter((color) => color !== undefined);
    return {
      id: generateNanoID(CONST_PREFIX_OPTION),
      name,
      color: getRandomColor(usedColors),
    };
  }

  override processCellDataOnCreate(data: CellValue, _ctx: CreateRecordContext): string[] | null {
    if (isNullOrUndefined(data) || !Array.isArray(data) || data.length === 0) {
      return null;
    }
    return this.getOrCreateOptions(data as string[]).map((option) => {
      assert(option.id);
      return option.id;
    });
  }

  override processCellDataOnUpdate(data: string[] | null): string[] | null {
    if (isNullOrUndefined(data) || data.length === 0) {
      return null;
    }
    return this.getOrCreateOptions(data).map((option) => {
      assert(option.id);
      return option.id;
    });
  }

  protected getOrCreateOptions(idOrNames: string[]): DatabaseFieldConfigSelectOption[] {
    return idOrNames.map((i) => {
      // 优先匹配选项ID
      const opt = this.findOptionById(i);
      if (opt) {
        return opt;
      }

      // 再则匹配选项名称
      let option = this.findOptionByName(i);

      if (option) {
        return option;
      }

      // 均未匹配到则新建选项
      option = this.createOption(i);
      this.hasPropertyChanged = true;
      this.options.push(option);
      return option;
    });
  }

  override getFakeCellData(): { data: string[]; value: iString[] } {
    if (this.options.length === 0) {
      return { data: [], value: [] };
    }
    return { data: [this.options[0].id || 'option_id'], value: [this.options[0].name] };
  }

  public sortField(): RecordSortLocation {
    return 'values';
  }

  protected override shouldUpdateCells(other: SingleSelectFieldProperty | MultiSelectFieldProperty): boolean {
    // 选项ID或名称发生变化, 需要重新创建单元格
    // 两个前提:
    // 1. 原选项名称被修改
    // 2. 原选项某些被删除
    const otherOptions = other.options;
    const diffOptions = this.findDiffOptions(this.property.options, otherOptions);
    return diffOptions.removed.length > 0 || diffOptions.updated.length > 0;
  }

  private findDiffOptions(
    oldOptions: DatabaseFieldConfigSelectOption[],
    newOptions: DatabaseFieldConfigSelectOption[],
  ): {
    added: DatabaseFieldConfigSelectOption[];
    removed: DatabaseFieldConfigSelectOption[];
    updated: DatabaseFieldConfigSelectOption[];
  } {
    // 将数组转为 Map 结构（id 作为键）
    const oldMap = new Map(oldOptions.map((item) => [item.id, item]));
    const newMap = new Map(newOptions.map((item) => [item.id, item]));

    const added: DatabaseFieldConfigSelectOption[] = [];
    const removed: DatabaseFieldConfigSelectOption[] = [];
    const updated: DatabaseFieldConfigSelectOption[] = [];

    // 检查新增项和修改项
    for (const [id, newItem] of newMap) {
      const oldItem = oldMap.get(id);
      if (!oldItem) {
        added.push(newItem); // 新增项
      } else if (newItem.name !== oldItem.name) {
        updated.push(newItem); // 修改项（仅对比 name 字段）
      }
    }

    // 检查删除项
    for (const [id, oldItem] of oldMap) {
      if (!newMap.has(id)) {
        removed.push(oldItem); // 删除项
      }
    }

    return { added, removed, updated };
  }

  refreshCells(
    _user: UserSO,
    records: RecordSO[],
    updateFieldBO: DatabaseSingleSelectField | DatabaseMultiSelectField,
  ): RecordCellModelMap {
    // 选项发生变化, 重建单元格数据
    const { options: newOptions } = updateFieldBO.property;
    const { options: oldOptions } = this.property;
    // 根据选项的ID/名称来提取区别
    const diffOptions = this.findDiffOptions(oldOptions, newOptions);
    // console.log(`======> diffOptions`, diffOptions);
    const recordCellModelMap: RecordCellModelMap = {};
    for (const record of records) {
      const cell = record.getCellSO(this.id);
      // 存的是选项ID, 只要里面的选项ID没在改变的范围内, 就不需要重新赋值
      const data = cell.getModelData();
      // 存的是选项昵称, 但不需要理会它, 只需要处理data完重新赋值即可
      // const value = this.getModelValue();

      // 空值直接跳过
      if (isNullOrUndefined(data)) {
        continue;
      }

      if (isArrayOfType(data, (v) => typeof v === 'string')) {
        // 是否在变化范围内
        const hasRemoved = diffOptions.removed.some((opt) => opt.id && data.includes(opt.id));
        const hasUpdated = diffOptions.updated.some((opt) => opt.id && data.includes(opt.id));
        const isInDiffOptions = hasRemoved || hasUpdated;
        // console.log(`======> ${record.id} - ${data} isInDiffOptions`, isInDiffOptions);
        if (!isInDiffOptions) {
          // 选项没有变化, 不需要重新赋值
          continue;
        }
        // 保持原来的选项ID顺序
        const newData: string[] = data
          .map((id) => {
            const option = newOptions.find((opt) => opt.id === id);
            if (option) {
              return option.id;
            }
            return undefined;
          })
          .filter((id) => id !== undefined);

        const newValue: iString[] | undefined = newData
          .map((id) => {
            const option = newOptions.find((opt) => opt.id === id);
            if (option) {
              return option.name;
            }
            return undefined;
          })
          .filter((name) => name !== undefined);

        recordCellModelMap[record.id] = {
          [this.id]: { bo: updateFieldBO, cell: { data: newData, computed: undefined, values: newValue } },
        };
      }
    }
    return recordCellModelMap;
  }
}

export class SingleSelectFieldSO extends SelectFieldSO<DatabaseSingleSelectField> {
  override assertValueType(input: Exclude<CellValue, null>): boolean {
    // 只能是字符串数组, 且只仅有一个元素或空数组
    return this.isCellValueValidStringArray(input);
  }

  override hasDefaultValue(): boolean {
    const defaultValue = this.property.defaultValue;
    return isNonEmptyString(defaultValue);
  }

  override fillDefaultCellModel(context: CreateRecordContext): { data?: string[]; values?: string[] } {
    const defaultValue = this.property.defaultValue;
    if (isNonEmptyString(defaultValue)) {
      const data = [defaultValue];
      const option = this.findOptionById(defaultValue);
      const values = option ? [iStringParse(option.name, context.user?.locale)] : [];
      return { data, values };
    }
    return {};
  }

  override initializeCellModelOnUpdate(_context: UpdateRecordContext): CellModel {
    // 更新记录时, 不接受输入值, 不初始化
    throw new Error(`the type of field [${this.id}] does not support auto initialization of cell value: ${this.type}`);
  }

  override buildCellModel(options: BuildCellModelOptions): { data: string[]; values: string[] } {
    const { input, context } = options;
    if (!isStringArray(input)) {
      // 不是字符串数组, 不处理
      return { data: [], values: [] };
    }
    if (input.length === 0) {
      // 空数组, 不处理
      return { data: [], values: [] };
    }
    const inputOptions = this.getOrCreateOptions(Array.from(new Set(input)));
    const optionsIds = inputOptions.map((option) => option.id).filter((id) => id !== undefined);
    const values = inputOptions.map((option) => iStringParse(option.name, context.user?.locale));
    return { data: optionsIds, values };
  }

  override buildUpdateCellModel(options: UpdateCellModelOptions): { data: string[]; values: string[] } | undefined {
    const { input, context } = options;
    if (!isStringArray(input)) {
      // 不是字符串数组, 清空
      return { data: [], values: [] };
    }
    if (input.length === 0) {
      // 空数组, 清空
      return { data: [], values: [] };
    }
    // 去重
    const inputOptions = this.getOrCreateOptions(Array.from(new Set(input)));
    const optionsIds = inputOptions.map((option) => option.id).filter((id) => id !== undefined);
    const values = inputOptions.map((option) => iStringParse(option.name, context.user?.locale));
    return { data: optionsIds, values };
  }

  override convertToCellValue(value: string | string[]): string[] | null {
    const values = Array.isArray(value) ? value : [value];
    const optIds = values.flatMap((val) => convertStringToArray(val));
    return optIds.length === 1 ? optIds : null;
  }
}

export class MultiSelectFieldSO extends SelectFieldSO<DatabaseMultiSelectField> {
  override assertValueType(input: Exclude<CellValue, null>): boolean {
    // 只能是字符串数组, 并且允许多个元素
    return this.isCellValueValidStringArray(input, true);
  }

  override hasDefaultValue(): boolean {
    const defaultValue = this.property.defaultValue;
    if (Array.isArray(defaultValue)) {
      return defaultValue.length > 0;
    }
    return false;
  }

  override fillDefaultCellModel(context: CreateRecordContext): { data?: string[]; values?: string[] } {
    const defaultValue = this.property.defaultValue;
    if (isStringArray(defaultValue)) {
      const data = defaultValue;
      const values = data
        .map((id) => {
          const option = this.findOptionById(id);
          return option ? iStringParse(option.name, context.user?.locale) : undefined;
        })
        .filter((name) => name !== undefined);
      return { data, values };
    }
    return {};
  }

  override buildCellModel(options: BuildCellModelOptions): { data: string[]; values: string[] } {
    const { input, context } = options;
    if (!isStringArray(input)) {
      // 不是字符串数组, 不处理
      return { data: [], values: [] };
    }
    if (input.length === 0) {
      // 空数组, 不处理
      return { data: [], values: [] };
    }
    // 去重
    const inputOptions = this.getOrCreateOptions(Array.from(new Set(input)));
    const optionsIds = inputOptions.map((option) => option.id).filter((id) => id !== undefined);
    const values = inputOptions.map((option) => iStringParse(option.name, context.user?.locale));
    return { data: optionsIds, values };
  }

  override buildUpdateCellModel(options: UpdateCellModelOptions): { data: string[]; values: string[] } | undefined {
    const { input, context } = options;
    if (!isStringArray(input)) {
      // 不是字符串数组, 清空
      return { data: [], values: [] };
    }
    if (input.length === 0) {
      // 空数组, 清空
      return { data: [], values: [] };
    }
    // 去重
    const inputOptions = this.getOrCreateOptions(Array.from(new Set(input)));
    const optionsIds = inputOptions.map((option) => option.id).filter((id) => id !== undefined);
    const values = inputOptions.map((option) => iStringParse(option.name, context.user?.locale));
    return { data: optionsIds, values };
  }

  override convertToCellValue(value: string | string[]): string[] | null {
    const values = Array.isArray(value) ? value : [value];
    const optIds = values.flatMap((val) => convertStringToArray(val));
    return optIds.length > 0 ? optIds : null;
  }
}
