// eslint-disable-next-line max-classes-per-file
import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import Excel from 'exceljs';
import mime from 'mime-types';
import { isNullOrUndefined } from '@bika/domains/shared/shared';
import {
  DatabaseFieldConfigSelectOption,
  DatabaseMultiSelectField,
  DatabaseSingleSelectField,
  getRandomColor,
  CellValue,
} from '@bika/types/database/bo';
import { StandardCellValue, CONST_PREFIX_OPTION } from '@bika/types/database/vo';
import { iString, iStringParse } from '@bika/types/system';
import { AbstractCellValueConvertor } from './abstract-cellvalue-convertor';

abstract class BaseSelectCellValueConvertor<
  T extends DatabaseSingleSelectField | DatabaseMultiSelectField,
> extends AbstractCellValueConvertor<T> {
  public override cellValueToString(value: CellValue): string | null {
    return this.arrayValueToString(this.cellValueToArray(value));
  }

  public override cellValueToExcelCellValue(value: CellValue): Excel.CellValue {
    if (isNullOrUndefined(value)) {
      return null;
    }
    return this.cellValueToString(value);
  }

  public override cellValueToArray(value: CellValue): (number | iString)[] | null {
    if (!value) {
      return null;
    }
    // 必须是字符串数组, 非iString数组
    assert(Array.isArray(value) && value.every((val) => typeof val === 'string'));

    const arr: iString[] = [];
    (value as string[]).forEach((optionId) => {
      const option = this.findOptionById(optionId);
      if (option) {
        arr.push(option.name);
      } else {
        arr.push(optionId);
      }
    });
    return arr;
  }

  public override arrayValueToString(array: CellValue): string | null {
    if (!array) {
      return null;
    }
    assert(Array.isArray(array));
    const arr = array as Array<unknown>;
    return arr.length ? arr.join(', ') : null;
  }

  protected findOptionById(id: string): DatabaseFieldConfigSelectOption | null {
    const { options } = this.field.property;
    if (!options.length) {
      return null;
    }
    return options.find((option) => option.id === id) ?? null;
  }

  protected findOptionByName(name: string): DatabaseFieldConfigSelectOption | null {
    const { options } = this.field.property;
    if (!options.length) {
      return null;
    }
    return options.find((option) => option.name === name) ?? null;
  }

  protected findOptionNameById(id: string): string | null {
    const { options } = this.field.property;
    if (!options.length) {
      return null;
    }
    const option = options.find((opt) => opt.id === id);
    if (!option) {
      return null;
    }
    return iStringParse(option.name);
  }

  protected createOptionId(): string {
    return generateNanoID(CONST_PREFIX_OPTION);
  }

  protected getOrCreateOptionByName(name: string): DatabaseFieldConfigSelectOption {
    const { options } = this.field.property;
    const option = this.findOptionByName(name);
    if (option) {
      return option;
    }
    const newOption: DatabaseFieldConfigSelectOption = {
      id: this.createOptionId(),
      color: getRandomColor(),
      name,
    };
    options.push(newOption);
    return newOption;
  }

  protected optionToCellModel(option: DatabaseFieldConfigSelectOption): { data: string[]; values: string[] } {
    return { data: option.id ? [option.id] : [], values: option.name ? [iStringParse(option.name)] : [] };
  }

  protected optionsToCellModel(options: DatabaseFieldConfigSelectOption[]): { data: string[]; values: string[] } {
    // 去重
    const optionIds = options.map((option) => option.id).filter((id) => id !== undefined);
    const values = options.map((option) => iStringParse(option.name));
    return { data: Array.from(new Set(optionIds)), values: Array.from(new Set(values)) };
  }

  protected stringToOptions(str: string, multi: boolean = false): DatabaseFieldConfigSelectOption[] {
    if (multi) {
      // 分割并过滤空值（支持逗号和空格分隔）
      const parts = str.split(/[,\s]+/).filter((part) => part.trim() !== '');
      return parts.map((name) => this.getOrCreateOptionByName(name.trim()));
    }
    // 统一截取前20个字符（注意处理多字节字符）
    const truncated = str.slice(0, 20);
    // 单元素模式验证长度
    const option = this.getOrCreateOptionByName(truncated.trim());
    return [option];
  }
}

export class SingleSelectCellValueConvertor extends BaseSelectCellValueConvertor<DatabaseSingleSelectField> {
  /**
   * @returns 锁定返回值为字符串数组, 并且只能有一个
   */
  override stdCellValueToCellModel(stdCellValue: StandardCellValue): { data?: string[]; values?: string[] } {
    const { type, data, value } = stdCellValue;
    // const { options, defaultValue } = this.field.property;
    if (isNullOrUndefined(data) || isNullOrUndefined(value)) {
      // 无数据, 直接返回空
      return { data: [], values: [] };
    }
    if (Array.isArray(data) && data.length === 0) {
      // 数组空的, 直接返回空
      return { data: [], values: [] };
    }
    // 多选切换单选, 选项不变, 单元格值只保留首个, 直接截取即可
    if (type === 'MULTI_SELECT') {
      // 原选项ID
      const newData = data.slice(0, 1);
      // 使用选项里面的值, 可能在转换时候改掉了原来的名称, 同步转换过来
      const newValue = newData.length > 0 ? this.findOptionNameById(newData[0]) : null;
      return { data: newValue ? newData : [], values: newValue ? [newValue] : [] };
    }
    // 附件取(附件名.{文件类型})拼接起来作为一个选项
    if (type === 'ATTACHMENT') {
      const newValue = data.map((attach) => `${attach.name}.${mime.extension(attach.mimeType)}`).join(', ');
      const newOption = this.getOrCreateOptionByName(newValue);
      return this.optionToCellModel(newOption);
    }
    // 关联记录转选项
    if (type === 'LINK') {
      // 关联记录是数组, 只处理value值, 不用理会data
      const newValue = value.join(', '); // 相当于新的 option name
      // 看是否存在, 复用
      const newOption = this.getOrCreateOptionByName(newValue);
      return this.optionToCellModel(newOption);
    }
    // 引用字段
    if (type === 'LOOKUP') {
      // 取value
      const newValue = Array.isArray(value)
        ? value
            .filter((v) => v !== null)
            .map((v) => iStringParse(v))
            .join(', ')
        : iStringParse(value);
      const newOption = this.getOrCreateOptionByName(newValue);
      return this.optionToCellModel(newOption);
    }
    // 公式字段
    if (type === 'FORMULA') {
      // 直接取解析完后的value来处理
      const newValue = Array.isArray(value) ? value.join(', ') : value;
      const newOption = this.getOrCreateOptionByName(newValue);
      return this.optionToCellModel(newOption);
    }
    // 协作成员/创建人/修改人, 直接取昵称
    if (type === 'MEMBER' || type === 'CREATED_BY' || type === 'MODIFIED_BY') {
      // 只处理value, data存的成员ID不理会
      // 直接取昵称
      const newValue = typeof value === 'string' ? iStringParse(value) : value.map((v) => iStringParse(v)).join(', ');
      // 看是否存在, 复用
      const newOption = this.getOrCreateOptionByName(newValue);
      return this.optionToCellModel(newOption);
    }
    // 数字类型, 转选项
    if (type === 'NUMBER' || type === 'CURRENCY' || type === 'PERCENT' || type === 'RATING') {
      // value才是字符串, 直接赋予值
      const newOption = this.getOrCreateOptionByName(value);
      return this.optionToCellModel(newOption);
    }
    // 字符串类型, 转选项
    if (
      type === 'SINGLE_TEXT' ||
      type === 'LONG_TEXT' ||
      type === 'URL' ||
      type === 'EMAIL' ||
      type === 'PHONE' ||
      type === 'CHECKBOX'
    ) {
      // 直接取字符串
      const newOption = this.stringToOptions(value);
      return this.optionsToCellModel(newOption);
    }
    // 其他类型不支持转换了, 需要再加
    return { data: [], values: [] };
  }

  public override excelCellToCellValue(cell: Excel.Cell): CellValue {
    const { value, text } = cell;
    if (isNullOrUndefined(value)) {
      return null;
    }

    if (text.length === 0) {
      return null;
    }

    return [cell.text];
  }
}

export class MultiSelectCellValueConvertor extends BaseSelectCellValueConvertor<DatabaseMultiSelectField> {
  /**
   * @returns 锁定返回值为字符串数组, 允许多个
   */
  override stdCellValueToCellModel(stdCellValue: StandardCellValue): { data: string[]; values?: string[] } {
    const { type, data, value } = stdCellValue;
    // const { options, defaultValue } = this.field.property;
    if (isNullOrUndefined(data) || isNullOrUndefined(value)) {
      // 无数据, 直接返回空
      return { data: [], values: [] };
    }
    if (Array.isArray(data) && data.length === 0) {
      // 数组空的, 直接返回空
      return { data: [], values: [] };
    }
    // 单选切换多选, 选项不变, 单元格值直接复制过来
    if (type === 'SINGLE_SELECT') {
      // 原选项ID
      const newData = data.slice(0, 1);
      // 使用选项里面的值, 可能在转换时候改掉了原来的名称, 同步转换过来
      const newValue = newData.length > 0 ? this.findOptionNameById(newData[0]) : null;
      return { data: newValue ? newData : [], values: newValue ? [newValue] : [] };
    }
    // 附件保留成数组([附件名.{文件类型}, ....])
    if (type === 'ATTACHMENT') {
      const newValue = data.map((attach) => `${attach.name}.${mime.extension(attach.mimeType)}`);
      const newOptions = newValue.map((v) => this.getOrCreateOptionByName(v));
      return this.optionsToCellModel(newOptions);
    }
    // 关联记录转选项, 每个值作为一个选项
    if (type === 'LINK') {
      // 关联记录是数组, 只处理value值, 不用理会data
      const newOptions = value.map((v) => this.getOrCreateOptionByName(v));
      return this.optionsToCellModel(newOptions);
    }
    // 引用字段
    if (type === 'LOOKUP') {
      // data/value一样的, 保险起见, 使用计算完的值value
      if (Array.isArray(value)) {
        const newOptions = value.filter((v) => v !== null).map((v) => this.getOrCreateOptionByName(iStringParse(v)));
        return this.optionsToCellModel(newOptions);
      }
      const newValue = iStringParse(value);
      const newOption = this.getOrCreateOptionByName(newValue);
      return this.optionToCellModel(newOption);
    }
    // 公式字段
    if (type === 'FORMULA') {
      // 直接取解析完后的value来处理
      if (Array.isArray(value)) {
        const newOptions = value.map((v) => this.getOrCreateOptionByName(iStringParse(v)));
        return this.optionsToCellModel(newOptions);
      }
      const newOption = this.getOrCreateOptionByName(value);
      return this.optionToCellModel(newOption);
    }
    // 协作成员/创建人/修改人, 直接取昵称
    if (type === 'MEMBER' || type === 'CREATED_BY' || type === 'MODIFIED_BY') {
      // 只处理value, data存的成员ID不理会
      if (Array.isArray(value)) {
        // 多个, 应该是成员
        const newOptions = value.map((v) => this.getOrCreateOptionByName(iStringParse(v)));
        return this.optionsToCellModel(newOptions);
      }
      // 单个, 应该是创建人/修改人类型
      const newOption = this.getOrCreateOptionByName(value);
      return this.optionToCellModel(newOption);
    }
    // 数字类型, 转选项(自动编号不转, 选项无限扩大)
    if (type === 'NUMBER' || type === 'CURRENCY' || type === 'PERCENT' || type === 'RATING') {
      // value才是字符串, 直接赋予值
      const newOption = this.getOrCreateOptionByName(value);
      return this.optionToCellModel(newOption);
    }
    // 字符串类型, 转选项
    if (
      type === 'SINGLE_TEXT' ||
      type === 'LONG_TEXT' ||
      type === 'URL' ||
      type === 'EMAIL' ||
      type === 'PHONE' ||
      type === 'CHECKBOX'
    ) {
      // 直接取字符串, 里面处理字符串分隔
      const newOptions = this.stringToOptions(value, true);
      return this.optionsToCellModel(newOptions);
    }
    // 其他类型不支持转换了, 需要再加
    return { data: [], values: [] };
  }

  public override excelCellToCellValue(cell: Excel.Cell): CellValue {
    const { value, text } = cell;
    if (isNullOrUndefined(value)) {
      return null;
    }

    if (text.length === 0) {
      return null;
    }

    return cell.text.split(',').map((v) => v.trim());
  }
}
