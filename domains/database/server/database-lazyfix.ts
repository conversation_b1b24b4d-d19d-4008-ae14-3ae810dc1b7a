import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import { db } from '@bika/server-orm';
import { BaseSelectFieldPropertySchema, MultiSelectFieldProperty } from '@bika/types/database/bo';
import { CONST_PREFIX_OPTION } from '@bika/types/database/vo';
import { DatabaseFieldModel } from './fields/types';

export class DatabaseLazyFix {
  public static async fixFields(fieldPOs: DatabaseFieldModel[]): Promise<DatabaseFieldModel[]> {
    if (fieldPOs.length === 0) {
      return fieldPOs;
    }
    return Promise.all(fieldPOs.map(DatabaseLazyFix.fixField));
  }

  public static async fixField(fieldPO: DatabaseFieldModel): Promise<DatabaseFieldModel> {
    const fieldPO2 = DatabaseLazyFix.fixMultiSelectFieldInvalidDefaultValueType(fieldPO);
    return DatabaseLazyFix.fixSelectFieldMissingOptionId(fieldPO2);
  }

  /**
   * 旧的 MultiSelectField 默认值类型错误，需要修复
   * 目前仅在返回时修复，不会回写数据库
   *
   * 旧类型：string
   * 新类型：string[]
   */
  private static fixMultiSelectFieldInvalidDefaultValueType(fieldPO: DatabaseFieldModel): DatabaseFieldModel {
    if (fieldPO.type !== 'MULTI_SELECT') {
      return fieldPO;
    }

    const property = fieldPO.property as MultiSelectFieldProperty;
    if (typeof property.defaultValue === 'string') {
      return {
        ...fieldPO,
        property: {
          ...property,
          defaultValue: [],
        },
      };
    }

    return fieldPO;
  }

  /**
   * 部分 SelectField 缺少 option id，需要修复
   */
  private static async fixSelectFieldMissingOptionId(fieldPO: DatabaseFieldModel): Promise<DatabaseFieldModel> {
    if (fieldPO.type !== 'SINGLE_SELECT' && fieldPO.type !== 'MULTI_SELECT') {
      return fieldPO;
    }

    // 快速路径：检查是否缺少 option id
    const selectFieldProperty = BaseSelectFieldPropertySchema.parse(fieldPO.property);
    const hasMissingOptionId = selectFieldProperty.options.some((option) => !option.id);
    if (!hasMissingOptionId) {
      return fieldPO;
    }

    // 尝试回写 property
    //
    // 成功则返回 fixedField，否则返回 null
    // Throw Error 表示无需再重试（网络异常或无法修复）
    const tryFix = async (): Promise<DatabaseFieldModel | null> =>
      db.prisma.$transaction(async (prisma) => {
        const oldField = await prisma.databaseField.findUnique({
          where: {
            id: fieldPO.id,
          },
        });

        // 无需修复，字段已被删除
        if (!oldField) {
          throw new Error(`Field ${fieldPO.id} not found`);
        }

        const property = BaseSelectFieldPropertySchema.parse(oldField.property);
        let missingOptionId = false;

        // 生成 option id，如果没有的话
        for (const option of property.options) {
          if (!option.id) {
            missingOptionId = true;
            option.id = generateNanoID(CONST_PREFIX_OPTION);
          }
        }

        // 无需修复（已在其它线程修复）
        if (!missingOptionId) {
          return oldField;
        }

        // 回写 property
        const { count } = await prisma.databaseField.updateMany({
          where: {
            id: fieldPO.id,
            revision: fieldPO.revision,
          },
          data: {
            property,
            revision: {
              increment: 1,
            },
          },
        });
        assert(count <= 1, `Failed to update field ${fieldPO.id}`);

        // Success
        if (count === 1) {
          return {
            ...oldField,
            property,
            revision: oldField.revision + 1,
          };
        }

        // Retry
        return null;
      });

    // 乐观锁: 尝试修复，最多重试 3 次
    for (let retry = 0; retry < 3; retry++) {
      const fixedField = await tryFix();

      if (fixedField) {
        return fixedField;
      }
    }

    throw new Error(`Failed to fix select field ID after 3 retries: ${fieldPO.id}`);
  }
}
