import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { isNullOrUndefined } from '@bika/domains/shared/shared';
import { DatabaseLinkField, DatabaseRecord, CellValue } from '@bika/types/database/bo';
import { CellRenderVO, CellValueVO, RecordRenderVO, CONST_PREFIX_RECORD } from '@bika/types/database/vo';
import { IBOProcessor, BORenderOpts } from '@bika/types/node/vo';
import { DateTimeFieldProperty, DateTimeSO } from '@bika/types/system';
import { DatabaseBOProcessor } from '../database-bo-processor';
import { FieldBOProcessor } from '../fields/bo/types';

export class RecordBOProcessor implements IBOProcessor<DatabaseRecord> {
  private _record: DatabaseRecord;

  private _databaseId: string;

  constructor(record: DatabaseRecord, databaseId: string) {
    this._record = _.cloneDeep(record);
    if (!this._record.id) {
      this._record.id = generateNanoID(CONST_PREFIX_RECORD);
    }
    this._databaseId = databaseId;
  }

  get bo(): DatabaseRecord {
    return this._record;
  }

  get id(): string {
    return this.bo.id!;
  }

  get templateId(): string {
    return this.bo.templateId!;
  }

  get databaseId(): string {
    return this._databaseId;
  }

  getData(fieldId?: string, fieldTemplateId?: string): CellValue {
    const fieldKey = fieldId || fieldTemplateId;
    if (!fieldKey) {
      return null;
    }
    if (fieldId && fieldTemplateId) {
      const newData = this.bo.data?.[fieldId] || this.bo.data?.[fieldTemplateId];
      return newData ?? null;
    }
    return this.bo.data?.[fieldKey] ?? null;
  }

  getValue(fieldId?: string, fieldTemplateId?: string): CellValue {
    const fieldKey = fieldId || fieldTemplateId;
    if (!fieldKey) {
      return null;
    }
    if (fieldId && fieldTemplateId) {
      const value = this.bo.values?.[fieldId] || this.bo.values?.[fieldTemplateId];
      return value ?? null;
    }
    return this.bo.values?.[fieldKey] ?? null;
  }

  getDatabase(opts?: BORenderOpts): DatabaseBOProcessor {
    const { getProcessor } = opts || {};
    const database = getProcessor?.(this._databaseId);
    if (!database) {
      throw new Error('RecordBOProcessor:database not found');
    }
    return database as DatabaseBOProcessor;
  }

  toVO<V = RecordRenderVO>(opts?: BORenderOpts): V {
    const datas = this.bo.data;
    if (!datas || Object.keys(datas).length === 0) {
      return { id: this.id, databaseId: this._databaseId, revision: 0, cells: {} } as V;
    }
    const cells: Record<string, CellRenderVO> = {};
    const columns = this.getDatabase(opts).fields;
    for (const column of columns) {
      const field = column.getBO();
      const value = this.getValue(field.id, field.templateId);
      const data = this.getData(field.id, field.templateId);
      const fieldId = column.getId();
      cells[fieldId] = this.toCellVO(column, opts)(data, value);
    }
    return { id: this.id, databaseId: this.databaseId, revision: 0, cells } as V;
  }

  toCellVO(field: FieldBOProcessor, opts?: BORenderOpts): (data: CellValue, value: CellValue) => CellRenderVO {
    return (data: CellValue, value: CellValue) => {
      const fieldId = field.getId();
      const fieldBO = field.getBO();
      if (fieldBO.type === 'LINK') {
        return this.toLinkCellVO(field, opts)(data, value);
      }
      if (fieldBO.type === 'CREATED_TIME' || fieldBO.type === 'MODIFIED_TIME' || fieldBO.type === 'DATETIME') {
        return this.toDateTimeCellVO(field, opts)(data);
      }
      const cellVO: CellRenderVO = {
        id: fieldId,
        value: value as CellValueVO,
        data: data as CellValue,
      };
      return cellVO;
    };
  }

  toLinkCellVO(field: FieldBOProcessor, opts?: BORenderOpts): (data: CellValue, value: CellValue) => CellRenderVO {
    const { getProcessor } = opts || {};
    const fieldBO = field.getBO() as DatabaseLinkField;
    return (data: CellValue, value: CellValue) => {
      const foreignDatabaseId = fieldBO.property.foreignDatabaseId || fieldBO.property.foreignDatabaseTemplateId;
      const linkedData: string[] = [];
      if (foreignDatabaseId) {
        const database = getProcessor?.(foreignDatabaseId) as DatabaseBOProcessor;
        if (database && Array.isArray(data)) {
          for (const item of data) {
            const linkRecord = database.getRecord(String(item));
            if (linkRecord) {
              linkedData.push(linkRecord.id);
            }
          }
        }
      }
      return {
        fieldType: 'LINK',
        id: field.getId(),
        value: value as CellValueVO,
        data: linkedData as CellValue,
      };
    };
  }

  toDateTimeCellVO(field: FieldBOProcessor, opts?: BORenderOpts): (data: CellValue) => CellRenderVO {
    return (data: CellValue) => {
      let cellData = data as CellValue;
      const fieldId = field.getId();
      const fieldBO = field.getBO();
      if (fieldBO.type === 'CREATED_TIME') {
        cellData = new Date().toISOString();
      }
      // console.log('field type', fieldBO.type);
      // console.log('cellData', cellData);
      if (isNullOrUndefined(cellData)) {
        return {
          id: fieldId,
          value: null,
          data: cellData,
        };
      }
      const property = _.cloneDeep(fieldBO.property) as DateTimeFieldProperty;
      if (!property.timeZone || property.timeZone === 'AUTO') {
        property.timeZone = opts?.timeZone;
      }
      const datetimeSO = DateTimeSO.fromISOString(cellData as string);
      const value = datetimeSO.format(property);
      const cellVO: CellRenderVO = {
        id: fieldId,
        value: value as CellValueVO,
        data: cellData,
      };
      return cellVO;
    };
  }
}
