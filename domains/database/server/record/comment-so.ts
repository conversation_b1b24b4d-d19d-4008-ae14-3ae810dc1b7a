import { generateNanoID } from 'basenext/utils/nano-id';
import { ServerError, errors } from '@bika/contents/config/server/error';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { CommentModel, db, mongoose } from '@bika/server-orm';
import { RecordCommentVO } from '@bika/types/database/vo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';

type CommentRelationType = 'RECORD' | 'MISSION';

export class CommentSO {
  private readonly _model: CommentModel;

  private constructor(model: CommentModel) {
    this._model = model;
  }

  get id() {
    return this._model.id;
  }

  get model() {
    return this._model;
  }

  static async init(id: string): Promise<CommentSO> {
    const commentPO = await db.mongo.comment.findOne({
      id,
    });
    if (!commentPO) {
      throw new ServerError(errors.database.record_comment_not_found);
    }
    return new CommentSO(commentPO);
  }

  static async create(
    userId: string,
    relationType: CommentRelationType,
    relationId: string,
    content: string,
    spaceId: string,
    unitMemberId?: string,
  ): Promise<CommentSO> {
    const newComment: CommentModel = {
      id: generateNanoID('cmt'),
      content,
      unitId: unitMemberId,
      relationType,
      relationId,
      spaceId,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
      updatedBy: userId,
    };
    const commentPO = await db.mongo.comment.create(newComment);
    const commentSO = new CommentSO(commentPO);
    if (relationType === 'RECORD') {
      EventSO.database.onRecordCommentCreated(commentSO);
    }
    return commentSO;
  }

  async delete(userId: string): Promise<void> {
    // const comment = await this.init(commentId);

    // 只有评论创建者可以删除评论
    if (this.model.createdBy !== userId) {
      // throw new TRPCError({ code: 'FORBIDDEN', message: 'Only comment creator can delete the comment' });
      throw new ServerError(errors.database.cannot_delete_comment_except_creator);
    }

    await db.mongo.comment.deleteOne({ id: this.id });

    // 触发评论删除事件
    if (this.model.relationType === 'RECORD') {
      EventSO.database.onRecordCommentDeleted(this);
    }
  }

  private static async getComments(
    relationType: CommentRelationType,
    relationId: string,
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: CommentSO[] }> {
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    const filter: mongoose.FilterQuery<CommentModel> = {
      relationType,
      relationId,
    };
    const [commentsPOs, total] = await Promise.all([
      db.mongo.comment
        .find(filter)
        .sort({ _id: -1 })
        .skip(pageNo > 0 ? (pageNo - 1) * pageSize : 0)
        .limit(pageSize),
      db.mongo.comment.countDocuments(filter),
    ]);
    return {
      pagination: { pageNo, pageSize, total },
      list: commentsPOs.map((po) => new CommentSO(po)),
    };
  }

  static async getRecordComments(
    q: { recordId: string },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: CommentSO[] }> {
    const { recordId } = q;
    return this.getComments('RECORD', recordId, pagination);
  }

  public static async getCommentCountByRecordIds(recordIds: string[]): Promise<Record<string, number>> {
    const comments = await db.mongo.comment.aggregate([
      {
        $match: {
          relationType: 'RECORD',
          relationId: { $in: recordIds },
        },
      },
      {
        $group: {
          _id: '$relationId',
          count: { $sum: 1 },
        },
      },
    ]);
    return comments.reduce(
      (acc, cur) => {
        acc[cur._id] = cur.count;
        return acc;
      },
      {} as Record<string, number>,
    );
  }

  async getMember() {
    if (!this.model.unitId) return null;
    const member = await MemberSO.init(this.model.unitId);
    return member.toVO();
  }

  async getUser() {
    if (!this.model.createdBy) return null;
    const user = await UserSO.init(this.model.createdBy);
    return user.toVO();
  }

  async toActivityVO(): Promise<RecordCommentVO> {
    const member = await this.getMember();
    const user = await this.getUser();
    return {
      id: this.model.id,
      content: this.model.content,
      createAt: this.model.createdAt.toISOString(),
      member,
      user: user!,
    };
  }
}
