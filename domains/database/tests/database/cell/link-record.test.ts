import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { test, describe, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DatabaseLinkField } from '@bika/types/database/bo';
import { TemplateRepoSO } from '../../../../template/server';
import { DatabaseSO } from '../../../server/database-so';
import { FieldSO } from '../../../server/fields/field-so';

describe('link record test', () => {
  test('init link record by template', async () => {
    // create user and space
    const { user, space } = await MockContext.initUserContext();
    const folder = await space.installTemplateById(user, 'simple-applicant-tracker');
    const children = await folder.getChildren();
    for (const child of children) {
      const databaseSO = await child.toResourceSO<DatabaseSO>();
      const records = await databaseSO.getRecordsAsPage();
      const fields = databaseSO.getFields();
      const fieldMap: { [key: string]: FieldSO } = {};
      const fieldIds = fields.map((field) => {
        fieldMap[field.id] = field;
        return field.id;
      });
      expect(records.length).toBe(3);

      const record = records[0];
      const recordVO = await record.toRenderVO();
      for (const [key, value] of Object.entries(recordVO.cells)) {
        // 检查列ID是否匹配
        expect(fieldIds.includes(key)).toBeTruthy();
        // 检查关联项是否存在
        if (fieldMap[key].type === 'LINK') {
          const bo = fieldMap[key].toBO();
          const foreignDatabaseId = (bo as DatabaseLinkField).property.foreignDatabaseId;
          if (!foreignDatabaseId) {
            throw new Error('foreignDatabaseId is required');
          }
          const foreignDatabase = await DatabaseSO.init(foreignDatabaseId);
          const foreignRecords = await foreignDatabase.getRecordsAsPage();
          const foreignRecordIds = foreignRecords.map((i) => i.id);
          expect(_.includes(foreignRecordIds, (value.data as string[])[0])).toBeTruthy();
        }
      }
    }
  });

  test('init link record publish to template', async () => {
    // create user and space
    const { user, space } = await MockContext.initUserContext();
    // 安装
    const folder = await space.installTemplateById(user, 'simple-applicant-tracker');
    // 再次发布
    const templateId = await folder.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        templateId: generateNanoID('tps'),
        category: ['sales'],
        version: '1.0',
        visibility: 'PUBLIC',
        detach: true,
        withRecords: true,
      },
    });
    const templateRepo = await TemplateRepoSO.init('simple-applicant-tracker');
    const template = templateRepo.currentTemplate;
    const database = _.get(template.resources, [0]);

    const newTemplateRepo = await TemplateRepoSO.init(templateId);
    const newDatabase = _.get(newTemplateRepo.currentTemplate.resources, [0]);
    // 因为什么都没有更改，所以应该都保持一致，包括templateId
    expect(database).toStrictEqual(newDatabase);
  });

  test('init link record publish to template and install new one', async () => {
    // create user and space
    const { user, space } = await MockContext.initUserContext();
    // 安装
    const originalFolder = await space.installTemplateById(user, 'simple-applicant-tracker');
    // 再次发布
    const templateId = await originalFolder.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        templateId: generateNanoID('tps'),
        category: ['sales'],
        version: '1.0',
        visibility: 'PUBLIC',
        detach: true,
        withRecords: true,
      },
    });
    const folder = await space.installTemplateById(user, templateId);
    const children = await folder.getChildren();
    for (const child of children) {
      const databaseSO = await child.toResourceSO<DatabaseSO>();
      const records = await databaseSO.getRecordsAsPage();
      const fields = databaseSO.getFields();
      const fieldMap: { [key: string]: FieldSO } = {};
      const fieldIds = fields.map((field) => {
        fieldMap[field.id] = field;
        return field.id;
      });
      expect(records.length).toBe(3);

      const record = records[0];
      const recordVO = await record.toRenderVO();
      for (const [key, value] of Object.entries(recordVO.cells)) {
        // 检查列ID是否匹配
        expect(fieldIds.includes(key)).toBeTruthy();
        // 检查关联项是否存在
        if (fieldMap[key].type === 'LINK') {
          const bo = fieldMap[key].toBO();
          const foreignDatabaseId = (bo as DatabaseLinkField).property.foreignDatabaseId;
          const foreignDatabase = await DatabaseSO.init(foreignDatabaseId!);
          const foreignRecords = await foreignDatabase.getRecordsAsPage();
          const foreignRecordIds = foreignRecords.map((i) => i.id);
          expect(_.includes(foreignRecordIds, (value.data as string[])[0])).toBeTruthy();
        }
      }
    }
  });
});
