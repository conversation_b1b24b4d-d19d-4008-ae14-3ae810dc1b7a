import { getAppEnv } from 'sharelib/app-env';
import { RowHeight } from '@bika/types/system/remote-storage';

export const AG_CELL_ITEMS_HEIGHT = 23;
const gapSize = 4;

export const getCellLineHeight = (lines: number): number =>
  (AG_CELL_ITEMS_HEIGHT * lines + gapSize * (lines - 1)) / lines;

const narwowLine = 1;
const mediumnLine = 2;
const tallLine = 4;
const extraTallLine = 6;

const verticalPadding = 8;

// Calculate row height based on number of lines
const calculateRowHeight = (lines: number): number =>
  verticalPadding + AG_CELL_ITEMS_HEIGHT * lines + gapSize * (lines - 1);

export const rowHeightNumberMap: Record<RowHeight, number> = {
  narrow: narwowLine, // 显示一行
  medium: mediumnLine, // 显示两行
  tall: tallLine, // 显示四行
  extra_tall: extraTallLine, // 显示六行
};

export const rowHeightMap: Record<RowHeight, number> = {
  narrow: calculateRowHeight(narwowLine), // 显示一行
  medium: calculateRowHeight(mediumnLine), // 显示两行
  tall: calculateRowHeight(tallLine), // 显示四行
  extra_tall: calculateRowHeight(extraTallLine), // 显示六行
};

export const AgGridConfig = {
  rowHeight: rowHeightMap.narrow,
  groupDefaultExpanded: 3,
  cellWidth: 200,
  enableFillHandle: true,
  readOnlyEdit: false,
  debounceVerticalScrollbar: true,
  rowBuffer: 20,
  cachedBlockSize: 100,
  suppressDragLeaveHidesColumns: true,
  paginationPageSizeSelector:
    getAppEnv() === 'PRODUCTION'
      ? [20, 50, 100, 500, 1000]
      : [20, 50, 100, 500, 1000, 5000, 10000, 20000, 50000, 100000, 200000, 300000, 400000],
};
