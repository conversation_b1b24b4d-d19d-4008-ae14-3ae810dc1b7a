import type { <PERSON>rid<PERSON><PERSON> } from '@ag-grid-community/core';
import { useSnackbar } from 'notistack';
import { useEffect, useCallback, useMemo } from 'react';
import type { TRPCOriginClient } from '@bika/api-caller';
import { useTRPC } from '@bika/api-caller/context';
import { ILocaleContext, useLocale } from '@bika/contents/i18n';
import type { RecordRenderVO } from '@bika/types/database/vo';
import { getAppEnv } from 'sharelib/app-env';
import {
  type SSEEvent,
  SSEEventNameSchema,
  SSERowEvent,
  SSEEventDatabaseImportProgressSchema,
  SSEEventDatabaseImportCompleteSchema,
  SSEEventDatabaseImportFailureSchema,
} from '@bika/types/user/bo';
import { useGlobalContext } from '@bika/types/website/context';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import { useBGridCollaborationStore, useSnackBar, closeSnackbar, ISnackbarProps } from '@bika/ui/snackbar';
import { CollaborationStatus } from '../types';
import { Ctx, GridAction, handleCollaborationEvent } from './useBgridCollaborationStatus';
import { convertToString } from '../utils';

const idImportProgressing = 'import-progress';

export const useBGridCollaborationEffect = (ctx: Ctx, api?: GridApi<RecordRenderVO> | null) => {
  const { status, setStatus, setImportPercentage, reset, editable, setEditable } = useBGridCollaborationStore();

  const { toast } = useSnackBar();

  const snackbar = useSnackbar();

  const { t } = useLocale();
  const { authContext: auth } = useGlobalContext();

  const userId = auth.me?.user?.id;

  const frameworkContext = useUIFrameworkContext();

  const appEnv = getAppEnv();
  const isIntergration = ['INTEGRATION'].includes(appEnv);

  const trpc = useTRPC();

  const isLiveEventEnabled = Boolean(frameworkContext.searchParams?.get('live')) || isIntergration;

  useEffect(() => {
    const handler = (event: SSEEvent) => {
      console.log('[datasheet import event]', event);
      if (event.name === SSEEventNameSchema.enum['database-import-progress']) {
        const data = SSEEventDatabaseImportProgressSchema.safeParse(event);
        if (data.success) {
          const { databaseId, jobId, totalRecords, processedRecord } = data.data;
          if (databaseId !== ctx.databaseId) {
            return;
          }

          if (status?.type === 'database-import-progress') {
            setImportPercentage(processedRecord / totalRecords);
            return;
          }

          setEditable(false);

          setStatus({
            type: 'database-import-progress',
            databaseId,
            jobId,
            totalRecords,
            processedRecord,
          } as CollaborationStatus);

          const percent = `${((processedRecord / totalRecords) * 100).toFixed(0)}%`;

          const importProgressText = t('editor.data_import_process', {
            Percent: percent,
          });
          toast(importProgressText, {
            variant: 'progress',
            persist: true,
            preventDuplicate: true,
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
            id: idImportProgressing,
            action: {
              text: t('editor.data_import_process', {
                Percent: percent,
              }),
            },
          });
        }
      }

      if (`${event.name}` === 'database-import-failure') {
        const data = SSEEventDatabaseImportFailureSchema.safeParse(event);
        if (data.success) {
          const { databaseId, reason, jobId, totalRecords, processedRecord } = data.data;
          if (databaseId !== ctx.databaseId) {
            return;
          }

          setEditable(true);

          closeSnackbar(idImportProgressing);
          const width = document?.querySelector('#sidebar')?.clientWidth;

          toast(
            t('resource.error_import_excel', {
              message: reason,
            }),
            {
              // persist: true,
              marginLeft: width,
              variant: 'error',
              id: 'import-complete',
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'left',
              },
            },
          );
        }
      }

      if (event.name === SSEEventNameSchema.enum['database-import-complete']) {
        const data = SSEEventDatabaseImportCompleteSchema.safeParse(event);
        if (data.success) {
          const { databaseId, jobId, totalRecords, processedRecord, operator } = data.data;
          if (databaseId !== ctx.databaseId) {
            return;
          }

          api?.refreshServerSide({
            purge: true,
          });

          setStatus({
            type: 'database-import-complete',
            databaseId,
            jobId,
            totalRecords,
            processedRecord,
            operator,
          } as CollaborationStatus);
          setEditable(true);

          closeSnackbar(idImportProgressing);
          const width = document?.querySelector('#sidebar')?.clientWidth;

          setTimeout(() => {
            toast(
              convertToString(
                t('editor.excel_import_success', {
                  Count: processedRecord,
                }),
              ),
              {
                // persist: operator === userId,
                marginLeft: width,
                variant: 'finish',
                id: 'import-complete',
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
              },
            );
          });
        }
      }
    };

    auth.registerSseEventsHandler(
      [SSEEventNameSchema.enum['database-import-complete'], SSEEventNameSchema.enum['database-import-progress']],
      handler,
    );
    return () => {
      auth.unregisterSseHandler(SSEEventNameSchema.enum['database-import-complete']);
      auth.unregisterSseHandler(SSEEventNameSchema.enum['database-import-progress']);
    };
  }, [auth, status, setStatus, setEditable, ctx.databaseId, snackbar, t, toast, setImportPercentage]);

  const handleSideEffect = useCallback(
    async (event: GridAction) => {
      console.log('handleSideEffect', event);
      const effect =
        event.type === 'auto_update'
          ? (event.effect as (
              api: GridApi<RecordRenderVO>,
              toast: ISnackbarProps['toast'],
              t: ILocaleContext['t'],
              trpc: TRPCOriginClient,
            ) => Promise<void>)
          : null;

      switch (event.type) {
        case 'snackbar': {
          toast(event?.message ?? t.resource.content_changed_warning, {
            id: event?.id ?? 'data-has-changed',
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
            preventDuplicate: event.id === 'data-has-changed' ? true : event.preventDuplicate,
            action: {
              onClick: () => {
                api?.refreshServerSide({
                  purge: false,
                });
              },
            },
            persist: true,
            variant: event?.variant ?? 'refresh',
          });
          break;
        }
        case 'drop_events':
          console.log('drop_events');
          break;
        case 'auto_update':
          if (effect && api) await effect(api, toast, t, trpc);
          break;
        case 'refresh_server_side':
          console.log('refresh_server_side');
          api?.refreshServerSide({
            purge: event.purge,
          });
          break;
        default:
          console.warn('Unhandled side effect type:', event);
          break;
      }
    },
    [api, toast, trpc],
  );

  useEffect(() => {
    auth.registerSseEventsHandler(
      [
        SSEEventNameSchema.enum['rows-created'],
        SSEEventNameSchema.enum['rows-updated'],
        SSEEventNameSchema.enum['rows-deleted'],
      ],
      (event) => {
        if (!isLiveEventEnabled) {
          return;
        }
        const sideEffect = handleCollaborationEvent(event as SSERowEvent, ctx);
        handleSideEffect(sideEffect);
      },
    );
    return () => {
      auth.unregisterSseEventsHandler([
        SSEEventNameSchema.enum['rows-created'],
        SSEEventNameSchema.enum['rows-updated'],
        SSEEventNameSchema.enum['rows-deleted'],
      ]);
    };
  }, [auth, ctx, handleSideEffect, isLiveEventEnabled]);

  return useMemo(
    () => ({
      status,
      editable,
      reset,
    }),
    [status, reset, editable],
  );
};
