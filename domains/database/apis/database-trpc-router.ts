import { z } from 'zod';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, publicProcedure, router } from '@bika/server-orm/trpc';
import {
  DatabaseInfoDTOSchema,
  RecordDetailListDTOSchema,
  FieldCreateDTOSchema,
  FieldGetDTOSchema,
  FieldUpdateDTOSchema,
  FieldDeleteDTOSchema,
  InfiniteRecordListDTOSchema,
  RecordDetailDTOSchema,
  RecordCreateDTOSchema,
  RecordAddCommentDTOSchema,
  RecordCommentListSchema,
  RecordDeleteDTOSchema,
  RecordListDTOSchema,
  RecordUpdateDTOSchema,
  RecordBulkUpdateDTOSchema,
  ViewDeleteDTOSchema,
  ViewInfoDTOSchema,
  DatabaseViewCreateDTOSchema,
  ViewUpdateDTOSchema,
  LinkDatabaseDTOSchema,
  FieldListDTOSchema,
  FieldROListDTOSchema,
  RecordActivityListSchema,
  LinkDatabaseInfoDTOSchema,
  RecordBulkUpdatesDTOSchema,
} from '@bika/types/database/dto';
import { ExcelImportDTOSchema } from '@bika/types/node/dto';
import * as DatabaseController from './database-controller';

export const databaseRouter = router({
  /**
   * Retrieve the info of a database
   */
  info: publicProcedure.input(DatabaseInfoDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.info(ctx, input);
  }),

  /**
   * Retrieve the info of a view
   */
  getView: publicProcedure.input(ViewInfoDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getView(ctx, input);
  }),

  /**
   * Create a view
   */
  createView: protectedProcedure.input(DatabaseViewCreateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.createView(user, input);
  }),

  /**
   * 获取所有关联表信息
   */
  getLinkDatabase: protectedProcedure.input(LinkDatabaseDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getLinkDatabase(ctx, input);
  }),

  /**
   * 获取关联字段所在表信息
   */
  getLinkDatabaseInfo: publicProcedure.input(LinkDatabaseInfoDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getLinkDatabaseInfo(ctx, input);
  }),

  /**
   * Update a view
   */
  updateView: protectedProcedure.input(ViewUpdateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.updateView(user, input);
  }),

  /**
   * Delete a view
   */
  deleteView: protectedProcedure.input(ViewDeleteDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.deleteView(user, input);
  }),

  /**
   * Retrieve the fields of a database
   */
  getFieldBOs: publicProcedure.input(FieldListDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getFieldBOs(ctx, input);
  }),

  /**
   * Retrieve the fieldROs of a database
   */
  getFieldROs: publicProcedure.input(FieldROListDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getFieldROs(ctx, input);
  }),

  /**
   * Retrieve the info of a field
   */
  getField: publicProcedure.input(FieldGetDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getField(ctx, input);
  }),

  /**
   * Create a field
   */
  createField: protectedProcedure.input(FieldCreateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.createField(user, input);
  }),

  /**
   * Update a field
   */
  updateField: protectedProcedure.input(FieldUpdateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.updateField(user, input);
  }),

  /**
   * Delete a field
   */
  deleteField: protectedProcedure.input(FieldDeleteDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.deleteField(user, input);
  }),

  /**
   * Retrieve the records of a database(use infinite scroll)
   */
  getInfiniteRecords: publicProcedure.input(InfiniteRecordListDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const { databaseId, mirrorId, formId, viewId, filter, keyword, cursor: startRow, limit } = input;
    const endRow = startRow + limit;

    return DatabaseController.listRecords(
      ctx,
      { databaseId, mirrorId, formId, viewId, filter, keyword, startRow, endRow },
      { useTempView: true },
    );
  }),

  /**
   * Find records with query parameter of a database
   */
  listRecords: publicProcedure.input(RecordListDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.listRecords(ctx, input);
  }),

  /**
   * Retrieve the info of a record
   */
  getRecord: publicProcedure.input(RecordDetailDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getRecord(ctx, input);
  }),

  /**
   * Retrieve multiple records by record id list
   */
  getRecords: publicProcedure.input(RecordDetailListDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getRecords(ctx, input);
  }),

  /**
   * Create a record
   */
  createRecord: publicProcedure.input(RecordCreateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.createRecordWithTRPCContext(ctx, input);
  }),

  /**
   * Update Record
   */
  updateRecord: publicProcedure.input(RecordUpdateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.updateRecord(user, input);
  }),

  /**
   * batch update records
   */
  updateRecords: publicProcedure.input(RecordBulkUpdatesDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.updateRecords(user, input);
  }),

  /**
   * Bulk update records
   */
  bulkUpdateRecords: publicProcedure.input(RecordBulkUpdateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.bulkUpdateRecords(user, input);
  }),

  /**
   * Delete records
   */
  deleteRecords: publicProcedure.input(RecordDeleteDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.deleteRecords(user, input);
  }),

  /**
   * Retrieve the comments of a record
   * @deprecated use getRecordActivities instead
   */
  getComments: publicProcedure.input(RecordCommentListSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getRecordComments(ctx, input);
  }),

  /**
   * Retrieve the activities of a record
   */
  getRecordActivities: publicProcedure.input(RecordActivityListSchema).query(async (opts) => {
    const { ctx, input } = opts;
    return DatabaseController.getRecordActivities(ctx, input);
  }),

  /**
   * Add a comment to a record
   */
  addComment: protectedProcedure.input(RecordAddCommentDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.addRecordComment(user, input);
  }),

  /**
   * Delete a comment from a record
   */
  deleteComment: protectedProcedure
    .input(
      z.object({
        databaseId: z.string(),
        recordId: z.string(),
        commentId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.deleteRecordComment(user, input);
    }),

  /*
   * 生成 Excel 增量导入模板
   */
  generateImportExcelTemplate: protectedProcedure
    .input(
      z.object({
        databaseId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.generateImportExcelTemplate(user, input.databaseId);
    }),

  /**
   * Excel 增量导入预览
   */
  importFromTemplateExcelPreview: protectedProcedure
    .input(
      z.object({
        databaseId: z.string(),
        tmpAttachmentId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.importFromTemplateExcelPreview(user, input.databaseId, input.tmpAttachmentId);
    }),

  /**
   * 增量导入 Excel v2
   */
  importFromTemplateExcel: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        tmpAttachmentId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.importFromTemplateExcel(user, input.id, input.tmpAttachmentId);
    }),

  // 导出全量数据
  exportToExcel: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        id: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.exportToExcel(user, input.spaceId, input.id);
    }),

  /**
   * 从 Excel 创建数据
   */
  createFromExcel: protectedProcedure.input(ExcelImportDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DatabaseController.createFromExcel(user, input);
  }),

  getDoc: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        docId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.getDoc(user, input);
    }),

  createDoc: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        name: z.string(),
        markdown: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.createDoc(user, input);
    }),

  updateDocName: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        docId: z.string(),
        name: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DatabaseController.updateDocName(user, input);
    }),
});
