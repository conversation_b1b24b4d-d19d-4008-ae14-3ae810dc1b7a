'use client';

import { Divider } from '@mui/material';
import type React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { PERMISSION_DESCRIPTION_KEYS, getPermissionColor } from '@bika/domains/node/client/header/node-header-info';
import type { INodeIconValue } from '@bika/types/node/bo';
import type { AccessPrivilege } from '@bika/types/permission/bo';
import { SkillsetVO } from '@bika/types/skill/vo';
import type { iString } from '@bika/types/system';
import { Button } from '@bika/ui/button';
import { useCssColor } from '@bika/ui/colors';
import { AvatarSize } from '@bika/ui/components/avatar/index';
import { SkillsetsLogos } from '@bika/ui/components/avatar/skillset-avatars';
import { jsonToText, textToJson } from '@bika/ui/editor/rich-text-editor/index';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import ShieldSecurityOutlined from '@bika/ui/icons/components/shield_security_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { NodeIcon, type NodeResourceIconType } from '@bika/ui/node/icon';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';

/**
 * TalkVO Popover Cardcomponent displays detailed information about the node in a popover
 */
export const TalkVOPopover: React.FC<{
  // value: TalkVO;
  name: string;
  nodeType: NodeResourceIconType;
  description?: iString;
  // icon?: AvatarLogo;
  icon: INodeIconValue;
  // extra footer for extension
  footer?: React.ReactNode;
  skillsets?: SkillsetVO[] | null;
  permission?: AccessPrivilege;
  onEdit?: () => void;
  right?: React.ReactNode;
}> = ({ name, description, icon, permission, onEdit, footer, nodeType, right, skillsets }) => {
  const { t, i } = useLocale();
  const colors = useCssColor();

  const displayDescription = i(description) ? jsonToText(textToJson(i(description))) : t.automation.no_description;

  return (
    <Stack
      spacing={1}
      sx={{
        width: '400px',
        maxWidth: '400px',
        minWidth: '300px',
        padding: '8px 16px',
        boxShadow: 'var(--shadow-high)',
        backgroundColor: 'var(--bg-popup)',
        border: '1px solid var(--border-default)',
        borderRadius: '8px',
        overflow: 'hidden',
      }}
    >
      {/* alignItems="center" spacing={2} sx={{ mb: 2 }} */}
      {/* Header with avatar and title */}
      <Stack direction="column">
        <Stack direction="row" alignItems="center" justifyContent={'space-between'} className="w-full  pt-[8px]">
          <NodeIcon value={icon} size={32} title={name} />

          {/* Edit button */}
          {right ||
            (permission && ['FULL_ACCESS', 'CAN_EDIT'].includes(permission) && (
              <Button
                // variant="outlined"
                variant="soft"
                color="default"
                size="md"
                sx={{
                  color: 'var(--text-primary)',
                  '& svg': {
                    fill: 'var(--text-primary) !important',
                  },
                  '&  svg > path': {
                    fill: 'var(--text-primary) !important',
                  },
                }}
                startDecorator={<EditOutlined color="var(---text-primary) !important" />}
                onClick={onEdit}
                // sx={{ width: '100%' }}
              >
                {t.action.edit}
              </Button>
            ))}
        </Stack>

        <Box sx={{ flex: 1, minWidth: 0 }} className="mt-[12px]">
          <EllipsisText>
            <Typography
              level="h6"
              sx={{
                fontWeight: 600,
                color: 'var(--text-primary)',
                maxWidth: '100%',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {i(name)}
            </Typography>
          </EllipsisText>
        </Box>
      </Stack>

      {/* <MultiLineEllipsisText tooltip={displayDescription} maxLines={18}>
        {displayDescription}
      </MultiLineEllipsisText> */}

      <Typography
        level="b3"
        sx={{
          maxWidth: '100%',
          width: '100%',
          marginBottom: '8px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          lineHeight: 1.5,
          marginTop: '12px',
          maxHeight: '300px',
          minHeight: '20px',
          color: 'var(--text-secondary)',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        {displayDescription}
      </Typography>

      {skillsets && <SkillsetsLogos skillsets={skillsets} size={AvatarSize.Size20} />}
      {footer}

      {permission && (
        <>
          <Divider></Divider>
          <Stack direction="row" alignItems="center" spacing={1} sx={{ marginTop: '8px', minWidth: 0 }}>
            <ShieldSecurityOutlined color={getPermissionColor(permission, colors)} size={16} />
            <Typography
              level="b4"
              sx={{
                color: getPermissionColor(permission, colors),
                flex: 1,
                minWidth: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {permission
                ? t.node.permission.permission_description[
                    PERMISSION_DESCRIPTION_KEYS[permission] as keyof typeof t.node.permission.permission_description
                  ]
                : t.automation.no_description}
            </Typography>
          </Stack>
        </>
      )}
    </Stack>
  );
};
