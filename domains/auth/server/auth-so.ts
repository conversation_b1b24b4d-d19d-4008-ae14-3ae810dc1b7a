import { Argon2id } from 'oslo/password';
import { RemoteStorageSO, VerificationCodeType } from '@bika/domains/system/server/remote-storage/remote-storage-so';
import { SiteAdminSO } from '@bika/domains/user/server/site-admin-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { SessionSO, SessionAttributes } from '@bika/server-orm/session';
import { AvatarLogo, Locale } from '@bika/types/system';
import { RemoteStorageWeixinQrCodeTicketUserId } from '@bika/types/system/remote-storage';
import { UserBO, UserLinkType } from '@bika/types/user/bo';
import { AuthOptions } from './types';
// If you're using Node.js 18 or below, you'll need to polyfill the Web Crypto API. This is not required in Node.js 20, CloudFlare Workers, Deno, Bun, and Vercel Edge Functions. This can be done either by importing webcrypto, or by enabling an experimental flag.
// import { webcrypto } from 'node:crypto';
// https://lucia-auth.com/getting-started/
// eslint-disable-next-line no-undef
// if (!globalThis.crypto) globalThis.crypto = webcrypto as unknown as Crypto;

/**
 * Authenticate & Authorization(鉴权)
 */
export class AuthSO {
  private _user: UserSO;

  private _session: SessionSO;

  protected constructor(user: UserSO, session: SessionSO) {
    this._user = user;
    this._session = session;
  }

  public get user() {
    return this._user;
  }

  public get session() {
    return this._session;
  }

  static async signIn(username: string, password: string): Promise<AuthSO | null> {
    const existingUser = await UserSO.findByUsername(username);
    if (!existingUser) return null;
    const validPassword = await new Argon2id().verify(existingUser.model.hashedPassword!, password);
    if (validPassword) {
      const newSession = await SessionSO.create(existingUser.id);
      return new AuthSO(existingUser, newSession);
    }
    return null;
  }

  static async signUp(username: string, password: string, locale: Locale): Promise<AuthSO> {
    const hashedPassword = await new Argon2id().hash(password);
    const newUser = await UserSO.createUser(
      {
        username,
        hashedPassword,
        avatar: {
          type: 'COLOR',
          color: 'DEEP_PURPLE',
        },
      },
      { locale },
    );
    const newSession = await SessionSO.create(newUser.id);
    return new AuthSO(newUser, newSession);
  }

  async signOut() {
    return this._session.invalidate();
  }

  static async validateBySessionId(sessionId: string): Promise<AuthSO | null> {
    const sessionSO = await SessionSO.validate(sessionId);
    if (sessionSO) {
      return new AuthSO(await UserSO.init(sessionSO.userId), sessionSO);
    }
    return null;
  }

  /**
   * 无需任何其它动作，马上注册创建
   * @returns
   */
  static async quickLogin(
    referralCode?: string,
    options: AuthOptions = {},
    attributes: SessionAttributes = {},
  ): Promise<AuthSO> {
    const { timezone, locale } = options || {};
    const newUser = await UserSO.createUser(
      { timezone, avatar: { type: 'COLOR', color: 'DEEP_PURPLE' } },
      { locale },
      { referralCode },
    );
    const newSession = await SessionSO.create(newUser.id, attributes);
    // 创建空间站，以便直接进入空间站
    await newUser.createSpace({ name: '' }); // 空的空间站名称会触发新手引导时填空间站名
    return new AuthSO(newUser, newSession);
  }

  /**
   * 微信参数二维码登录 通过ticket 轮询接口
   * @param ticket
   * @param attributes
   * @returns
   */
  static async loginByWeixinTicket(ticket: string, attributes: SessionAttributes = {}) {
    const property = (await RemoteStorageSO.getProperty(
      'WEIXIN_QR_CODE_TICKET_USER_ID',
      ticket,
    )) as RemoteStorageWeixinQrCodeTicketUserId;
    if (!property) {
      throw new Error('Ticket not found');
    }
    const user = await UserSO.init(property.userId);
    const newSession = await SessionSO.create(user.id, attributes);
    return new AuthSO(user, newSession);
  }

  static async loginByQuickCode(code: string, attributes: SessionAttributes = {}) {
    const userId = await RemoteStorageSO.getVerificationCode('QUICK_LOGIN_VERIFICATION_CODE', code);
    if (!userId) {
      throw new Error('User not found');
    }
    const user = await UserSO.init(userId);
    const newSession = await SessionSO.create(user.id, attributes);
    return new AuthSO(user, newSession);
  }

  static async loginByVerificationCode(
    type: VerificationCodeType,
    target: string,
    verificationCode: string,
    options: AuthOptions = {},
    attributes: SessionAttributes = {},
  ): Promise<AuthSO> {
    // 校验验证码
    await RemoteStorageSO.matchVerificationCode(type, target, verificationCode);

    let user: UserSO | null;
    let bo: UserBO;
    if (type === 'MAIL_VERIFICATION_CODE') {
      user = await UserSO.findByEmail(target);
      bo = {
        email: target,
        name: target.split('@')[0],
      };
    } else {
      user = await UserSO.findByPhone(target);
      bo = {
        phone: target,
      };
    }
    if (user) {
      const newSession = await SessionSO.create(user.id, attributes);
      return new AuthSO(user, newSession);
    }
    // 自动注册
    const { timezone, locale, skipSpaceCreatable, referralCode } = options || {};
    const newUser = await UserSO.createUser({ ...bo, timezone }, { locale }, { referralCode });
    const newSession = await SessionSO.create(newUser.id, attributes);
    if (!skipSpaceCreatable) {
      await newUser.createSpace({ name: '' });
    }
    await RemoteStorageSO.delete(type, target);
    return new AuthSO(newUser, newSession);
  }

  static async adminLogin(username: string, password: string): Promise<AuthSO> {
    const authSO = await this.signIn(username, password);
    if (!authSO) {
      throw new Error('Invalid username or password');
    }
    // 验证是否是管理员
    const isAdmin = await SiteAdminSO.isAdmin(authSO.user.id);
    if (!isAdmin) {
      throw new Error('Not site admin');
    }
    return authSO;
  }

  static async getUserByExternalUser(
    type: UserLinkType,
    externalUser: {
      email?: string;
      externalId: string;
      name?: string;
      avatar?: AvatarLogo;
    },
    options: AuthOptions,
  ): Promise<{ isCreate: boolean; user: UserSO }> {
    const { email, name, avatar, externalId } = externalUser;
    const userSO = await UserSO.getUserByExternalId(type, externalId);
    if (userSO) {
      // 用户存在，直接返回，不新建了
      return { isCreate: false, user: userSO };
    }
    const createUserBO: UserBO = { name, avatar: avatar || { type: 'COLOR', color: 'YELLOW' } };

    if (email) {
      // 查询邮箱是否已经被使用 已使用就不要重复写了
      const user = await UserSO.findByEmail(email);
      if (!user) {
        createUserBO.email = email;
      } else {
        const emailArr = email.split('@');
        const emailName = emailArr[0];
        const emailDomain = emailArr[1];

        const firstChar = emailName.charAt(0);
        const lastChar = emailName.charAt(emailName.length - 1);
        const emailNameStar = `${firstChar}**${emailName.length > 1 ? lastChar : ''}`;

        const domainParts = emailDomain.split('.');
        const domain = domainParts[0];
        const tld = domainParts.slice(1).join('.');

        const domainFirstChar = domain.charAt(0);
        const domainStar = `${domainFirstChar}**`;

        const emailStar = `${emailNameStar}@${domainStar}.${tld}`;
        throw new Error(
          `The email ${emailStar} is already linked to an existing Bika account. Please switch to 'Continue with Email' to log in.`,
        );
      }
    }
    const { timezone, locale, skipSpaceCreatable, referralCode } = options || {};

    createUserBO.timezone = timezone;

    // TODO 加上事务
    const newUser = await UserSO.createUser(createUserBO, { locale }, { referralCode });
    await newUser.linkExternalUser(type, {
      externalId,
      avatar: createUserBO.avatar,
    });
    if (!Number(skipSpaceCreatable)) {
      // 创建空间站，以便直接进入空间站
      await newUser.createSpace({ name: '' });
    }
    return { isCreate: true, user: newUser };
  }

  static async loginWithExternalUser(
    type: UserLinkType,
    externalUser: {
      email?: string;
      externalId: string;
      name?: string;
      avatar?: AvatarLogo;
    },
    options: AuthOptions,
    attributes: SessionAttributes,
  ): Promise<AuthSO> {
    const { isCreate, user: userSO } = await AuthSO.getUserByExternalUser(type, externalUser, options);
    if (isCreate) {
      const newSession = await SessionSO.create(userSO.id, attributes);
      return new AuthSO(userSO, newSession);
    }
    const newSession = await SessionSO.create(userSO.id, attributes);
    return new AuthSO(userSO, newSession);
  }

  toVO() {
    return {
      user: this.user.toVO(),
      session: this.session.toVO(),
    };
  }
}
