// OAuth Callbacks, etc.
import { OAuth2RequestError } from 'arctic';
import { Hono } from 'hono';
import { AuthSO } from '@bika/domains/auth/server';
import { AuthOptions } from '@bika/domains/auth/server/types';
import { generateRandomString, Logger } from '@bika/domains/shared/server';
import { RemoteStorageSO } from '@bika/domains/system/server';
import { ExternalAlreadyLinkedError } from '@bika/domains/user/server/errors';
import { UserLinkData } from '@bika/domains/user/server/types';
import { UserSO } from '@bika/domains/user/server/user-so';
import { parseAttributesFromRequest } from '@bika/server-orm/utils';
import { UserLinkTypeSchema } from '@bika/types/user/bo';
import { AuthController } from './auth-controller';
import { RemoteStorageHelper } from '../../system/server/remote-storage/remote-storage-helper';

const app = new Hono();

function getCallbackRedirect(code: string, redirect?: string) {
  if (redirect) {
    if (redirect.startsWith('bika://')) {
      return `${redirect}?code=${code}`;
    }
    return redirect;
  }
  return null;
}

function printPostMessage(payload: any) {
  return `
<script>
window.opener.postMessage(${JSON.stringify(payload)});
window.close();
</script>
`;
}

/**
 * @api {get} /:oauth_type Get OAuth Redirect URL
 */
app.get('/:oauth_type', async (c) => {
  try {
    const url = await AuthController.getOAuthedirectUrl(c.req.param('oauth_type'), c.req.query(), c.req.raw.headers);
    return c.redirect(url);
  } catch (e) {
    if (e instanceof Error) {
      return c.text(e.message, 400);
    }
  }
});

/**
 * @api {get} /callback/weixin 微信公众号回调
 */
app.all('/callback/weixin', async (c) => {
  const signature = c.req.query('signature');
  const timestamp = c.req.query('timestamp');
  const nonce = c.req.query('nonce');
  const echostr = c.req.query('echostr');
  const rawBody = await c.req.text();
  if (rawBody) {
    try {
      const message = await AuthController.callbackWeiXinMessage(signature, timestamp, nonce, echostr, rawBody);
      c.res.headers.set('Content-Type', 'text/xml');
      return c.text(message);
    } catch (e) {
      console.error(e);
      if (e instanceof Error) {
        return c.text(e.message, 400);
      }
    }
  }
  return c.text(echostr || 'ok');
});

/**
 * @api {get} /callback/:oauth_type OAuth Callback
 */
app.all('/callback/:oauth_type', async (c) => {
  const oauthType = c.req.param('oauth_type');
  let state = c.req.query('state');
  if (!state && oauthType === 'apple') {
    const formData = await c.req.formData();
    state = formData.get('state') as string;
  }
  if (!state) {
    return c.text('no state', 400);
  }
  const property = await RemoteStorageSO.getThirdPartyLoginState(state);
  if (!property) {
    return c.text('no state', 400);
  }
  const oauthRedirect = property.redirectUrl;
  const options = (property || {}) as AuthOptions;

  let externalUser!: UserLinkData;

  try {
    if (oauthType === 'apple') {
      const formData = await c.req.formData();
      externalUser = await AuthController.callbackAppleOAuth(formData);
    } else if (oauthType === 'github') {
      const code = c.req.query('code');
      externalUser = await AuthController.callbackGitHubOAuth(code);
    } else if (oauthType === 'google') {
      const code = c.req.query('code');
      const codeVerifier = property.codeVerifier;
      externalUser = await AuthController.callbackGoogleOAuth(code, codeVerifier);
    } else {
      return c.text('not supported', 400);
    }
    const userLinkType = UserLinkTypeSchema.parse(oauthType.toUpperCase());
    // 如果是绑定用户
    if (property.linkExternalUser) {
      const userSO = await UserSO.init(property.linkExternalUser);
      await userSO.linkExternalUser(userLinkType, externalUser, property.initExternalUser === '1');
      if (externalUser.avatar?.type && externalUser.avatar.type === 'URL' && externalUser.avatar.url) {
        userSO.updateAvatarByUrl(externalUser.avatar.url);
      }
      return c.html(printPostMessage({ error: null, type: oauthType, code: '' }), 200);
    }
    const attributes = await parseAttributesFromRequest(c.req.raw.headers, options.bikaVersion);
    const authSO = await AuthSO.loginWithExternalUser(userLinkType, externalUser, options, attributes);
    // 异步下载头像并更新
    if (externalUser.avatar?.type && externalUser.avatar.type === 'URL' && externalUser.avatar.url) {
      authSO.user.updateAvatarByUrl(externalUser.avatar.url);
    }
    // http://localhost:3000/api/auth/github?redirect=ai.bika.bika://auth
    if (authSO) {
      const sessionCookie = authSO.session.toCookie();
      const redirect = getCallbackRedirect(sessionCookie.value, oauthRedirect);
      c.res.headers.append('Set-Cookie', sessionCookie.serialize());
      if (!redirect) {
        const payload = {
          error: null,
          code: sessionCookie.value,
          type: oauthType,
        };

        return c.html(printPostMessage(payload), 200);
      }
      return c.redirect(redirect);
    }
    return c.text('login failed, auth not available', 400);
  } catch (e) {
    if (e instanceof ExternalAlreadyLinkedError) {
      const quickCode = generateRandomString(64);
      await RemoteStorageHelper.verificationCode.create('QUICK_LOGIN_VERIFICATION_CODE', e.userId, quickCode);
      const payload = {
        error: 'ExternalAlreadyLinkedError',
        code: quickCode,
        type: oauthType,
      };
      return c.html(printPostMessage(payload), 200);
    }
    Logger.error(`login fail`, e);
    if (e instanceof OAuth2RequestError) {
      // return c.text(e.message, 400);
      return c.redirect(`/error?message=${encodeURIComponent(e.message)}`);
    }
    if (e instanceof Error) {
      // return c.text(e.message, 500);
      return c.redirect(`/error?message=${encodeURIComponent(e.message)}`);
    }
  }
});

export default app;
