import { generateCodeVerifier, generateState } from 'arctic';
import { XMLParser } from 'fast-xml-parser';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { Locale } from '@bika/contents/i18n';
import { AuthSO } from '@bika/domains/auth/server/auth-so';
import { apple } from '@bika/domains/auth/server/integrations/apple/index';
import { github } from '@bika/domains/auth/server/integrations/github/index';
import { google } from '@bika/domains/auth/server/integrations/google/index';
import * as weixin from '@bika/domains/auth/server/integrations/weixin/index';
import { createOauthTypeProperty } from '@bika/domains/auth/server/utils';
import { RemoteStorageSO } from '@bika/domains/system/server';
import { UserLinkData } from '@bika/domains/user/server/types';
import { UserSO } from '@bika/domains/user/server/user-so';
import { SESSION_COOKIE_NAME, SessionSO } from '@bika/server-orm/session';
import { parseAttributesFromRequest } from '@bika/server-orm/utils';
import { AdminLoginInput, LoginInput } from '@bika/types/auth';
import { TIMEZONE_HEADER, LOCALE_HEADER } from '@bika/types/shared';
import { AuthVO, ApiFetchRequestContext } from '@bika/types/user/vo';
import { credentials } from '../server/integrations/apple';
import { GitHubUser } from '../server/integrations/github/types';
import { GoogleUser } from '../server/integrations/google/types';
import { AuthOptions } from '../server/types';

export class AuthController {
  /**
   * 在Next JS的request中进行鉴权，
   * 如果失败，会自动redirect
   */
  static async getAuthByNextRequest(headers: Headers) {
    const sessionSO = await AuthController.getSessionByNextRequest(headers);
    if (!sessionSO) {
      return null;
    }
    const authSO = await AuthSO.validateBySessionId(sessionSO.id);
    if (!authSO) {
      return null;
    }

    return authSO.toVO();
  }

  /**
   * 获取第三方登录的跳转链接
   * @param oauthType
   * @param query
   * @param headers
   * @returns {string} 跳转链接
   */
  static async getOAuthedirectUrl(oauthType: string, query: Record<string, string>, headers: Headers) {
    const state = generateState();

    const property = await createOauthTypeProperty(query, headers);

    if (oauthType === 'google') {
      const codeVerifier = generateCodeVerifier();
      property.codeVerifier = codeVerifier;
    }

    await RemoteStorageSO.create(state, property, new Date(Date.now() + 60 * 10 * 1000));

    if (oauthType === 'github') {
      const url = await github.createAuthorizationURL(state);
      return url.href;
    }
    if (oauthType === 'google') {
      const codeVerifier = property.codeVerifier as string;
      const url = await google.createAuthorizationURL(state, codeVerifier, {
        scopes: ['openid', 'email', 'profile'],
      });
      return url.href;
    }
    if (oauthType === 'apple') {
      const url: URL = await apple.createAuthorizationURL(state, {
        scopes: ['email', 'name'],
      });
      url.searchParams.set('response_mode', 'form_post');

      return url.href;
    }
    throw new Error('not supported');
  }

  /**
   * 微信消息借口 后面要独立出去？
   * @param signature
   * @param timestamp
   * @param nonce
   * @param echostr
   * @param body
   * @returns
   */
  static async callbackWeiXinMessage(
    signature?: string,
    timestamp?: string,
    nonce?: string,
    echostr?: string,
    body?: string,
  ) {
    if (!signature || !timestamp || !nonce || !weixin.verifyMessage(signature, timestamp, nonce)) {
      throw new Error('weixin verify message error');
    }
    if (body) {
      const parser = new XMLParser({
        ignoreAttributes: false,
      });
      const data = parser.parse(body);
      const xml = data.xml;
      if ((xml.MsgType === 'event' && xml.Event === 'SCAN') || (xml.MsgType === 'event' && xml.Event === 'subscribe')) {
        const property = await RemoteStorageSO.getThirdPartyLoginState(xml.Ticket);
        if (!property) {
          throw new Error('no ticket');
        }
        const info = await weixin.getUserInfo(xml.FromUserName);
        // 绑定开放平台才有 unionid 必须绑定 否则APP 网站 公众号 对不上
        const options = (property || {}) as AuthOptions;

        const linkData: UserLinkData = {
          externalId: (info.unionid || info.openid) as string,
          name: info.nickname,
          avatar: {
            type: 'URL',
            url: info.headimgurl as string,
          },
        };
        let message = '';
        if (property.linkExternalUser) {
          const user = await UserSO.init(property.linkExternalUser);
          try {
            if (user) {
              await user.linkExternalUser('WEIXIN', linkData);
              await RemoteStorageSO.set(xml.Ticket, {
                type: 'WEIXIN_QR_CODE_TICKET_USER_ID',
                userId: property.linkExternalUser,
              });
              message = '绑定成功';
            }
          } catch (e) {
            // @ts-expect-error
            message = `绑定失败 ${e.message}`;
          }
        } else {
          const authInfo = await AuthSO.getUserByExternalUser('WEIXIN', linkData, options);

          if (authInfo.isCreate) {
            // 异步下载头像并更新
            authInfo.user.updateAvatarByUrl(info.headimgurl);
          }

          await RemoteStorageSO.set(xml.Ticket, {
            type: 'WEIXIN_QR_CODE_TICKET_USER_ID',
            userId: authInfo.user.id,
          });
          message = '登录成功';
        }

        const replyMessage = `
        <xml>
          <ToUserName><![CDATA[${xml.FromUserName}]]></ToUserName>
          <FromUserName><![CDATA[${xml.ToUserName}]]></FromUserName>
          <CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
          <MsgType><![CDATA[text]]></MsgType>
          <Content><![CDATA[${message}]]></Content>
        </xml>
      `;
        return replyMessage;
      }
    }
    return echostr || 'ok';
  }

  static async callbackAppleOAuth(formData: FormData = new FormData()): Promise<UserLinkData> {
    const userJSON = formData.get('user');
    const error = formData.get('error');
    if (error) {
      throw new Error(error as string);
    }
    const code = formData.get('code') as string;

    const data = new URLSearchParams({
      client_id: credentials.clientId,
      client_secret: jwt.sign({}, credentials.certificate, {
        algorithm: 'ES256',
        expiresIn: '180d', // 过期时间
        audience: 'https://appleid.apple.com',
        issuer: credentials.teamId,
        subject: credentials.clientId,
        header: {
          alg: 'ES256',
          kid: credentials.keyId,
        },
      }),
      code,
      grant_type: 'authorization_code',
    });

    const res = await fetch('https://appleid.apple.com/auth/token', {
      method: 'POST',
      body: data,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    const json = await res.json();
    if (json.error) {
      throw new Error(json.error_description);
    }
    const idToken = json.id_token;
    const payload = jwt.decode(idToken) as JwtPayload;
    if (payload.sub) {
      const user = JSON.parse((userJSON as string) || '{}');
      const name = user.firstName ? `${user.firstName} ${user.lastName}` : payload.email.split('@')[0];
      return {
        externalId: payload.sub,
        name,
        email: payload.email,
        avatar: {
          type: 'COLOR',
          color: 'BLUE',
        },
      };
    }
    throw new Error('login failed, auth not available');
  }

  static async callbackGitHubOAuth(code?: string): Promise<UserLinkData> {
    if (!code) {
      throw new Error('no auth code');
    }
    const tokens = await github.validateAuthorizationCode(code);
    const githubUserResponse = await fetch('https://api.github.com/user', {
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
    });
    const githubUser: GitHubUser = await githubUserResponse.json();

    return {
      externalId: githubUser.id.toString(),
      name: githubUser.name || githubUser.login,
      email: githubUser.email!,
      // 新建时使用URL保存，登录后异步下载头像并更新
      avatar: {
        type: 'URL',
        url: githubUser.avatar_url,
      },
    };
  }

  static async callbackGoogleOAuth(code?: string, codeVerifier?: string): Promise<UserLinkData> {
    if (!code || !codeVerifier) {
      throw new Error('no auth code');
    }
    const tokens = await google.validateAuthorizationCode(code, codeVerifier);
    const response = await fetch('https://openidconnect.googleapis.com/v1/userinfo', {
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
    });
    const googleUser: GoogleUser = await response.json();

    return {
      externalId: googleUser.sub,
      name: googleUser.name,
      email: googleUser.email,
      // 新建时使用URL保存，登录后异步下载头像并更新
      avatar: {
        type: 'URL',
        url: googleUser.picture,
      },
    };
  }

  static async getAuthBySessionId(sessionId: string) {
    const authSO = await AuthSO.validateBySessionId(sessionId);
    if (!authSO) {
      return null;
    }

    return authSO.toVO();
  }

  static async getAuthByHeaders(headers: Headers): Promise<AuthVO | null> {
    const sessionSO = await AuthController.getSessionByHeaders(headers);
    if (!sessionSO) {
      return null;
    }

    const authSO = await AuthSO.validateBySessionId(sessionSO.id);
    if (!authSO) {
      return null;
    }

    return authSO.toVO();
  }

  /**
   * Fetch服务器，除了Next JS其它所有情况都是Fetch请求，包括Hono、tRPC
   *
   * 需要手动在fetch请求返回"Set-Cookie"头，来更新cookie，
   * 因此只返回是否要refreshCookie，由外部自己处理更新cookie
   *
   * Next JS则是直接函数内更新
   * @param req
   * @returns
   */
  static async getSessionByFetchRequest(fetchRequest: Request): Promise<SessionSO | null> {
    const { headers } = fetchRequest;
    const sessionId = SessionSO.parseSessionIdFromHeader(headers);

    if (sessionId) {
      const sessionSO = await SessionSO.validate(sessionId);
      return sessionSO;
    }
    return null;
  }

  /**
   * 从Next 的Request里获得session id
   * NextJS的请求里保存Cookie，这个函数只能在NextJS里执行
   *
   * 注意，这个Session不严谨，是cookie session判断，没有数据库sessoin判断
   * 不实际检查数据库登录态，较快，适合middleware中执行，但对于残留的cookie，但session已经过期的情况，无法检查
   *
   * 注意！只能在Next JS 的page.ts / route.ts 里执行， tRPC也不行，tRPC使用fetch request(hono)
   * @param request
   */
  static async getSessionByNextRequest(headers: Headers, autoRefreshCookie = true): Promise<SessionSO | null> {
    const sessionId = SessionSO.parseSessionIdFromHeader(headers);

    if (sessionId) {
      const sessionSO = await SessionSO.validate(sessionId);
      if (autoRefreshCookie && sessionSO && sessionSO.fresh) {
        const sessionCookie = sessionSO.toCookie();
        (await cookies()).set(SESSION_COOKIE_NAME, sessionCookie.value, sessionCookie.attributes);
      }
      return sessionSO;
    }
    return null;
    // return this.getSession(headers(), cookies(), refreshCookie);
  }

  static async getSessionByHeaders(theHeaders: Headers): Promise<SessionSO | null> {
    const sessionId = SessionSO.parseSessionIdFromHeader(theHeaders);
    if (sessionId) {
      const sessionSO = await SessionSO.validate(sessionId);
      return sessionSO;
    }
    return null;
    // return this.getSession(headers(), cookies(), refreshCookie);
  }

  static async loginWithQuickCode(fetchRequestContext: ApiFetchRequestContext, code: string): Promise<AuthVO> {
    const attributes = parseAttributesFromRequest(fetchRequestContext.req.headers);
    const auth = await AuthSO.loginByQuickCode(code, attributes);
    fetchRequestContext.resHeaders?.append('Set-Cookie', auth.session.toCookie().serialize());
    return auth.toVO();
  }

  /**
   * 解析成cookie
   */
  static async quickLoginByFetchRequest(
    fetchRequestContext: ApiFetchRequestContext,
    referralCode?: string,
    setCookie = true,
  ): Promise<AuthVO> {
    // get timezone
    const option = AuthController.parseHeaders(fetchRequestContext);
    const attributes = parseAttributesFromRequest(fetchRequestContext.req.headers);
    const auth = await AuthSO.quickLogin(referralCode, option, attributes);
    if (setCookie) fetchRequestContext.resHeaders?.append('Set-Cookie', auth.session.toCookie().serialize());

    return auth.toVO();
  }

  static async loginByWeixinTicket(fetchRequestContext: ApiFetchRequestContext, ticket: string): Promise<AuthVO> {
    const attributes = parseAttributesFromRequest(fetchRequestContext.req.headers);
    const auth = await AuthSO.loginByWeixinTicket(ticket, attributes);
    fetchRequestContext.resHeaders?.append('Set-Cookie', auth.session.toCookie().serialize());
    return auth.toVO();
  }

  static async loginByVerificationCode(
    fetchRequestContext: ApiFetchRequestContext,
    input: LoginInput,
  ): Promise<AuthVO> {
    const type = input.type === 'SMS_CODE' ? 'SMS_VERIFICATION_CODE' : 'MAIL_VERIFICATION_CODE';
    const { username, credential, skipSpaceCreatable, referralCode } = input;
    const option = AuthController.parseHeaders(fetchRequestContext);
    const attributes = parseAttributesFromRequest(fetchRequestContext.req.headers);
    const auth = await AuthSO.loginByVerificationCode(
      type,
      username,
      credential,
      { ...option, skipSpaceCreatable, referralCode },
      attributes,
    );
    fetchRequestContext.resHeaders?.append('Set-Cookie', auth.session.toCookie().serialize());
    return auth.toVO();
  }

  static async adminLogin(fetchRequestContext: ApiFetchRequestContext, input: AdminLoginInput): Promise<AuthVO> {
    const { username, password } = input;
    const auth = await AuthSO.adminLogin(username, password);
    fetchRequestContext.resHeaders?.append('Set-Cookie', auth.session.toCookie().serialize());
    return auth.toVO();
  }

  private static parseHeaders(ctx: ApiFetchRequestContext): AuthOptions {
    const { headers } = ctx.req;
    const locale = headers.get(LOCALE_HEADER);
    return {
      timezone: headers.get(TIMEZONE_HEADER) || undefined,
      locale: locale ? (locale as Locale) : undefined,
    };
  }

  /**
   * Next JS的cookie登出
   * @param auth
   * @returns
   */
  static async logoutByNextRequest(auth: AuthVO, setCookie = true) {
    const existingAuth = await AuthSO.validateBySessionId(auth.session.id);
    const logoutSession = await existingAuth!.signOut();

    if (setCookie) await this.cleanSessionCookie();

    return logoutSession.id;
  }

  /**
   * 确保cookie清理
   */
  static async cleanSessionCookie() {
    const blankCookie = SessionSO.getBlankCookie();
    (await cookies()).set(SESSION_COOKIE_NAME, blankCookie.value, blankCookie.attributes);
  }

  static async logoutByFetchRequest(fetchRequestContext: ApiFetchRequestContext, setCookie = true) {
    const session = fetchRequestContext.session!;
    const existingAuth = await AuthSO.validateBySessionId(session.id);
    const logoutSession = await existingAuth!.signOut();

    const blankCookie = SessionSO.getBlankCookie();

    if (setCookie) fetchRequestContext.resHeaders?.append('Set-Cookie', blankCookie.serialize());

    return logoutSession.id;
  }
}
