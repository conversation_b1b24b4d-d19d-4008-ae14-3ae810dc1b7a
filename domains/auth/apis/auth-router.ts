import { generateState } from 'arctic';
import { z } from 'zod';
import { AuthController } from '@bika/domains/auth/apis';
import { AuthSO } from '@bika/domains/auth/server/auth-so';
import * as weixin from '@bika/domains/auth/server/integrations/weixin/index';
import { RemoteStorageSO } from '@bika/domains/system/server';
import { SiteAdminSO } from '@bika/domains/user/server/site-admin-so';
import { router, protectedProcedure, publicProcedure, TRPCError } from '@bika/server-orm/trpc';
import { LoginInputSchema, AdminLoginInputSchema } from '@bika/types/auth';
import { createOauthTypeProperty } from '../server/utils';

export const authRouter = router({
  login: publicProcedure.input(LoginInputSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    switch (input.type) {
      case 'SMS_CODE':
      case 'EMAIL_CODE':
        return AuthController.loginByVerificationCode(ctx, input);
      default:
        throw new TRPCError({ code: 'BAD_REQUEST', message: `Invalid login type ${input.type}` });
    }
  }),

  /**
   * 管理员登录专属
   */
  adminLogin: publicProcedure.input(AdminLoginInputSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    return AuthController.adminLogin(ctx, input);
  }),

  /**
   * 一键快速登录
   */
  quickLogin: publicProcedure
    .input(z.object({ referralCode: z.string().optional() }).optional())
    .mutation(async (opt) => AuthController.quickLoginByFetchRequest(opt.ctx, opt.input?.referralCode)),

  // 邮箱重复后的快速验证码登录某个账号32位验证码
  loginWithQuickCode: publicProcedure
    .input(
      z.object({
        code: z.string(),
      }),
    )
    .mutation(async (opt) => AuthController.loginWithQuickCode(opt.ctx, opt.input.code)),

  /**
   * 获取我的信息，自动从cookie或headers中拿
   */
  getMe: publicProcedure.query(async (opts) => {
    const { ctx } = opts;
    const { session } = ctx;
    if (!session) {
      return null;
    }
    const authSO = await AuthSO.validateBySessionId(session!.id);
    if (authSO) {
      const { user } = authSO;
      const host = ctx.req.headers.get('host');
      const cnHost = process.env.CN_HOST || process.env.WATERMARK_HOST || 'bikaai.cn';
      const isFromCNHost = !!host?.includes(cnHost);
      user
        .setIsChinaUser(isFromCNHost)
        .then(() => {
          // console.log('用户的 isChinaUser 变更成功', user.isChinaUser());
          // 这里是为了让用户的 isChinaUser 变更生效
          // 但是不需要返回给前端
          // authSO.update(user);
        })
        .catch((e) => {
          // console.error(e);
        });
    }

    return authSO?.toVO();
  }),

  /**
   * 登出
   */
  logout: protectedProcedure.mutation(async (opts) => {
    const { ctx } = opts;
    const logoutResult = await AuthController.logoutByFetchRequest(ctx);
    return logoutResult;
  }),

  /**
   * 是不是管理员？
   */
  isAdmin: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const isAdmin = await SiteAdminSO.isAdmin(ctx.session!.userId);
    return isAdmin;
  }),

  // 用 ticket 换取 user id登录
  loginByWeixinTicket: publicProcedure
    .input(
      z.object({
        ticket: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      try {
        await AuthController.loginByWeixinTicket(ctx, input.ticket);
        return true;
      } catch (error) {
        return false;
      }
    }),

  getWeixinQRCodeTicket: publicProcedure.input(z.record(z.string())).query(async (opts) => {
    const property = await createOauthTypeProperty(opts.input || {}, opts.ctx.req.headers);
    const state = generateState();

    const ticket = await weixin.createQRCodeTicket(state);
    await RemoteStorageSO.create(ticket, property, new Date(Date.now() + 60 * 10 * 1000));
    const qrUrl = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${encodeURIComponent(ticket)}`;
    // 前端 ticket 轮询来换是否已经扫码登录
    return { url: qrUrl, state: ticket };
  }),
});
