import { Hono } from 'hono';
import { Saml } from '@bika/domains/auth/server/integrations/saml/index';
import { extractRequestUrl } from '@bika/server-orm/utils';

const app = new Hono();

/**
 * 前缀: /api/site-admin/saml
 * 站点级SP元数据端点, 公开访问, 供IdP接入
 * 一旦开启, 整个站点将默认走SAML登录
 */
app.get('/metadata', async (c) => {
  // 整个站点的SAML SP元数据, 用于IdP接入
  let hostname: string;
  const baseUrl = extractRequestUrl(c.req.raw.headers);
  if (!process.env.APP_HOSTNAME) {
    hostname = baseUrl;
  } else {
    hostname = process.env.APP_HOSTNAME;
  }
  const samlSP = Saml.getSiteSP(hostname);
  const metadataXmlString = samlSP.getMetadataXml();
  return c.text(metadataXmlString, 200, { 'Content-Type': 'application/xml' });
});

export default app;
