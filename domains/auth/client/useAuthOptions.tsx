import { useSearchParams } from 'next/navigation';
import { useLocale } from '@bika/contents/i18n/context';
import { getVersion } from 'sharelib/app-env';
import { useGlobalContext } from '@bika/types/website/context';
import { AuthTabViewProps } from './props';

export function useAuthOptions(props: AuthTabViewProps) {
  const globalContext = useGlobalContext();
  const { lang } = useLocale();
  const searchParams = useSearchParams();

  const options = {
    redirect: searchParams.get('from') === 'desktop' ? '/auth/deep-page' : props.redirect,
    option_bikaVersion: getVersion(),
    option_skipSpaceCreatable: props.ignoreAutoCreateSpace ? '1' : '0',
    option_timezone: globalContext.timezone,
    option_locale: lang,
    option_referralCode: props.referralCode,
    linkExternalUser: props.linkExternalUser ? '1' : '0',
    initExternalUser: props.initExternalUser ? '1' : '0',
  };
  return options;
}
