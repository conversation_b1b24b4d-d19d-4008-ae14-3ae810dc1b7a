import assert from 'assert';
import { createD<PERSON><PERSON>tream, JSONValue } from 'ai';
import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { sleep } from 'sharelib/sleep';
import { db, AIArtifactModel } from '@bika/server-orm';
import { AIUsage, AIArtifactError, AIArtifactState, AiNodeBO } from '@bika/types/ai/bo';
import { ArtifactType, ArtifactVO, ArtifactVOSchema, Slides } from '@bika/types/ai/vo';
import { RecordVO } from '@bika/types/database/vo';
import type { NodeResource } from '@bika/types/node/bo';
import { NodeDetailVO } from '@bika/types/node/vo';
import { AIAgentArtifactHandler } from './ai-agent-server-artifact/ai-agent-artifact-handler';
import { RecordArtifactHandler } from './database-record-server-artifact/record.artifact-handler';
import { FileArtifactHandler } from './file-artifact/file-artifact-handler';
import { ImageTextArtifactHandler } from './file-artifact/image-text-artifact-handler';
import { HTMLArtifactHandler } from './html-server-artifact/html-artifact-handler';
import { IArtifactHandler } from './interface';
import { NodeDetailArtifactHandler } from './node-detail-server-artifact/node-detail-artifact-handler';
import { NodeResourcesArtifactHandler } from './node-resources-server-artifact/node-resources-artifact-handler';
import { SlidesArtifactHandler } from './slides-server-artifact/slides-artifact-handler';
import { TextArtifactHandler } from './text-server-artifact/text-artifact-handler';
import { hasExistingStream, resumeExistingStream, createNewResumableStream } from '../ai/server/stream-context';
import { IPrompt, AIModelOptions } from '../ai/server/types';
import type { UserSO } from '../user/server/user-so';

const MAX_DURATION = 300; // seconds
export type AIArtifactDTO =
  | {
      toolCallId: string;
      type: 'html';
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
      initData?: {
        html: string;
      };

      // result?: StreamObjectResult<unknown, unknown, unknown>;
      // dataStreamWriter?: DataStreamWriter;
    }
  | {
      toolCallId: string;
      type: 'ai-agent';
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
      initData?: AiNodeBO;
      state?: AIArtifactState;

      // result?: StreamObjectResult<unknown, unknown, unknown>;
      // dataStreamWriter?: DataStreamWriter;
    }
  | {
      toolCallId: string;
      type: 'code';
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
      initData?: string;
      // result?: StreamObjectResult<unknown, unknown, unknown>;
      // dataStreamWriter?: DataStreamWriter;
    }
  | {
      type: 'text';
      toolCallId: string;
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
      initData?: string;
      error?: AIArtifactError;
      state?: AIArtifactState;
      // result?: StreamTextResult<ToolSet, never>;
      // dataStreamWriter?: DataStreamWriter;
    }
  | {
      type: 'file';
      toolCallId: string;
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
      initData?: string;
      // result?: StreamTextResult<ToolSet, never>;
      // dataStreamWriter?: DataStreamWriter;
    }
  | {
      type: 'node-resources'; // workflow
      toolCallId: string;
      initData: NodeResource[];
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
      error?: AIArtifactError;
    }
  | {
      type: 'database'; // workflow
      toolCallId: string;
      initData: NodeDetailVO[];
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
    }
  | {
      type: 'record'; // record
      toolCallId: string;
      initData: RecordVO[];
      prompt?: Pick<IPrompt, 'system' | 'prompt'>;
    }
  | {
      type: 'slides'; // slides
      toolCallId: string;
      prompt: Pick<IPrompt, 'system' | 'prompt'>;
      initData?:
        | {
            outline: {
              title: string;
              slides: Array<{
                slide_number: number;
                slide_title: string;
                slide_type: string;
                // 不影响
                slide_data: any;
                slide_description: string;
              }>;
            };
            slide_template: Array<{
              theme: {
                color_primary: string;
                color_secondary: string;
                color_text: string;
                color_accent: string;
              };
              template: string; // HTML template
            }>;
          }
        | {
            slides: Slides;
          };
    }
  | {
      type: 'image-text';
      toolCallId: string;
      initData: { text: string; imageUrl: string };
      prompt?: Pick<IPrompt, 'system' | 'prompt'>;
    };
/**
 * AI UI 相关的SO, generate UI / React / HTML component
 */
export class AIArtifactSO {
  private _model: AIArtifactModel;

  // private _prop: AIArtifactDTO;

  private _handler: IArtifactHandler;

  public get type() {
    return this._model.type as ArtifactType;
  }

  public get prompt(): Pick<IPrompt, 'system' | 'prompt'> {
    return this._model.prompt as Pick<IPrompt, 'system' | 'prompt'>;
  }

  public get model() {
    return this._model;
  }

  public get state() {
    return this._model.state;
  }

  public get data() {
    return this._model.data;
  }

  public get error() {
    return this._model.error;
  }

  private constructor(model: AIArtifactModel) {
    this._model = model;
    const artifactType = model.type;
    // , artifactType: AIArtifactDTO['type'], handler: IArtifactHandler
    // this._prop = prop;

    let handler: IArtifactHandler;
    if (artifactType === 'html') {
      handler = new HTMLArtifactHandler(this);
    } else if (artifactType === 'text') {
      handler = new TextArtifactHandler(this);
    } else if (artifactType === 'file') {
      handler = new FileArtifactHandler(this);
    } else if (artifactType === 'node-resources') {
      handler = new NodeResourcesArtifactHandler(this);
    } else if (artifactType === 'database') {
      handler = new NodeDetailArtifactHandler(this);
    } else if (artifactType === 'record') {
      handler = new RecordArtifactHandler(this);
    } else if (artifactType === 'slides') {
      handler = new SlidesArtifactHandler(this);
    } else if (artifactType === 'ai-agent') {
      handler = new AIAgentArtifactHandler(this);
    } else if (artifactType === 'image-text') {
      handler = new ImageTextArtifactHandler(this);
    } else {
      throw new Error('Unsupported artifact type');
    }

    this._handler = handler;
  }

  private static async getPO(artifactId: string) {
    const artifactPO = await db.mongo.aiArtifact.findOne({
      id: artifactId,
    });
    return artifactPO;
  }

  public static async getById(artifactId: string) {
    const artifacePO = await db.mongo.aiArtifact.findOne({
      id: artifactId,
    });
    assert(artifacePO, `AIArtifactSO not found: ${artifactId}`);
    return new AIArtifactSO(artifacePO!);
  }

  public static async getByToolCallId(toolCallId: string) {
    const artifacePO = await db.mongo.aiArtifact.findOne({
      toolCallId,
    });
    assert(artifacePO, `AIArtifactSO not found by toolCallId: ${toolCallId}`);
    return new AIArtifactSO(artifacePO!);
  }

  public static async create(user: UserSO, dto: AIArtifactDTO): Promise<AIArtifactSO> {
    const getState = () => {
      if ('error' in dto && dto.error) {
        return 'ERROR';
      }
      if ('state' in dto && dto.state) {
        return dto.state;
      }
      return 'CREATED';
    };
    const artifactPO = await db.mongo.aiArtifact.create({
      id: generateNanoID('atf'),
      state: getState(),
      type: dto.type,
      data: dto.initData,
      toolCallId: dto.toolCallId,
      prompt: dto.prompt,
      createdBy: user.id,
      updatedBy: user.id,
      error: 'error' in dto ? dto.error : undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return new AIArtifactSO(artifactPO);
  }

  public get id() {
    return this._model.id;
  }

  toVO(): ArtifactVO {
    return {
      id: this.id,
      type: this.type,
      data: this.data,
    };
  }

  // 如果传入 streamId，那么会存起来这个 streamId，方便后续 重新继续，streamId = chatId + messsageId + toolCallId
  private getStreamId(user: UserSO): string {
    const streamId = `${user.id}-${this._model.toolCallId}-${this.id}`;
    return streamId;
  }

  private async resumeExistingStream(user: UserSO) {
    const streamId = this.getStreamId(user);
    return resumeExistingStream(streamId);
  }

  /**
   * auto upsert
   *
   * @param user
   * @returns
   */
  public async resumableStream(user: UserSO) {
    const streamId = this.getStreamId(user);
    const has = await hasExistingStream(streamId);
    if (has === true) {
      return this.resumeExistingStream(user);
    }

    const newRs = await this.createResumableStream(user);
    return newRs?.stream;
  }

  private async createResumableStream(user: UserSO) {
    const streamId = this.getStreamId(user);
    const sro = await this._handler.streamResult(user);

    if (sro) {
      return {
        stream: createNewResumableStream({
          chatId: this.id, // artifactId
          streamId,
          userId: user.id,
          makeStream: () => {
            const streamResultType = sro.type;
            const artifactType = this.type;
            const artifactId = this.id;
            if (sro.type === 'text') {
              return createDataStream({
                execute: async (dataStreamWriter) => {
                  // completion text stream
                  sro.textResult.mergeIntoDataStream(dataStreamWriter);
                  let fullTxt = '';
                  for await (const delta of sro.textResult.fullStream) {
                    if (delta.type === 'text-delta') {
                      fullTxt += delta.textDelta;

                      dataStreamWriter.writeData({
                        type: 'artifact-value',
                        value: {
                          id: artifactId,
                          type: artifactType,
                          data: fullTxt,
                        },
                      });
                    }
                  }
                },
              });
            }
            if (sro.type === 'object') {
              return createDataStream({
                execute: async (dataStreamWriter) => {
                  // custom delta-object stream
                  for await (const delta of sro.objectResult.fullStream) {
                    const { type } = delta;
                    if (type === 'object') {
                      const obj = delta.object;
                      const { success, error, data } = ArtifactVOSchema.safeParse({
                        id: artifactId,
                        type: artifactType,
                        data: obj,
                      });
                      if (success && data) {
                        dataStreamWriter.writeData({
                          type: 'artifact-value',
                          value: data,
                        } as JSONValue);
                      }
                      if (error) {
                        console.warn('artifact-value schema failed. ', error.message, 'current obj: ', obj);
                      }
                    }
                  }
                },
              });
              // return sro.objectResult.textStream;
            }
            throw new Error(`Unsupported stream result type: ${streamResultType}`);
          },
        }),
        streamResult: sro,
      };
    }
    // 可能handler没有 streamResult，属于直接返回结果
    return undefined;
  }

  async getValue(user: UserSO, _options?: Pick<AIModelOptions, 'dataStreamWriter' | 'onChunk'>): Promise<ArtifactVO> {
    const streamId = this.getStreamId(user);
    if (await hasExistingStream(streamId)) {
      //  存在过了，等待执行, 上限 x 秒
      for (let i = 0; i < MAX_DURATION; i++) {
        this._model = await AIArtifactSO.getPO(this.id);
        assert(this._model);
        if (this._model.state === 'SUCCESS' || this._model.state === 'ERROR') {
          break;
        }
        // 等待其它 stream 执行成功，直接取结果
        await sleep(1000);
      }
      return this.toVO();
    }

    const resumeStream = await this.createResumableStream(user);

    await db.mongo.aiArtifact.updateOne(
      {
        id: this.id,
      },
      {
        $set: {
          state: 'STREAMING',
          updatedAt: new Date(),
          updatedBy: user.id,
        },
      },
    );

    const vo: ArtifactVO = await this._handler.getValue(
      user,
      resumeStream?.streamResult,
      // , options?.dataStreamWriter
    );
    // const vo: ArtifactVO = await this._handler.value(user, options?.dataStreamWriter);
    // SUCCESS
    await db.mongo.aiArtifact.updateOne(
      {
        id: this.id,
      },
      {
        $set: {
          type: vo.type,
          data: vo.data,
          state: 'SUCCESS',
          updatedAt: new Date(),
          updatedBy: user.id,
          usage: _.get(vo, 'usage', undefined),
        },
      },
    );
    return vo;

    // let artifactData;
    // if (this._prop.type === 'html') {
    //   const dataStreamWriter = this._prop.dataStreamWriter;
    //   const streamResult = this._prop.result;
    //   for await (const delta of streamResult.fullStream) {
    //     const { type } = delta;
    //     if (type === 'object') {
    //       if (dataStreamWriter) {
    //         dataStreamWriter.writeMessageAnnotation({
    //           type: 'artifact',
    //           artifactType: 'html',
    //           artifactId: this.id,
    //           artifactData: delta.object,
    //         } as JSONValue);
    //       }

    //       artifactData = delta.object;
    //     } else {
    //       console.warn('Unknown delta type', type, delta);
    //     }
    //   }

    //   vo = {
    //     artifactType: 'html',
    //     artifactId: this.id,
    //     artifactData,
    //   };
    // } else {
    //   vo = {
    //     artifactType: 'html',
    //     artifactId: this.id,
    //     artifactData: '',
    //   };
    // }

    // // SUCCESS
    // await db.mongo.aiArtifact.updateOne(
    //   {
    //     where: { id: this.id },
    //   },
    //   {
    //     $set: {
    //       data: artifactData,
    //       state: 'SUCCESS',
    //       updatedAt: new Date(),
    //     },
    //   },
    // );

    // return vo;
  }

  static async getUsagesByIds(artifactIds: string[]): Promise<AIUsage[]> {
    if (!artifactIds.length) {
      return [];
    }
    const artifacts = await db.mongo.aiArtifact.find(
      {
        id: { $in: artifactIds },
      },
      {
        usage: 1,
      },
    );
    return artifacts.map((a) => a.usage).filter((u) => u && u.costCredit > 0);
  }
}
