import assert from 'assert';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { WidgetRenderVO } from '@bika/types/dashboard/vo';
import InfoCircleOutlined from '@bika/ui/icons/components/info_circle_outlined';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';

interface Props {
  widget: WidgetRenderVO;
  locale: ILocaleContext;
}

export function SubscribeInfoWidgetVORenderer({ widget, locale }: Props) {
  assert(widget.type === 'SUBSCRIBE_INFO', 'Widget type must be subscribe-info-widget');
  const { t, i } = locale;

  return (
    <div className={'border-[1px] border-[--border-default] rounded-[8px] py-[10px] px-4'}>
      {/* 第一部分 */}
      <div className={'flex items-center justify-between mb-[6px]'}>
        <div className={'text-b2'}>
          <Typography
            level="body-md"
            endDecorator={
              widget.tips !== undefined ? (
                <Tooltip
                  title={i(widget.tips)}
                  variant="solid"
                  arrow
                  color="neutral"
                  placement="top"
                  sx={{ zIndex: 11111 }}
                >
                  <div>
                    <InfoCircleOutlined />
                  </div>
                </Tooltip>
              ) : (
                <></>
              )
            }
          >
            {i(widget.name)}
          </Typography>
        </div>
        <div>
          {widget.buttons?.map((button, idx) => (
            <div
              key={idx}
              className={'text-b4 text-[--brand] cursor-pointer'}
              onClick={() => {
                button.onClick();
              }}
            >
              {button.name}
            </div>
          ))}
        </div>
      </div>
      {typeof widget.content === 'function' ? widget.content() : widget.content}
    </div>
  );
}
