import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import { widgetMap } from '@bika/domains/dashboard-widgets/server-registry';
import { TemplateIdPathStore } from '@bika/domains/template/server/template-id-path-store';
import { $Enums, db, PrismaPromise } from '@bika/server-orm';
import { ChartWidgetBO, WidgetBO, WidgetLayout, WidgetType } from '@bika/types/dashboard/bo';
import { CONST_PREFIX_FIELD } from '@bika/types/database/vo';
import { WidgetModel } from './types';
import { WidgetSO } from './widget-so';

export class WidgetSOFactory {
  static determineWidget(model: WidgetModel): WidgetSO {
    const type = model.type as WidgetType;
    const HANDLER = widgetMap[type];

    if (!HANDLER) {
      throw new Error(`Widget Type ${type} is not supported yet`);
    }
    return new HANDLER(model);
  }

  static async create(dashboardId: string, widgetTpl: WidgetBO): Promise<WidgetSO> {
    const model = await db.prisma.widget.create({
      data: {
        id: generateNanoID('wdt'),
        templateId: widgetTpl.templateId,
        name: widgetTpl.name,
        dashboardId,
        type: widgetTpl.type.toString() as $Enums.WidgetType,
        bo: widgetTpl as object,
        revision: 0,
      },
    });
    return this.determineWidget(model);
  }

  public static buildWidgetBO(widget: WidgetBO, templateIdPathStore?: TemplateIdPathStore): WidgetBO {
    if (widget.type === 'CHART') {
      return this.buildChartWidgetBO(widget, templateIdPathStore);
    }
    return widget;
  }

  private static buildChartWidgetBO(widget: ChartWidgetBO, templateIdPathStore?: TemplateIdPathStore): ChartWidgetBO {
    if (widget.datasource.type === 'DATABASE') {
      // Fill in the databaseId
      let databaseId = widget.datasource.databaseId;
      if (!databaseId) {
        assert(templateIdPathStore, 'templateIdPathStore is required');
        assert(widget.datasource.databaseTemplateId, 'widget.datasource.databaseTemplateId is required');
        databaseId = templateIdPathStore.get(widget.datasource.databaseTemplateId);
      }

      // Fill in the viewId
      let viewId = widget.datasource.viewId;
      if (!viewId) {
        assert(templateIdPathStore, 'templateIdPathStore is required');
        assert(widget.datasource.viewTemplateId, 'widget.datasource.viewTemplateId is required');
        viewId = templateIdPathStore.get(widget.datasource.viewTemplateId);
      }

      return {
        ...widget,
        name: widget.name,
        type: widget.type,
        datasource: {
          ...widget.datasource,
          databaseId,
          viewId,
        },
      };
    }

    return widget;
  }

  static convertToInstanceId(widgets: WidgetBO[], convertToInstanceId: (templateId: string) => string) {
    // for pure template create new instance
    for (const widget of widgets) {
      if (widget.type === 'CHART') {
        if (widget.datasource.type === 'DATABASE') {
          const datasource = widget.datasource;
          assert(datasource.databaseTemplateId, 'widget.datasource.databaseTemplateId is required');
          datasource.databaseId = convertToInstanceId(widget.datasource.databaseTemplateId!);
          if (datasource.viewTemplateId) {
            datasource.viewId = convertToInstanceId(`${datasource.databaseTemplateId}:${datasource.viewTemplateId}`);
            datasource.viewTemplateId = undefined;
          }
          if (datasource.dimensionTemplateId) {
            if (datasource.dimensionTemplateId.startsWith(CONST_PREFIX_FIELD)) {
              datasource.dimension = convertToInstanceId(datasource.dimensionTemplateId);
            } else {
              datasource.dimension = convertToInstanceId(
                `${datasource.databaseTemplateId}:${datasource.dimensionTemplateId}`,
              );
            }
            datasource.dimensionTemplateId = undefined;
          }
          if (datasource.metrics && datasource.metrics.fieldTemplateId) {
            if (datasource.metrics.fieldTemplateId.startsWith(CONST_PREFIX_FIELD)) {
              datasource.metrics.fieldId = convertToInstanceId(datasource.metrics.fieldTemplateId);
            } else {
              datasource.metrics.fieldId = convertToInstanceId(
                `${datasource.databaseTemplateId}:${datasource.metrics.fieldTemplateId}`,
              );
            }
            datasource.metrics.fieldTemplateId = undefined;
          }
          datasource.databaseTemplateId = undefined;
        }
      } else if (widget.type === 'NUMBER') {
        if (widget.datasource.type === 'DATABASE') {
          const datasource = widget.datasource;
          assert(datasource.databaseTemplateId, 'widget.datasource.databaseTemplateId is required');
          datasource.databaseId = convertToInstanceId(widget.datasource.databaseTemplateId!);
          if (datasource.viewTemplateId) {
            datasource.viewId = convertToInstanceId(`${datasource.databaseTemplateId}:${datasource.viewTemplateId}`);
            datasource.viewTemplateId = undefined;
          }
          if (datasource.metrics && datasource.metrics.fieldTemplateId) {
            if (datasource.metrics.fieldTemplateId.startsWith(CONST_PREFIX_FIELD)) {
              datasource.metrics.fieldId = convertToInstanceId(datasource.metrics.fieldTemplateId);
            } else {
              datasource.metrics.fieldId = convertToInstanceId(
                `${datasource.databaseTemplateId}:${datasource.metrics.fieldTemplateId}`,
              );
            }
            datasource.metrics.fieldTemplateId = undefined;
          }
          datasource.databaseTemplateId = undefined;
        }
      }
    }
  }

  static replaceInstanceId(widgets: WidgetBO[], replaceInstanceId: (id: string) => string) {
    for (const widget of widgets) {
      if (widget.type === 'CHART') {
        if (widget.datasource.type === 'DATABASE') {
          const datasource = widget.datasource;
          if (datasource.databaseId) {
            datasource.databaseId = replaceInstanceId(datasource.databaseId!);
          }
          if (datasource.viewId) {
            datasource.viewId = replaceInstanceId(datasource.viewId);
          }
          if (datasource.dimension) {
            datasource.dimension = replaceInstanceId(datasource.dimension);
          }
          if (datasource.metrics && datasource.metrics.fieldId) {
            datasource.metrics.fieldId = replaceInstanceId(datasource.metrics.fieldId);
          }
        }
      } else if (widget.type === 'NUMBER') {
        if (widget.datasource.type === 'DATABASE') {
          const datasource = widget.datasource;
          if (datasource.databaseId) {
            datasource.databaseId = replaceInstanceId(datasource.databaseId!);
          }
          if (datasource.viewId) {
            datasource.viewId = replaceInstanceId(datasource.viewId);
          }
          if (datasource.metrics && datasource.metrics.fieldId) {
            datasource.metrics.fieldId = replaceInstanceId(datasource.metrics.fieldId);
          }
        }
      }
    }
  }

  static async getWidgetsByDashboardId(dashboardId: string): Promise<WidgetSO[]> {
    const widgets = await db.prisma.widget.findMany({
      where: {
        dashboardId,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
    return widgets.map((widget) => this.determineWidget(widget));
  }

  static async findByTemplateId(templateId: string): Promise<WidgetSO | null> {
    const model = await db.prisma.widget.findFirst({
      where: {
        templateId,
      },
    });
    return model && this.determineWidget(model);
  }

  static async findWidgetById(widgetId: string): Promise<WidgetSO> {
    const widgetModel = await db.prisma.widget.findFirst({
      where: {
        id: widgetId,
      },
    });
    if (!widgetModel) {
      throw new Error(`Cannot find the widget by widgetId: ${widgetId}`);
    }
    return this.determineWidget(widgetModel);
  }

  static async updateWidgets(
    userId: string,
    widgets: { id: string; layout?: WidgetLayout }[],
  ): Promise<PrismaPromise<WidgetModel>[]> {
    const operations: PrismaPromise<WidgetModel>[] = [];
    if (!widgets) {
      return operations;
    }
    const widgetPOList = await db.prisma.widget.findMany({
      where: {
        id: {
          in: widgets.map((widget) => widget.id),
        },
      },
    });
    for (const widget of widgetPOList) {
      const widgetBO = widget.bo as WidgetBO;
      operations.push(
        db.prisma.widget.update({
          where: {
            id: widget.id,
          },
          data: {
            bo: {
              ...widgetBO,
              layout: widgets.find((w) => w.id === widget.id)?.layout,
            },
            updatedBy: userId,
          },
        }),
      );
    }
    return operations;
  }
}
