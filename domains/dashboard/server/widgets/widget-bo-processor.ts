/* eslint-disable max-classes-per-file */
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { DatabaseBOProcessor } from '@bika/domains/database/server/database-bo-processor';
import { RecordBOProcessor } from '@bika/domains/database/server/record/record-bo-processor';
import { ViewBOProcessor } from '@bika/domains/database/server/views/view-bo-processor';
import {
  ChartWidgetBO,
  EChartRenderVO,
  NumberWidgetBO,
  TextWidget,
  WidgetBO,
  WidgetDatasourceTypeSchema,
} from '@bika/types/dashboard/bo';
import { ChartWidgetVO, NumberWidgetVO, WidgetVO } from '@bika/types/dashboard/vo';
import { CONST_PREFIX_WIDGET } from '@bika/types/database/vo';
import { BORenderOpts, IBOProcessor } from '@bika/types/node/vo';
import { iStringParse } from '@bika/types/system';

export abstract class WidgetBOProcessor<T extends WidgetBO> implements IBOProcessor<T> {
  private _widget: T;

  constructor(bo: T) {
    this._widget = _.cloneDeep(bo);
    if (!this._widget.id) {
      this._widget.id = generateNanoID(CONST_PREFIX_WIDGET);
    }
  }

  get bo(): T {
    return this._widget;
  }

  get id(): string {
    return this.bo.id!;
  }

  getDatabase(opts?: BORenderOpts): DatabaseBOProcessor | undefined {
    const { getProcessor } = opts || {};
    if (this.bo.type === 'NUMBER' || this.bo.type === 'CHART') {
      if (this.bo.datasource.type === WidgetDatasourceTypeSchema.enum.DATABASE) {
        const databaseKey = this.bo.datasource.databaseId || this.bo.datasource.databaseTemplateId;
        if (databaseKey && getProcessor) {
          return getProcessor(databaseKey) as DatabaseBOProcessor;
        }
      }
    }
    return undefined;
  }

  getView(database: DatabaseBOProcessor): ViewBOProcessor | undefined {
    if (this.bo.type === 'NUMBER') {
      if (this.bo.datasource.type === WidgetDatasourceTypeSchema.enum.DATABASE) {
        const viewKey = this.bo.datasource.viewId || this.bo.datasource.viewTemplateId;
        if (viewKey) {
          return database.getView(viewKey) as ViewBOProcessor;
        }
      }
    }
    return undefined;
  }

  toVO<V = WidgetVO>(opts?: BORenderOpts): V {
    return {
      id: this.id,
      name: iStringParse(this.bo.name, opts?.locale),
      description: iStringParse(this.bo.description, opts?.locale),
      type: this.bo.type,
      datasource: _.get(this.bo, 'datasource', {}),
      settings: _.get(this.bo, 'settings', {}),
    } as V;
  }
}
export class DefaultWidgetBOProcessor extends WidgetBOProcessor<WidgetBO> {}

export class ChartWidgetBOProcessor extends WidgetBOProcessor<ChartWidgetBO> {
  datasourceVO(database?: DatabaseBOProcessor) {
    const datasource = _.cloneDeep(this.bo.datasource);
    if (datasource.type === WidgetDatasourceTypeSchema.enum.DATABASE) {
      datasource.databaseId = database?.id;
      if (database) {
        const view = super.getView(database);
        datasource.viewId = view?.id;
        if (datasource.metrics && datasource.metricsType === 'AGGREGATION_BY_FIELD') {
          const field = database.getField(datasource.metrics.fieldId || datasource.metrics.fieldTemplateId!);
          datasource.metrics.fieldId = field?.getId();
        }
        const dimensionField = database.getField(datasource.dimension || datasource.dimensionTemplateId!);
        datasource.dimension = dimensionField?.getId();
      }
    }
    return datasource;
  }

  loadValueFromDatabase(database: DatabaseBOProcessor): EChartRenderVO {
    const datasource = this.bo.datasource;

    if (datasource.type === WidgetDatasourceTypeSchema.enum.DATABASE) {
      const records = database.records;
      const dimensionField = database.getField(datasource.dimension || datasource.dimensionTemplateId!);
      const dimensionFieldBO = dimensionField?.getBO();
      const { metrics, metricsType, chartType } = datasource;
      const { isSepareted } = this.bo.settings || {};

      const getDimensionValue = (record: RecordBOProcessor): string[] => {
        const dimensionValue = record.getData(dimensionFieldBO?.id, dimensionFieldBO?.templateId);
        if (typeof dimensionValue === 'string') {
          return [dimensionValue];
        }
        if (Array.isArray(dimensionValue)) {
          if (!isSepareted) {
            return [dimensionValue.join(',')];
          }
          return dimensionValue as string[];
        }
        return [];
      };

      if (metricsType === 'COUNT_RECORDS') {
        const dimensionRecords = records.reduce(
          (acc, record) => {
            const dimensionValues = getDimensionValue(record);
            dimensionValues.forEach((v) => {
              acc[v] = (acc[v] || 0) + 1;
            });
            return acc;
          },
          {} as Record<string, number>,
        );

        return {
          xAxis: { type: 'category', data: Object.keys(dimensionRecords) },
          yAxis: { type: 'value' },
          series: [{ type: 'line', data: Object.values(dimensionRecords) }],
        };
      }
      if (metricsType === 'AGGREGATION_BY_FIELD' && metrics) {
        const { aggregationType, fieldId, fieldTemplateId } = metrics;
        const field = database.getField(fieldId || fieldTemplateId!);
        const fieldBO = field?.getBO();
        const callRecords = records.map((record) => {
          const dimensionValue = getDimensionValue(record);
          const metricValue = record.getData(fieldBO?.id, fieldBO?.templateId);
          return {
            dimensionValue,
            metricValue: typeof metricValue === 'number' ? metricValue : 0,
          };
        });
        let values: Record<string, number> = {};
        switch (aggregationType) {
          case 'SUM':
            values = callRecords.reduce(
              (acc, record) => {
                record.dimensionValue.forEach((v) => {
                  acc[v] = (acc[v] || 0) + record.metricValue;
                });
                return acc;
              },
              {} as Record<string, number>,
            );
            break;
          case 'MIN':
            values = callRecords.reduce(
              (acc, record) => {
                record.dimensionValue.forEach((v) => {
                  acc[v] = Math.min(acc[v] || 0, record.metricValue);
                });
                return acc;
              },
              {} as Record<string, number>,
            );
            break;
          case 'MAX':
            values = callRecords.reduce(
              (acc, record) => {
                record.dimensionValue.forEach((v) => {
                  acc[v] = Math.max(acc[v] || 0, record.metricValue);
                });
                return acc;
              },
              {} as Record<string, number>,
            );
            break;
          case 'AVG':
            {
              const dementionCount: Record<string, number> = {};
              values = callRecords.reduce(
                (acc, record) => {
                  record.dimensionValue.forEach((v) => {
                    acc[v] = (acc[v] || 0) + record.metricValue;
                    dementionCount[v] = (dementionCount[v] || 0) + 1;
                  });
                  return acc;
                },
                {} as Record<string, number>,
              );
              values = Object.fromEntries(
                Object.entries(values).map(([v, value]) => [v, value / (dementionCount[v] || 1)]),
              );
            }
            break;
          default:
            break;
        }
        if (Object.keys(values).length > 0) {
          return {
            xAxis: { type: 'category', data: Object.keys(values) },
            yAxis: { type: 'value' },
            series: [{ type: chartType, data: Object.values(values) }],
          };
        }
      }
    }
    return {
      xAxis: { type: 'category', data: ['example1', 'example2', 'example3'] },
      yAxis: { type: 'value' },
      series: [{ type: 'line', data: [1, 2, 3] }],
    };
  }

  override toVO<V = ChartWidgetVO>(_opts?: BORenderOpts): V {
    const vo = super.toVO<ChartWidgetVO>(_opts);
    const database = super.getDatabase(_opts);
    vo.datasource = this.datasourceVO(database);

    if (database) {
      vo.value = this.loadValueFromDatabase(database);
    }
    return vo as V;
  }
}

export class NumberWidgetBOProcessor extends WidgetBOProcessor<NumberWidgetBO> {
  datasourceVO(database?: DatabaseBOProcessor) {
    const datasource = _.cloneDeep(this.bo.datasource);
    if (datasource.type === WidgetDatasourceTypeSchema.enum.DATABASE) {
      datasource.databaseId = database?.id;
      if (database) {
        const view = super.getView(database);
        datasource.viewId = view?.id;
        if (datasource.metrics && datasource.metricsType === 'AGGREGATION_BY_FIELD') {
          const field = database.getField(datasource.metrics.fieldId || datasource.metrics.fieldTemplateId!);
          datasource.metrics.fieldId = field?.getId();
        }
      }
    }
    return datasource;
  }

  loadValueFromDatabase(database: DatabaseBOProcessor): string {
    if (this.bo.datasource.type === WidgetDatasourceTypeSchema.enum.DATABASE) {
      const records = database.records;

      const { metrics, metricsType } = this.bo.datasource;

      if (metricsType === 'COUNT_RECORDS') {
        return database.records.length.toString();
      }

      if (metricsType === 'AGGREGATION_BY_FIELD' && metrics) {
        const field = database.getField(metrics.fieldId || metrics.fieldTemplateId!);
        const fieldBO = field?.getBO();

        const getNumberValue = (record: RecordBOProcessor): number => {
          const data = record.getData(fieldBO?.id, fieldBO?.templateId);
          return typeof data === 'number' ? data : 0;
        };

        const values = records.map(getNumberValue);

        switch (metrics?.aggregationType) {
          case 'SUM':
            return values.reduce((a, b) => a + b, 0).toString();
          case 'MIN': {
            const min = Math.min(...values);
            return Number.isFinite(min) ? min.toString() : '0';
          }
          case 'MAX': {
            const max = Math.max(...values);
            return Number.isFinite(max) ? max.toString() : '0';
          }
          case 'AVG': {
            const sum = values.reduce((a, b) => a + b, 0);
            return values.length > 0 ? (sum / values.length).toString() : '0';
          }
          case 'NOT_FILLED':
            return values.filter((v) => v === 0).length.toString();
          case 'FILLED':
            return values.filter((v) => v !== 0).length.toString();
          default:
            return '0';
        }
      }
    }
    return '0';
  }

  override toVO<V = NumberWidgetVO>(opts?: BORenderOpts): V {
    const vo = super.toVO<NumberWidgetVO>(opts);
    const value = { value: '0', targetValue: this.bo.targetValue?.toString() };

    if (this.bo.datasource.type === WidgetDatasourceTypeSchema.enum.CUSTOM) {
      value.value = this.bo.datasource.number.toString();
    }
    const database = super.getDatabase(opts);
    if (database) {
      const calValue = this.loadValueFromDatabase(database);
      vo.datasource = this.datasourceVO(database);
      value.value = calValue;
    }

    vo.value = value;
    return vo as V;
  }
}

export class TextWidgetBOProcessor extends WidgetBOProcessor<TextWidget> {
  override toVO<V>(_opts?: BORenderOpts): V {
    return super.toVO(_opts) as V;
  }
}

export class WidgetBOProcessorFactory {
  static getProcessor<T extends WidgetBO>(bo: T): WidgetBOProcessor<T> {
    switch (bo.type) {
      case 'CHART':
        return new ChartWidgetBOProcessor(bo) as unknown as WidgetBOProcessor<T>;
      case 'NUMBER':
        return new NumberWidgetBOProcessor(bo) as unknown as WidgetBOProcessor<T>;
      case 'TEXT':
        return new TextWidgetBOProcessor(bo) as unknown as WidgetBOProcessor<T>;
      default:
        return new DefaultWidgetBOProcessor(bo) as unknown as WidgetBOProcessor<T>;
    }
  }
}
