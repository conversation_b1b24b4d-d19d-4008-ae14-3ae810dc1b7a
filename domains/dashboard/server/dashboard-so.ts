import _ from 'lodash';
import { generateNanoID } from 'basenext/utils/nano-id';
import { TemplateFolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { IRelationIdOpts, NodeResourceSO } from '@bika/domains/node/server/types';
import { $Enums, db, Prisma, PrismaPromise } from '@bika/server-orm';
import { WidgetBO, Dashboard, WidgetType } from '@bika/types/dashboard/bo';
import { DashboardCreateBO, DashboardUpdateBO } from '@bika/types/dashboard/dto';
import { DashboardVO } from '@bika/types/dashboard/vo';
import { CONST_PREFIX_DSB } from '@bika/types/database/vo';
import type { ExportBOOptions, NodeResourceType } from '@bika/types/node/bo';
import { ToTemplateOptions } from '@bika/types/node/bo';
import { NodeRenderOpts } from '@bika/types/node/vo';
import { iString, iStringParse, LocaleType } from '@bika/types/system';
import { DashboardIncludeNode, DashboardModel } from './types';
import { WidgetSOFactory } from './widgets/factory';
import { WidgetModel } from './widgets/types';
import { WidgetSO } from './widgets/widget-so';

export class DashboardSO extends NodeResourceSO {
  private readonly _model: DashboardModel;

  private _widgets?: WidgetSO[];

  private constructor(model: DashboardModel, widgets?: WidgetSO[]) {
    super();
    this._model = model;
    this._widgets = widgets;
  }

  get resourceType(): NodeResourceType {
    return 'DASHBOARD';
  }

  get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get spaceId() {
    return this.model.node.spaceId;
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get name(): iString {
    return this.model.name as iString;
  }

  get description(): iString | undefined {
    return (this.model.description as iString) || undefined;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  toNodeSO() {
    return NodeSO.initWithModel(this.model.node);
  }

  async updateWithNodeInput(
    userId: string,
    param: DashboardUpdateBO,
  ): Promise<PrismaPromise<DashboardModel | WidgetModel>[]> {
    const operations: PrismaPromise<DashboardModel | WidgetModel>[] = [];
    operations.push(
      db.prisma.dashboard.update({
        where: {
          id: this.id,
        },
        data: {
          name: param.name,
          description: param.description,
          updatedBy: userId,
          node: {
            update: {
              name: param.name,
              description: param.description,
              updatedBy: userId,
            },
          },
        } as Prisma.DashboardUpdateInput,
        include: {
          node: true,
        },
      }),
    );
    if (param.widgets) {
      operations.push(...(await WidgetSOFactory.updateWidgets(userId, param.widgets)));
    }
    return operations;
  }

  /**
   * Create a dashboard operation with template
   * @param userId user id
   * @param createParam create parameters
   * @param dashboardTemplate dashboard template
   */
  static createDashboardOperationWithTemplate(
    userId: string,
    createParam: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
    },
    dashboardTemplate: Dashboard,
  ): { id: string; operation: PrismaPromise<DashboardModel> } {
    const { id, input } = this.buildDashboardCreateInputWithTemplate(userId, createParam, dashboardTemplate);
    const operation = db.prisma.dashboard.create({
      data: input,
      include: DashboardIncludeNode,
    });
    return { id, operation };
  }

  override async toBO(): Promise<Dashboard> {
    const widgets = await this.getWidgets();
    return {
      id: this.id,
      templateId: this.templateId,
      resourceType: $Enums.NodeResourceType.DASHBOARD,
      icon: this.toNodeSO().icon,
      description: this.description,
      name: this._model.name as iString,
      widgets: widgets.map((w) => w.toBO()),
    };
  }

  override async export(opts?: ExportBOOptions): Promise<Dashboard> {
    const widgets = await this.getWidgets();
    const widgetBOList: WidgetBO[] = widgets.map((w) => w.exportBO(opts));
    const dashboard = await this.toBO();
    return { ...dashboard, widgets: widgetBOList };
  }

  override async toTemplate(opts?: ToTemplateOptions): Promise<Dashboard> {
    const widgets = await this.getWidgets();
    const widgetBOList: WidgetBO[] = widgets.map((w) => w.toTemplate(opts));
    const dashboard = _.omit(await this.toBO(), 'id') as Dashboard;
    if (!dashboard.templateId) {
      dashboard.templateId = this.id;
    }
    return { ...dashboard, widgets: widgetBOList };
  }

  override async setTemplateId() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = this.setTemplateIdWithIdOperation();
    const widgets = await this.getWidgets();
    for (const widget of widgets) {
      operations.push(...widget.publish());
    }
    return operations;
  }

  /**
   * Build dashboard create input with template
   * @param userId user id
   * @param params create parameters
   * @param dashboardTemplate dashboard template
   */
  private static buildDashboardCreateInputWithTemplate(
    userId: string,
    params: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      unitId?: string;
    },
    dashboardTemplate: Dashboard,
  ): { id: string; input: Prisma.DashboardCreateInput } {
    const { spaceId, parentId, preNodeId, nextNodeId, unitId } = params;
    const id = dashboardTemplate.id ?? generateNanoID(CONST_PREFIX_DSB);
    const dashboardCreateInput: Prisma.DashboardCreateInput = {
      templateId: dashboardTemplate.templateId,
      name: dashboardTemplate.name,
      description: dashboardTemplate.description,
      revision: 0,
      createdBy: userId,
      updatedBy: userId,
      node: {
        create: {
          id,
          templateId: dashboardTemplate.templateId,
          name: dashboardTemplate.name,
          description: dashboardTemplate.description,
          icon: dashboardTemplate.icon,
          type: $Enums.NodeResourceType.DASHBOARD,
          createdBy: userId,
          updatedBy: userId,
          unit: unitId ? { connect: { id: unitId } } : undefined,
          space: {
            connect: {
              id: spaceId,
            },
          },
          parent: {
            connect: {
              id: parentId,
            },
          },
          preNode: {
            connect: preNodeId
              ? {
                  id: preNodeId,
                }
              : undefined,
          },
          nextNode: {
            connect: nextNodeId
              ? {
                  id: nextNodeId,
                }
              : undefined,
          },
        },
      },
      widgets: {
        createMany: {
          data: dashboardTemplate.widgets?.map((widgetTpl: WidgetBO) => ({
            id: generateNanoID('wdt'),
            templateId: widgetTpl.templateId,
            name: widgetTpl.name,
            type: widgetTpl.type as $Enums.WidgetType,
            bo: widgetTpl as object,
            revision: 0,
          })),
        },
      },
    };
    return { id, input: dashboardCreateInput };
  }

  async getWidgets(): Promise<WidgetSO[]> {
    if (!this._widgets) {
      this._widgets = await WidgetSOFactory.getWidgetsByDashboardId(this.id);
    }
    return this._widgets;
  }

  async getWidget(widgetId: string): Promise<WidgetSO | undefined> {
    const widgets = await this.getWidgets();
    return widgets.find((w) => w.id === widgetId);
  }

  async getWidgetByType(type: WidgetType): Promise<WidgetSO[]> {
    const widgets = await this.getWidgets();
    return widgets.filter((w) => w.type === type);
  }

  async createWidget(widgetTpl: WidgetBO): Promise<WidgetSO> {
    const newWidget = await WidgetSOFactory.create(this.id, widgetTpl);
    // update widgets cache
    const widgets = await this.getWidgets();
    widgets.push(newWidget);
    return newWidget;
  }

  public static async init(id: string): Promise<DashboardSO> {
    const model = await db.prisma.dashboard.findUnique({
      where: {
        id,
      },
      include: DashboardIncludeNode,
    });
    if (!model) {
      throw new Error(`Dashboard not found: ${id}`);
    }
    return new DashboardSO(model);
  }

  static async findDashboard(param: {
    templateNodeId?: string;
    dashboardId?: string;
    dashboardTemplateId?: string;
  }): Promise<DashboardSO> {
    const { templateNodeId, dashboardId, dashboardTemplateId } = param;
    return TemplateFolderSO.findNodeResourceByKey<DashboardSO>(
      {
        nodeId: dashboardId,
        nodeTemplateId: dashboardTemplateId,
        templateNodeId,
      },
      'dashboard',
    );
  }

  async toVO(opts?: NodeRenderOpts): Promise<DashboardVO> {
    const widgets = await this.getWidgets();
    const widgetVOs = await Promise.all(widgets.map((w) => w.toVO(opts)));
    return {
      id: this.id,
      templateId: this.templateId,
      name: iStringParse(this.name, opts?.locale),
      description: iStringParse(this.description, opts?.locale),
      widgets: widgetVOs,
    };
  }

  override relationInstanceId(dashboard: Dashboard, opts: IRelationIdOpts): boolean {
    const { convertToInstanceId, replaceInstanceId } = opts;
    // for pure template create new instance
    if (convertToInstanceId) {
      WidgetSOFactory.convertToInstanceId(dashboard.widgets, convertToInstanceId);
    }
    if (replaceInstanceId) {
      WidgetSOFactory.replaceInstanceId(dashboard.widgets, replaceInstanceId);
    }
    return true;
  }

  static boToCreateInput(
    userId: string,
    data: DashboardCreateBO,
  ): Prisma.DashboardUncheckedCreateNestedOneWithoutNodeInput | undefined {
    return $Enums.NodeResourceType.DASHBOARD === data.resourceType
      ? {
          create: {
            revision: 0,
            name: data.name,
            description: data.description,
            templateId: data.templateId,
            createdBy: userId,
            updatedBy: userId,
            widgets: data.widgets
              ? {
                  createMany: {
                    data: data.widgets?.map((widgetTpl: WidgetBO) => ({
                      id: widgetTpl.id || generateNanoID('wdt'),
                      templateId: widgetTpl.templateId,
                      name: widgetTpl.name,
                      type: widgetTpl.type as $Enums.WidgetType,
                      bo: widgetTpl as object,
                      revision: 0,
                    })),
                  },
                }
              : undefined,
          },
        }
      : undefined;
  }

  private setTemplateIdWithIdOperation() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.dashboard.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
        ],
      );
    }
    return operations;
  }
}
