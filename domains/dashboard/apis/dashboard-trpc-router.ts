import { z } from 'zod';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import { WidgetCreateDTOSchema, WidgetUpdateDTOSchema } from '@bika/types/dashboard/dto';
import * as DashboardController from './dashboard-controller';

export const dashboardRouter = router({
  wigets: protectedProcedure
    .input(
      z.object({
        widgetId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      return DashboardController.fetchWidget(input.widgetId, ctx);
    }),

  fetchWidgetBO: protectedProcedure
    .input(
      z.object({
        widgetId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      return DashboardController.fetchWidgetBO(input.widgetId);
    }),

  fetchDashboard: protectedProcedure
    .input(
      z.object({
        dashboardId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      return DashboardController.fetchDashboard(input.dashboardId);
    }),

  createWidget: protectedProcedure.input(WidgetCreateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DashboardController.createWidget(user, input);
  }),

  updateWidget: protectedProcedure.input(WidgetUpdateDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return DashboardController.updateWidget(user, input);
  }),

  deleteWidget: protectedProcedure
    .input(
      z.object({
        dashboardId: z.string(),
        widgetId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return DashboardController.deleteWidget(user, input);
    }),
});
