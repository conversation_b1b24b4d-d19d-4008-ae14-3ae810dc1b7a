import { z } from 'zod';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import { ManualTriggerOutputSchema } from '@bika/types/automation/bo';
import {
  AutomationActionCreateDTOSchema,
  AutomationActionTestDTOSchema,
  AutomationActionUpdateDTOSchema,
  AutomationManuallyRunDTOSchema,
  AutomationTriggerCreateDTOSchema,
  AutomationTriggerTestDTOSchema,
  AutomationTriggerUpdateDTOSchema,
  RunHistoryListDTOSchema,
} from '@bika/types/automation/dto';
import { AutomationRunHistoryPageVOSchema } from '@bika/types/automation/vo';
import * as AutomationController from './automation-controller';

export const automationRouter = router({
  /**
   * enable automation.
   */
  enabledAutomation: protectedProcedure.input(z.object({ automationId: z.string() })).mutation(async (opts) => {
    const { input } = opts;
    return AutomationController.enabledAutomation(input.automationId);
  }),

  /**
   * disable automation
   */
  disabledAutomation: protectedProcedure.input(z.object({ automationId: z.string() })).mutation(async (opts) => {
    const { input } = opts;
    return AutomationController.disabledAutomation(input.automationId);
  }),

  fetchVariables: protectedProcedure.input(z.object({ automationId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return AutomationController.fetchVariables(user, input.automationId);
  }),

  /**
   * trigger manually
   */
  triggerManually: protectedProcedure
    .input(AutomationManuallyRunDTOSchema)
    .output(ManualTriggerOutputSchema.pick({ result: true }))
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      return AutomationController.triggerManually(ctx.session!.userId, input);
    }),

  /**
   * add trigger
   */
  addTrigger: protectedProcedure.input(AutomationTriggerCreateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return AutomationController.addTrigger(user, input.automationId, input.trigger);
  }),

  /**
   * delete trigger
   */
  deleteTrigger: protectedProcedure.input(z.object({ triggerId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    // todo permission check
    return AutomationController.deleteTrigger(ctx.session!.userId, input.triggerId);
  }),

  /**
   * get trigger
   */
  getTrigger: protectedProcedure.input(z.object({ triggerId: z.string() })).query(async (opts) => {
    const { input } = opts;
    return AutomationController.getTrigger(input.triggerId);
  }),

  /**
   * update trigger
   */
  updateTrigger: protectedProcedure.input(AutomationTriggerUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return AutomationController.updateTrigger(user, input);
  }),

  testTrigger: protectedProcedure.input(AutomationTriggerTestDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return AutomationController.testTrigger(user, input.triggerId);
  }),

  /**
   * add action
   */
  addAction: protectedProcedure.input(AutomationActionCreateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return AutomationController.addAction(ctx.session!.userId, input);
  }),

  /**
   * delete action
   */
  deleteAction: protectedProcedure.input(z.object({ actionId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    // todo permission check
    return AutomationController.deleteAction(ctx.session!.userId, input.actionId);
  }),

  /**
   * update action
   */
  updateAction: protectedProcedure.input(AutomationActionUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    return AutomationController.updateAction(ctx.session!.userId, input);
  }),

  testAction: protectedProcedure.input(AutomationActionTestDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return AutomationController.testAction(user, input);
  }),

  runHistoryList: protectedProcedure
    .output(AutomationRunHistoryPageVOSchema)
    .input(RunHistoryListDTOSchema)
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return AutomationController.runHistoryList(user, input);
    }),

  fetchRunHistoryDetail: protectedProcedure
    // AutomationRunHistoryDetailVOSchema
    .output(z.any())
    .input(z.object({ id: z.string() }))
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return AutomationController.getAutomationRunHistoryDetail(user, input.id);
    }),

  cancelDelayAutomation: protectedProcedure.input(z.object({ runHistoryId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return AutomationController.cancelDelayAutomation({ user, runHistoryId: input.runHistoryId });
  }),

  // fetchFormAppAIAccountToken: protectedProcedure
  //   .output(z.string())
  //   .input(z.object({ spaceId: z.string() }))
  //   .query(async (opts) => {
  //     const { input, ctx } = opts;
  //     const { userId } = ctx.session!;
  //     const user = await UserSO.init(userId);
  //     return AutomationController.fetchFormAppAIAccountToken(user, input.spaceId);
  //   }),

  fetchToolSDKAIAccountToken: protectedProcedure
    .output(z.string())
    .input(z.object({ spaceId: z.string() }))
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return AutomationController.fetchToolSDKAIAccountToken(user, input.spaceId);
    }),
});
