import { Action, ActionType } from '@bika/types/automation/bo';
import {
  ActionBOProcessor,
  DelayActionBOProcessor,
  AISummaryActionBOProcessor,
  CallAgentActionBOProcessor,
  SendEmailActionBOProcessor,
  WebhookActionBOProcessor,
  XCreateTweetActionBOProcessor,
  CreateMissionActionBOProcessor,
  FindMembersActionBOProcessor,
  FindMissionsActionBOProcessor,
  FormappAIActionBOProcessor,
  DingtalkWebhookActionBOProcessor,
  FeishuWebhookActionBOProcessor,
  SlackWebhookActionBOProcessor,
  TelegramWebhookActionBOProcessor,
  WecomWebhookActionBOProcessor,
  FindDashboardActionBOProcessor,
  RandomActionBOProcessor,
  RoundRobinActionBOProcessor,
  RunScriptActionBOProcessor,
  SendReportActionBOProcessor,
  TwitterUploadMediaActionBOProcessor,
  DummyActionBOProcessor,
  FilterActionBOProcessor,
  FindWidgetActionBOProcessor,
  OpenAIGenerateTextActionBOProcessor,
} from './action-bo-processor';
import { CreateRecordActionBOProcessor } from './create-record-action-bo-processort';
import { FindRecordsActionBOProcessor } from './find-record-action-bo-processor';
import { LoopActionBOProcessor } from './loop-action-bo-processor';
import { UpdateRecordActionBOProcessor } from './update-record-action-bo-processor';

// Define a type for the processor constructor
type ProcessorConstructor = new (action: Action) => ActionBOProcessor<Action>;

export class ActionBOFactory {
  private static readonly processorMap: Record<ActionType, ProcessorConstructor> = {
    DELAY: DelayActionBOProcessor,
    AI_SUMMARY: AISummaryActionBOProcessor,
    CALL_AGENT: CallAgentActionBOProcessor,
    SEND_EMAIL: SendEmailActionBOProcessor,
    FIND_RECORDS: FindRecordsActionBOProcessor as unknown as ProcessorConstructor,
    UPDATE_RECORD: UpdateRecordActionBOProcessor as unknown as ProcessorConstructor,
    CREATE_RECORD: CreateRecordActionBOProcessor as unknown as ProcessorConstructor,
    LOOP: LoopActionBOProcessor as unknown as ProcessorConstructor,
    WEBHOOK: WebhookActionBOProcessor,
    DINGTALK_WEBHOOK: DingtalkWebhookActionBOProcessor,
    FEISHU_WEBHOOK: FeishuWebhookActionBOProcessor,
    SLACK_WEBHOOK: SlackWebhookActionBOProcessor,
    TELEGRAM_SEND_MESSAGE: TelegramWebhookActionBOProcessor,
    WECOM_WEBHOOK: WecomWebhookActionBOProcessor,
    X_CREATE_TWEET: XCreateTweetActionBOProcessor,
    CREATE_MISSION: CreateMissionActionBOProcessor,
    FIND_MEMBERS: FindMembersActionBOProcessor,
    FIND_MISSIONS: FindMissionsActionBOProcessor,
    FORMAPP_AI: FormappAIActionBOProcessor,
    FILTER: FilterActionBOProcessor,
    FIND_DASHBOARD: FindDashboardActionBOProcessor,
    FIND_WIDGET: FindWidgetActionBOProcessor,
    OPENAI_GENERATE_TEXT: OpenAIGenerateTextActionBOProcessor,
    DEEPSEEK: DummyActionBOProcessor,
    AI_MODEL: DummyActionBOProcessor,
    RANDOM: RandomActionBOProcessor,
    ROUND_ROBIN: RoundRobinActionBOProcessor,
    RUN_SCRIPT: RunScriptActionBOProcessor,
    SEND_REPORT: SendReportActionBOProcessor,
    TWITTER_UPLOAD_MEDIA: TwitterUploadMediaActionBOProcessor,
    DUMMY_ACTION: DummyActionBOProcessor,
    TOOLSDK_AI: DummyActionBOProcessor,
    CREATE_DOCUMENT: DummyActionBOProcessor,
    REPLACE_FILE: DummyActionBOProcessor,
    CREATE_NODE_RESOURCE: DummyActionBOProcessor,
    CONDITION: DummyActionBOProcessor,
  };

  static newProcessor(action: Action): ActionBOProcessor<Action> {
    const ProcessorClass = this.processorMap[action.actionType];
    if (!ProcessorClass) {
      throw new Error(`Action type ${action.actionType} processor not supported`);
    }
    return new ProcessorClass(action);
  }
}
