// eslint-disable-next-line max-classes-per-file
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { Action, CreateRecordAction, FindRecordsAction, UpdateRecordAction } from '@bika/types/automation/bo';
import { AutomationActionVO } from '@bika/types/automation/vo';
import { CONST_PREFIX_ACTION } from '@bika/types/database/vo';
import { BORenderOpts, IBOProcessor } from '@bika/types/node/vo';
import { iStringParse } from '@bika/types/system';
import { DatabaseBOProcessor } from '../../../../database/server/database-bo-processor';

export abstract class ActionBOProcessor<T extends Action> implements IBOProcessor<T> {
  private _action: T;

  constructor(action: T) {
    this._action = _.cloneDeep(action);
    if (!this._action.id) {
      this._action.id = generateNanoID(CONST_PREFIX_ACTION);
    }
  }

  get bo(): T {
    return this._action;
  }

  get id(): string {
    return this.bo.id!;
  }

  toVO<V>(_opts?: BORenderOpts): V | Promise<V> {
    throw new Error(`${this.bo.actionType} action toVO is not implemented`);
  }

  protected toSimpleVO(opts?: BORenderOpts): Omit<AutomationActionVO, 'bo'> {
    const { locale } = opts ?? {};
    return {
      id: this.id,
      type: this.bo.actionType,
      description: iStringParse(this.bo.description, locale),
      isVerified: true,
    };
  }
}

/**
 * BO不需要特殊处理, 直接继承BaseActionBOProcessor,
 * 这个类中定义了toVO方法, 用于将BO转换为VO
 * ps: 不要export这个类, 如果要重写toVO方法, 请继承ActionBOProcessor
 */
abstract class BaseActionBOProcessor extends ActionBOProcessor<Action> {
  override toVO<V = AutomationActionVO>(_opts?: BORenderOpts): V {
    const vo: AutomationActionVO = {
      ...super.toSimpleVO(_opts),
      bo: this.bo,
    };
    return vo as V;
  }
}

// 以下action的BO不需要特殊处理, 直接继承BaseActionBOProcessor.
export class AISummaryActionBOProcessor extends BaseActionBOProcessor {}
export class CallAgentActionBOProcessor extends BaseActionBOProcessor {}
export class DelayActionBOProcessor extends BaseActionBOProcessor {}
export class SendEmailActionBOProcessor extends BaseActionBOProcessor {}
export class XCreateTweetActionBOProcessor extends BaseActionBOProcessor {}
export class CreateMissionActionBOProcessor extends BaseActionBOProcessor {}
export class FindMembersActionBOProcessor extends BaseActionBOProcessor {}
export class FindMissionsActionBOProcessor extends BaseActionBOProcessor {}
export class FormappAIActionBOProcessor extends BaseActionBOProcessor {}
export class ToolSDKAIActionBOProcessor extends BaseActionBOProcessor {}
export class FilterActionBOProcessor extends BaseActionBOProcessor {}
export class FindDashboardActionBOProcessor extends BaseActionBOProcessor {}
export class FindWidgetActionBOProcessor extends BaseActionBOProcessor {}
export class OpenAIGenerateTextActionBOProcessor extends BaseActionBOProcessor {}
export class RandomActionBOProcessor extends BaseActionBOProcessor {}
export class RoundRobinActionBOProcessor extends BaseActionBOProcessor {}
export class RunScriptActionBOProcessor extends BaseActionBOProcessor {}
export class SendReportActionBOProcessor extends BaseActionBOProcessor {}
export class TwitterUploadMediaActionBOProcessor extends BaseActionBOProcessor {}
export class UpdateRecordActionBOProcessor extends BaseActionBOProcessor {}
export class WebhookActionBOProcessor extends BaseActionBOProcessor {}
export class DingtalkWebhookActionBOProcessor extends BaseActionBOProcessor {}
export class FeishuWebhookActionBOProcessor extends BaseActionBOProcessor {}
export class SlackWebhookActionBOProcessor extends BaseActionBOProcessor {}
export class TelegramWebhookActionBOProcessor extends BaseActionBOProcessor {}
export class WecomWebhookActionBOProcessor extends BaseActionBOProcessor {}
export class DummyActionBOProcessor extends BaseActionBOProcessor {}

// database 相关的 action, 抽象处理方法
export abstract class BaseDatabaseActionBOProcessor<
  T extends FindRecordsAction | CreateRecordAction | UpdateRecordAction,
> extends ActionBOProcessor<T> {
  protected getDatabase(opts?: BORenderOpts): DatabaseBOProcessor | undefined {
    const { getProcessor } = opts || {};
    const databaseKey = this.bo.input.databaseId || this.bo.input.databaseTemplateId;
    return databaseKey ? (getProcessor?.(databaseKey) as DatabaseBOProcessor) : undefined;
  }

  override toVO<V = AutomationActionVO>(opts?: BORenderOpts): V {
    const databaseId = this.getDatabase(opts);
    const bo = { ...this.bo, input: { ...this.bo.input, databaseId } };
    return { ...super.toSimpleVO(), bo } as V;
  }
}

// database + record 相关的 action, 抽象处理方法
export class BaseRecordActionBOProcessor<
  T extends CreateRecordAction | UpdateRecordAction,
> extends BaseDatabaseActionBOProcessor<T> {
  override toVO<V = AutomationActionVO>(opts?: BORenderOpts): V {
    const vo = super.toVO(opts);
    const bo = vo.bo as CreateRecordAction | UpdateRecordAction;
    // 将 data 中的 fieldId或者fieldTemplateId 替换为 field.getId()
    const data = _.cloneDeep(bo.input.data);
    const database = this.getDatabase(opts);
    for (const key of Object.keys(data)) {
      const field = database?.getField(key);
      if (field) {
        data[field.getId()] = data[key];
      }
    }
    return { ...vo, bo: { ...bo, input: { ...bo.input, data } } } as V;
  }
}
