import { generateNanoID } from 'basenext/utils/nano-id';
import dayjs from 'dayjs';
import _ from 'lodash';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { TrashModel } from '@bika/domains/change/config/trash-table';
import { TrashSO } from '@bika/domains/change/server/trash-so';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { getUnknownErrorMessage, IOneWayLinkedListNode, LinkedListNodeFactory } from '@bika/domains/shared/server';
import { JobSO } from '@bika/domains/system/server/job-so';
import { SchedulerSO } from '@bika/domains/system/server/scheduler-so';
import { AutomationTriggerRelationType, TriggerSchedulerProperty } from '@bika/domains/system/server/types';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, Prisma, PrismaPromise } from '@bika/server-orm';
import { InboundEmailTriggerState, Trigger, TriggerOutput, TriggerState, TriggerType } from '@bika/types/automation/bo';
import { AutomationTriggerUpdateDTO } from '@bika/types/automation/dto';
import { AutomationTriggerVO, AutomationRenderOpts } from '@bika/types/automation/vo';
import { TrashBO } from '@bika/types/change/bo';
import { ExportBOOptions, ToTemplateOptions } from '@bika/types/node/bo';
import { iString, iStringParse } from '@bika/types/system';
import { AutomationSO } from './automation-so';
import { TriggerHandlerManager } from './trigger/trigger-handler-manager';
import { ITriggerEventContext, ITriggerParams } from './trigger/types';
import {
  IAutomationRunOptions,
  IMultiTriggerCreateInput,
  ITriggerCreateParam,
  ITriggerCreatedInput,
  TriggerModel,
} from './types';

export class TriggerSO<T extends Trigger = Trigger> implements IOneWayLinkedListNode {
  private _model: TriggerModel;

  constructor(model: TriggerModel) {
    this._model = model;
  }

  getNodeId(): string {
    return this.model.id;
  }

  getPreNodeId(): string | null {
    return this.model.preTriggerId;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get type(): TriggerType {
    return this.model.type as TriggerType;
  }

  get description(): iString | undefined {
    return (this.model.description as iString) || undefined;
  }

  get withScheduler(): boolean {
    const triggerHandler = this.getTriggerHandler();
    return triggerHandler.withScheduler();
  }

  get input(): T['input'] {
    return this.model.input;
  }

  get state(): TriggerState | undefined {
    return this.model.state ? (this.model.state as TriggerState) : undefined;
  }

  get spaceId(): string {
    return this.model.spaceId;
  }

  get automationId(): string {
    return this.model.automationId;
  }

  get preTriggerId(): string | null {
    return this.model.preTriggerId;
  }

  getAutomation(): Promise<AutomationSO> {
    return AutomationSO.init(this.automationId, false);
  }

  getTriggerHandler() {
    return TriggerHandlerManager.getTriggerHandler(this.type);
  }

  toBO(opts?: { withId?: boolean; withState?: boolean }): Trigger {
    const { withState = true } = opts || {};
    return {
      id: this.id,
      triggerType: this.type,
      templateId: this.templateId,
      description: this.description,
      input: this.input || undefined,
      state: withState && this.model.state ? this.model.state : undefined,
    } as Trigger;
  }

  toTemplate(opts?: ToTemplateOptions): Trigger {
    const trigger = _.omit(this.toBO({ withState: false }), 'id') as Trigger;
    if (!trigger.templateId) {
      trigger.templateId = this.id;
    }
    const triggerHandler = this.getTriggerHandler();
    return triggerHandler.toTemplateBO(trigger, opts?.getTemplateId);
  }

  async exportBO(opts?: ExportBOOptions) {
    const triggerHandler = this.getTriggerHandler();
    const trigger = this.toBO({ withState: false });
    try {
      return triggerHandler.exportBO(trigger, opts?.getInstanceId);
    } catch (_e) {
      throw new ServerError(errors.automation.automation_linked_resource_not_in_current_folder);
    }
  }

  setTemplateId(): PrismaPromise<Prisma.BatchPayload>[] {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        db.prisma.automationTrigger.updateMany({ where: { id: this.id }, data: { templateId: this.id } }),
      );
    }
    return operations;
  }

  static async init(triggerId: string): Promise<TriggerSO> {
    const triggerSO = await this.findById(triggerId);
    if (!triggerSO) {
      throw new ServerError(errors.common.not_found);
    }
    return triggerSO;
  }

  static async initMaybeNull(triggerId: string): Promise<TriggerSO | null> {
    const triggerPO = await db.prisma.automationTrigger.findUnique({
      where: { id: triggerId },
    });
    return triggerPO && TriggerSO.initWithModel(triggerPO);
  }

  static initWithModel(model: TriggerModel): TriggerSO {
    return new TriggerSO(model);
  }

  static initWithModels(models: TriggerModel[], sort: boolean = true): TriggerSO[] {
    const triggers = models.map((model: TriggerModel) => new TriggerSO(model));
    if (sort) {
      return new LinkedListNodeFactory(triggers).sort();
    }
    return triggers;
  }

  static async findById(triggerId: string): Promise<TriggerSO | undefined> {
    const triggerPO = await db.prisma.automationTrigger.findUnique({
      where: { id: triggerId },
    });
    return triggerPO ? this.initWithModel(triggerPO) : undefined;
  }

  static async findByAutomationId(automationId: string, sort: boolean = true): Promise<TriggerSO[]> {
    const pos = await db.prisma.automationTrigger.findMany({
      where: { automationId },
    });
    return this.initWithModels(pos, sort);
  }

  static async findActiveTriggersByAutomationIdAndType(automationId: string, type: TriggerType): Promise<TriggerSO[]> {
    const pos = await db.prisma.automationTrigger.findMany({
      where: {
        automationId,
        type,
        automation: { isActive: true },
      },
    });
    return this.initWithModels(pos, false);
  }

  /**
   * @param spaceId
   * @param type
   * @param bo Prisma JSON query https://www.prisma.io/docs/orm/prisma-client/special-fields-and-types/working-with-json-fields#filter-on-object-property
   * @param automationIds
   */
  static async findActiveTriggers(
    spaceId: string,
    types: TriggerType[],
    input?: Prisma.JsonNullableFilter,
    automationIds?: string[],
  ): Promise<TriggerSO[]> {
    const automation =
      automationIds && automationIds.length > 0 ? { id: { in: automationIds }, isActive: true } : { isActive: true };
    const pos = await db.prisma.automationTrigger.findMany({
      where: {
        spaceId,
        type: { in: types },
        input,
        automation,
      },
    });
    return this.initWithModels(pos, false);
  }

  static async findActiveResourceTriggers(type: TriggerType | TriggerType[], node: NodeSO): Promise<TriggerSO[]> {
    const types = Array.isArray(type) ? type : [type];
    const { id, spaceId, templateId } = node;
    // 通过资源ID查询
    const resourceIdPath = `${node.model.type.toString().toLowerCase()}Id`;
    const triggers = await this.findActiveTriggers(spaceId, types, {
      path: [resourceIdPath],
      equals: id,
    });
    // 资源不存在模板ID，直接返回
    if (!templateId) {
      return triggers;
    }

    // 资源存在模板ID，避免跨模板匹配，当前逻辑：遍历同模板文件夹的节点，找到所有自动化节点ID
    const templateFolderNode = await node.findTemplateFolderNode();
    if (!templateFolderNode) {
      return triggers;
    }
    const nodes = await templateFolderNode.getAllChildren();
    const automationIds = nodes.filter((n) => n.isAutomation).map((n) => n.id);
    if (automationIds.length === 0) {
      return triggers;
    }
    // 通过资源模板ID + automationIds 查询
    const resourceTemplateIdPath = `${node.model.type.toString().toLowerCase()}TemplateId`;
    const templateTriggers = await this.findActiveTriggers(
      spaceId,
      types,
      {
        path: [resourceTemplateIdPath],
        equals: templateId,
      },
      automationIds,
    );
    if (templateTriggers.length === 0) {
      return triggers;
    }

    // 合并
    const existTriggerIds = triggers.map((trigger) => trigger.id);
    templateTriggers
      .filter((trigger) => !existTriggerIds.includes(trigger.id))
      .forEach((trigger) => triggers.push(trigger));
    return triggers;
  }

  static async createTrigger(data: { user: UserSO; automation: AutomationSO; trigger: Trigger }): Promise<TriggerSO> {
    const { user, automation, trigger } = data;
    const { id: automationId, spaceId, isActive } = automation;
    const triggers = await automation.getTriggers(false);
    const preTriggerId = triggers.length > 0 ? triggers[triggers.length - 1].id : undefined;
    const { triggerId, operations } = await this.createTriggerOperation({
      user,
      spaceId,
      automationId,
      isActive,
      preTriggerId,
      trigger,
    });
    // execute transaction
    await db.prisma.$transaction(operations);
    return this.init(triggerId);
  }

  async buildSchedulerOperation(state?: TriggerState) {
    const { id: triggerId, createdBy } = this.model;
    const user = createdBy ? await UserSO.init(createdBy) : undefined;
    const triggerHandler = this.getTriggerHandler();
    const { schedulerInput } = await triggerHandler.buildSchedulerInput(this.toBO(), {
      user,
      triggerId,
      state,
    });
    return schedulerInput && SchedulerSO.createOperation(schedulerInput);
  }

  /**
   * 这个函数之所以会被重复执行，因为确保Scheduler没有被走丢。
   */
  async ensureSchedulerOfTrigger() {
    if (!this.withScheduler) {
      return;
    }
    const schedulerSOs = await this.findSchedulers();
    // 存在了？不用重复创建;
    if (schedulerSOs.length > 0) {
      return;
    }
    await this.buildSchedulerOperation();
  }

  /**
   * 目前场景，一个Trigger 最多只有一个Scheduler。
   * 防止某些情况下，产生多个Scheduler脏数据，这里不接收指定的Scheduler(from cron scan)，全找一遍，然后全部删除。
   */
  async runSchedulers(once?: boolean): Promise<JobSO[]> {
    const schedulersSOs = await this.findSchedulers();
    const jobs: JobSO[] = [];
    if (schedulersSOs.length === 0) {
      return jobs;
    }
    const schedulerIds = schedulersSOs.map((s) => s.model.id);
    await SchedulerSO.deleteByIds(schedulerIds);
    // 判断该类型的触发器是否带有调度器
    if (!this.withScheduler) {
      return jobs;
    }
    // 判断自动化是否激活
    const automation = await this.getAutomation();
    // 不再调用 automation.canRun()，防止内部查询 space audit 的 log 服务挂掉，导致这里中断，并影响新scheduler的创建
    if (!automation.isActive) {
      return jobs;
    }

    // 自动化存在，并处于激活状态，才会创建Job和新的Scheduler
    const { runTime, property } = schedulersSOs[0].model;
    const schedulerProperty = property ? (property as unknown as TriggerSchedulerProperty) : undefined;
    const job = await JobSO.new({
      type: 'AUTOMATION_TRIGGER',
      triggerId: this.id,
      triggerType: this.type,
      runTime: dayjs(runTime).toISOString(),
      property: schedulerProperty,
    });
    jobs.push(job);

    // 重新创建Scheduler
    if (!once) {
      // 传入state，imap trigger 会根据 authFailedCount 控制轮询频率
      await this.buildSchedulerOperation(this.state);
    }
    return jobs;
  }

  async match(ctx?: ITriggerEventContext): Promise<TriggerOutput[] | TriggerOutput | undefined> {
    const triggerHandler = this.getTriggerHandler();
    const params = this.buildTriggerParam();
    return triggerHandler.match(params, ctx);
  }

  /**
   * 当前触发器，执行自动化
   */
  async run(ctx?: ITriggerEventContext, optional?: IAutomationRunOptions & { skipActiveCheck?: boolean }) {
    const { skipActiveCheck = false, ...opt } = optional || {};
    // 校验自动化是否可运行
    const automation = await this.getAutomation();
    if (!skipActiveCheck) {
      const automationIsActive = await automation.canRun();
      if (!automationIsActive) {
        return false;
      }
    }
    const triggerHandler = this.getTriggerHandler();
    const params = this.buildTriggerParam();
    const output = await triggerHandler.match(params, ctx);
    if (!output) {
      return false;
    }
    const outputs = Array.isArray(output) ? output : [output];
    if (outputs.length === 0) {
      return false;
    }
    const trigger = this.toBO({ withState: false });
    return Promise.all(outputs.map((o) => automation.run([{ id: this.id, ...trigger, output: o }], opt)));
  }

  async doTest(user: UserSO) {
    const triggerHandler = this.getTriggerHandler();
    const isVerified = triggerHandler.validateBO(this.toBO());
    if (!isVerified) {
      throw new ServerError(errors.automation.trigger_missing_required_configuration);
    }
    let testResult: TriggerState['testResult'];
    let otherStateProperty;
    const runTime = new Date().toISOString();
    const params = this.buildTriggerParam();
    try {
      const { stateProperty, output } = await triggerHandler.runTest(params, user);
      testResult = { success: true, output, runTime };
      otherStateProperty = stateProperty;
    } catch (error: unknown) {
      testResult = { success: false, error: getUnknownErrorMessage(error), runTime };
    }
    // save to state
    await this.updateState({ testResult, ...otherStateProperty });
    return testResult;
  }

  async fetchTriggerOutputMaybeFake(_user?: UserSO, opts?: AutomationRenderOpts): Promise<TriggerOutput> {
    const triggerHandler = this.getTriggerHandler();
    const isVerified = triggerHandler.validateBO(this.toBO());
    if (!isVerified) {
      throw new ServerError(errors.automation.trigger_missing_required_configuration);
    }
    const params = this.buildTriggerParam();
    return triggerHandler.fetchOutputMaybeFake(params, opts);
  }

  private buildTriggerParam(): ITriggerParams {
    return {
      spaceId: this.spaceId,
      automationId: this.automationId,
      triggerId: this.id,
      triggerTemplateId: this.templateId,
      trigger: this.toBO(),
      triggerInput: this.input,
      triggerState: this.state,
      updateTriggerState: async (state: TriggerState) => {
        await this.updateState(state);
      },
      fetchAutomationTemplateNodeId: async () => {
        const automation = await this.getAutomation();
        return automation.toNodeSO().findTemplateFolderNodeId();
      },
    };
  }

  /**
   * 查找关联的Schedulers
   *
   * @returns {Promise<SchedulerSO[]>}
   */
  async findSchedulers(): Promise<SchedulerSO[]> {
    if (!this.withScheduler) {
      return [];
    }
    return SchedulerSO.findSchedulersByRelation([this.id], AutomationTriggerRelationType);
  }

  async buildDeleteTriggerOperations(): Promise<PrismaPromise<Prisma.BatchPayload>[]> {
    const triggerId = this.id;
    const nextTrigger = await db.prisma.automationTrigger.findUnique({
      where: { preTriggerId: triggerId },
      select: { id: true },
    });
    const operations = [db.prisma.automationTrigger.deleteMany({ where: { id: triggerId } })];
    // delete scheduler
    if (this.withScheduler) {
      operations.push(
        db.prisma.scheduler.deleteMany({
          where: {
            relationId: triggerId,
            relationType: 'AUTOMATION_TRIGGER',
          },
        }),
      );
    }
    // change position
    if (nextTrigger) {
      const trigger = await db.prisma.automationTrigger.findUnique({
        where: { id: triggerId },
        select: { preTriggerId: true },
      });
      operations.push(
        db.prisma.automationTrigger.updateMany({
          where: { id: nextTrigger.id },
          data: {
            preTriggerId: trigger?.preTriggerId,
          },
        }),
      );
    }
    return operations;
  }

  async delete(userId: string) {
    const operations: PrismaPromise<Prisma.BatchPayload | Omit<TrashModel, 'user'>>[] =
      await this.buildDeleteTriggerOperations();
    const trashBO: TrashBO = {
      trashType: 'TRIGGER',
      bo: {
        automationId: this.automationId,
        trigger: this.toBO(),
        preTriggerId: this.preTriggerId || undefined,
      },
    };
    const trashCreateInput = await TrashSO.boToCreateInput(this.spaceId, await UserSO.init(userId), trashBO);
    operations.push(db.prisma.trash.create({ data: trashCreateInput }));
    // 保证记录首先被创建
    await db.log.write({
      kind: 'SPACE_TRASH_LOG',
      spaceid: this.spaceId,
      trashid: trashCreateInput.id,
      data: JSON.stringify(trashBO),
    });
    await db.prisma.$transaction(operations);
    EventSO.automation.onTriggerDeleted(userId, this);
  }

  async updateState(updatedState: TriggerState) {
    const state: TriggerState = this.state ? { ...this.state, ...updatedState } : updatedState;
    await db.prisma.automationTrigger.update({
      where: { id: this.id },
      data: { state },
    });
    this.model.state = state as unknown as Prisma.JsonObject;
  }

  async clearState() {
    if (!this.state || this.type !== 'INBOUND_EMAIL') {
      return;
    }
    const { lastMessageUid } = this.state as InboundEmailTriggerState;
    if (lastMessageUid) {
      // 清空 lastMessageUid，避免下次重新开启自动化时，会把期间的邮件当作新邮件处理
      await this.updateState({ lastMessageUid: undefined });
    }
  }

  async update(user: UserSO, data: Omit<AutomationTriggerUpdateDTO, 'triggerId'>) {
    const updateTriggerOperations: PrismaPromise<Prisma.BatchPayload | TriggerModel>[] = [];
    if (data.trigger) {
      // API 修改配置，不允许主动修改 state
      const updatedTrigger = { ...data.trigger, state: undefined };
      const { operations } = await this.buildUpdateTriggerOperations(user, updatedTrigger);
      updateTriggerOperations.push(...operations);
    }
    if (data.preTriggerId !== undefined) {
      const updateSortOperations = await this.updateSortByPreTriggerIdOperation(user.id, data.preTriggerId);
      updateTriggerOperations.push(...updateSortOperations);
    }
    if (updateTriggerOperations.length > 0) {
      await db.prisma.$transaction(updateTriggerOperations);
    }
  }

  async buildUpdateTriggerOperations(
    user: UserSO,
    trigger: Trigger,
    isTemplateOperation?: boolean,
  ): Promise<{
    operations: PrismaPromise<Prisma.BatchPayload>[];
    templateCallbackFn?: () => Promise<void>;
  }> {
    const triggerId = this.id;
    const { triggerType, description } = trigger;
    // build trigger update input and state
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(triggerType);
    const input = triggerHandler.buildUpdatedInput(trigger, user, this.toBO());
    const state = triggerHandler.buildUpdatedState(trigger, this.toBO());
    const operations = [
      db.prisma.automationTrigger.updateMany({
        where: { id: triggerId },
        data: {
          description,
          type: triggerType.toString() as TriggerType,
          input,
          state,
          updatedBy: user.id,
        },
      }),
    ];
    // delete scheduler
    if (this.withScheduler) {
      const schedulers = await this.findSchedulers();
      if (schedulers.length > 0) {
        const schedulerIds = schedulers.map((s) => s.id);
        operations.push(
          db.prisma.scheduler.deleteMany({
            where: { id: { in: schedulerIds } },
          }),
        );
      }
    }
    // ensure scheduler
    const { schedulerInput, templateCallbackFn } = await triggerHandler.buildSchedulerInput(trigger, {
      user,
      triggerId,
      isTemplateOperation,
    });
    if (schedulerInput) {
      const schedulerOperation = SchedulerSO.createMany([schedulerInput]);
      operations.push(schedulerOperation);
    }
    return { operations, templateCallbackFn };
  }

  private async updateSortByPreTriggerIdOperation(userId: string, preTriggerId: string | null) {
    // old sorted actions
    const triggers = await (await this.getAutomation()).getTriggers();
    const operations: PrismaPromise<TriggerModel>[] = [];
    // insert the item to old sorted triggers, then get the new sorted triggers
    const sortedTriggers = triggers.reduce((prev: TriggerSO[], cur, index) => {
      if (index === 0 && preTriggerId === null) {
        prev.push(this);
      }
      if (cur.id !== this.id) {
        prev.push(cur);
      }
      if (cur.id === preTriggerId) {
        prev.push(this);
      }

      return prev;
    }, []);
    // 根据新的顺序修改 trigger
    sortedTriggers.forEach((trigger, index) => {
      const newPreTriggerId = index === 0 ? null : sortedTriggers[index - 1].id;
      const newNextTriggerId = index === sortedTriggers.length - 1 ? null : sortedTriggers[index + 1].id;
      if (newPreTriggerId !== trigger.preTriggerId) {
        operations.push(
          db.prisma.automationTrigger.update({
            where: { id: trigger.id },
            data: {
              preTrigger: newPreTriggerId
                ? {
                    connect: {
                      id: newPreTriggerId,
                    },
                  }
                : { disconnect: true },
              nextTrigger: newNextTriggerId
                ? {
                    connect: {
                      id: newNextTriggerId,
                    },
                  }
                : undefined,
              updatedBy: userId,
            },
          }),
        );
      }
    });
    return operations;
  }

  async toVO(opts?: AutomationRenderOpts): Promise<AutomationTriggerVO> {
    const triggerHandler = this.getTriggerHandler();
    const bo = this.toBO();
    const isVerified = triggerHandler.validateBO(bo);
    // fill in bo
    const trigger = await triggerHandler.fillInBO(bo, opts?.templateNodeId);
    return {
      id: this.id,
      type: this.type,
      description: iStringParse(this.description, opts?.locale),
      bo: trigger,
      isVerified,
    };
  }

  static async createTriggerOperation(createParam: ITriggerCreateParam): Promise<ITriggerCreatedInput> {
    const { isActive } = createParam;
    // build trigger create input
    const triggerCreateInput = this.buildTriggerCreateManyInputConnectAutomation(createParam);
    const triggerId = triggerCreateInput.id;
    const triggerOperation = db.prisma.automationTrigger.create({
      data: triggerCreateInput,
    });
    // 若自动化未激活，不需要创建scheduler，直接返回
    if (!isActive) {
      return { triggerId, operations: [triggerOperation] };
    }
    // build scheduler create input
    const trigger = createParam.trigger;
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
    const { schedulerInput, templateCallbackFn } = await triggerHandler.buildSchedulerInput(trigger, {
      ...createParam,
      triggerId,
    });
    if (!schedulerInput) {
      return { triggerId, operations: [triggerOperation], templateCallbackFn };
    }
    const schedulerOperation = SchedulerSO.createOperation(schedulerInput);
    return { triggerId, operations: [triggerOperation, schedulerOperation] };
  }

  private static buildTriggerCreateManyInputConnectAutomation(createParam: {
    user: UserSO;
    spaceId: string;
    automationId: string;
    preTriggerId?: string;
    trigger: Trigger;
  }): Prisma.AutomationTriggerCreateManyInput {
    const { automationId } = createParam;
    const input = this.buildTriggerCreateInputNotConnectAutomation(createParam);
    return { automationId, ...input };
  }

  static buildTriggerCreateInputNotConnectAutomation(createParam: {
    user: UserSO;
    spaceId: string;
    preTriggerId?: string;
    trigger: Trigger;
  }): Prisma.AutomationTriggerCreateManyAutomationInput {
    const { user, spaceId, preTriggerId, trigger } = createParam;
    const userId = user.id;
    const { id, templateId, description, triggerType } = trigger;
    const triggerHandler = TriggerHandlerManager.getTriggerHandler(triggerType);
    const input = triggerHandler.buildCreatedInput(trigger, user);
    return {
      id: id || generateNanoID('trg'),
      templateId,
      description,
      spaceId,
      type: triggerType.toString(),
      input,
      preTriggerId,
      createdBy: userId,
      updatedBy: userId,
    };
  }

  /**
   * 构建自动化类型触发器创建数据。
   */
  static async buildTriggerCreateManyInputNotConnectAutomation(params: {
    user: UserSO;
    spaceId: string;
    triggers: Trigger[];
    isActive?: boolean;
    isTemplateOperation?: boolean;
  }): Promise<IMultiTriggerCreateInput> {
    const { user, spaceId, triggers, isActive, isTemplateOperation } = params;
    const triggerInputs: Prisma.AutomationTriggerCreateManyAutomationInput[] = [];
    const schedulerInputs: Prisma.SchedulerCreateManyInput[] = [];
    const triggerCallbackFns: (() => Promise<void>)[] = [];
    let preTriggerId: string | undefined;
    for (const trigger of triggers) {
      const triggerCreateInput = this.buildTriggerCreateInputNotConnectAutomation({
        user,
        spaceId,
        preTriggerId,
        trigger,
      });
      const triggerId = triggerCreateInput.id;
      // 更新 preTriggerId
      preTriggerId = triggerId;
      triggerInputs.push(triggerCreateInput);
      if (!isActive) {
        continue;
      }
      // 如果是激活状态，需要创建scheduler
      const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
      const { schedulerInput, templateCallbackFn } = await triggerHandler.buildSchedulerInput(trigger, {
        user,
        triggerId,
        isTemplateOperation,
      });
      if (schedulerInput) {
        schedulerInputs.push(schedulerInput);
      }
      if (templateCallbackFn) {
        triggerCallbackFns.push(templateCallbackFn);
      }
    }
    return { triggerInputs, schedulerInputs, triggerCallbackFns };
  }
}
