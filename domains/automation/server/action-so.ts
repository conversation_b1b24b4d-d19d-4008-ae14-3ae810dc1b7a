import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { ServerError, errors } from '@bika/contents/config/server/error';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { getUnknownErrorMessage, IOneWayLinkedListNode, LinkedListNodeFactory } from '@bika/domains/shared/server';
import { JobSO } from '@bika/domains/system/server/job-so';
import { DelayActionSchedulerProperty } from '@bika/domains/system/server/types';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, Prisma, PrismaPromise } from '@bika/server-orm';
import { Action, ActionType, ActionState, ActionOutput } from '@bika/types/automation/bo';
import { AutomationActionUpdateDTO } from '@bika/types/automation/dto';
import { AutomationActionVO, AutomationRenderOpts } from '@bika/types/automation/vo';
import { TrashBO } from '@bika/types/change/bo';
import { ExportBOOptions, ToTemplateOptions } from '@bika/types/node/bo';
import { iString, iStringParse } from '@bika/types/system';
import { isInCI } from 'sharelib/app-env';
import { ActionHandlersManager } from './action/action-handlers-manager';
import { IActionRunInput, IActionRunResult, IAutomationRunInput } from './action/types';
import { AutomationRunContextBuilder } from './automation-run-context-builder';
import { AutomationSO } from './automation-so';
// import { FormAppAISO } from '../../automation-nodes/formapp-action/formapp-ai-so';
import { ActionModel, IAutomationRunContext, IMultiActionCreateInput } from './types';
import { ToolSDKAISO } from '../../automation-nodes/toolsdk-action/toolsdk-ai-so';
import { TrashModel } from '../../change/config/trash-table';
import { TrashSO } from '../../change/server/trash-so';

export class ActionSO implements IOneWayLinkedListNode {
  private _model: ActionModel;

  private _childrenActions?: ActionSO[];

  constructor(model: ActionModel, childrenActions?: ActionSO[]) {
    this._model = model;
    this._childrenActions = childrenActions;
  }

  getNodeId(): string {
    return this.model.id;
  }

  getPreNodeId(): string | null {
    return this.model.preActionId;
  }

  get model(): ActionModel {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get type(): ActionType {
    return this.model.type as ActionType;
  }

  get description(): iString | undefined {
    return (this.model.description as iString) || undefined;
  }

  get state(): ActionState | undefined {
    return this.model.state ? (this.model.state as ActionState) : undefined;
  }

  get spaceId(): string {
    return this.model.spaceId;
  }

  get automationId(): string {
    return this.model.automationId;
  }

  get withScheduler(): boolean {
    const typeWithScheduler = ['DELAY'];
    return typeWithScheduler.includes(this._model.type);
  }

  get preActionId(): string | null {
    return this.model.preActionId;
  }

  get parentActionId(): string | null {
    return this.model.parentActionId;
  }

  getAutomation(): Promise<AutomationSO> {
    return AutomationSO.init(this.automationId, false);
  }

  async toBO(opts?: { withState?: boolean }): Promise<Action> {
    const { withState = true } = opts || {};
    let actions: Action[] | undefined;
    const nestedActions = await this.getChildrenActions({ sort: true });
    if (nestedActions.length > 0) {
      actions = await Promise.all(nestedActions.map(async (action) => action.toBO(opts)));
    }
    return {
      id: this.id,
      actionType: this.type,
      templateId: this.templateId,
      description: this.description,
      input: this.model.input || undefined,
      state: withState && this.model.state ? this.model.state : undefined,
      actions,
    } as Action;
  }

  async toTemplate(opts?: ToTemplateOptions): Promise<Action> {
    const action = _.omit(await this.toBO({ withState: false }), 'id') as Action;
    if (!action.templateId) {
      action.templateId = this.id;
    }
    const actionHandler = ActionHandlersManager.getAction(action.actionType);
    const actionTemplate = actionHandler.toTemplateBO(
      action,
      opts?.getTemplateId as (id: string) => string | undefined,
    );
    if ('actions' in actionTemplate && actionTemplate.actions && actionTemplate.actions.length > 0) {
      const actions = actionTemplate.actions;
      const nestedActions = await this.getChildrenActions();
      const nestedActionTemplates = await Promise.all(
        nestedActions.map((nestedAction) => nestedAction.toTemplate(opts)),
      );
      // sort nested actions by order
      nestedActionTemplates.sort((a, b) => {
        const aIndex = actions.findIndex((act) => act.templateId === a.templateId);
        const bIndex = actions.findIndex((act) => act.templateId === b.templateId);
        return aIndex - bIndex;
      });
      return {
        ...actionTemplate,
        actions: nestedActionTemplates,
      };
    }
    return actionTemplate;
  }

  setTemplateId(): PrismaPromise<Prisma.BatchPayload>[] {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(db.prisma.automationAction.updateMany({ where: { id: this.id }, data: { templateId: this.id } }));
    }
    return operations;
  }

  async exportBO(opts?: ExportBOOptions): Promise<Action> {
    let action = await this.toBO({ withState: false });
    const actionHandler = ActionHandlersManager.getAction(action.actionType);
    try {
      action = await actionHandler.exportBO(action, opts?.getInstanceId);
    } catch (_e) {
      throw new Error(
        `The automation ${opts?.resourceName} action ${action.actionType} linked resource not in current folder)`,
      );
    }
    if ('actions' in action && action.actions && action.actions.length > 0) {
      const nestedActions = await this.getChildrenActions();
      const actions = await Promise.all(nestedActions.map((nestedAction) => nestedAction.exportBO(opts)));
      // sort nested actions by order
      actions.sort((a, b) => {
        const aIndex = action.actions!.findIndex((act) => act.id === a.id);
        const bIndex = action.actions!.findIndex((act) => act.id === b.id);
        return aIndex - bIndex;
      });
      return {
        ...action,
        actions,
      };
    }
    return action;
  }

  static async init(id: string): Promise<ActionSO> {
    const action = await db.prisma.automationAction.findUnique({ where: { id } });
    if (!action) {
      throw new ServerError(errors.common.not_found);
    }
    return this.initWithModel(action);
  }

  static async initMaybeNull(actionId: string): Promise<ActionSO | null> {
    const action = await db.prisma.automationAction.findUnique({
      where: { id: actionId },
    });
    return action && ActionSO.initWithModel(action);
  }

  /**
   * init with model
   * @param model model
   */
  static initWithModel(model: ActionModel): ActionSO {
    return new ActionSO(model);
  }

  static initWithManyModels(models: ActionModel[], sort: boolean = true): ActionSO[] {
    const sos = models.map((model: ActionModel) => {
      if ('childrenActions' in model && model.childrenActions.length > 0) {
        const nestedActions = model.childrenActions.map((nestedAction) => new ActionSO(nestedAction));
        if (!sort) {
          return new ActionSO(model, nestedActions);
        }
        const sortedNestedActions = new LinkedListNodeFactory(nestedActions).sort();
        return new ActionSO(model, sortedNestedActions);
      }
      return new ActionSO(model);
    });
    return sort ? new LinkedListNodeFactory(sos).sort() : sos;
  }

  /**
   * 查询自动化的所有动作。包含嵌套动作
   */
  static async findManyByAutomationId(automationId: string): Promise<ActionSO[]> {
    const pos = await db.prisma.automationAction.findMany({
      where: {
        automationId,
      },
    });
    return ActionSO.initWithManyModels(pos, false);
  }

  /**
   * 查询自动化的第一层动作。不包含嵌套动作
   */
  static async findFirstLevelActions(automationId: string, sort: boolean = true): Promise<ActionSO[]> {
    const pos = await db.prisma.automationAction.findMany({
      where: {
        automationId,
        parentActionId: null,
      },
      include: {
        childrenActions: true,
      },
    });
    return ActionSO.initWithManyModels(pos, sort);
  }

  async getChildrenActions(opt?: { useCache?: boolean; sort?: boolean }): Promise<ActionSO[]> {
    const { useCache = true, sort = true } = opt || {};
    if (this._childrenActions && useCache) {
      return this._childrenActions;
    }
    // 嵌套动作内，不能再有嵌套动作
    if (this.model.parentActionId !== null) {
      return [];
    }
    const withNestedActionType = ['CONDITION', 'FIND_RECORDS', 'LOOP'];
    if (!withNestedActionType.includes(this.type)) {
      return [];
    }
    const pos = await db.prisma.automationAction.findMany({
      where: {
        automationId: this.model.automationId,
        parentActionId: this.id,
      },
    });
    const actions = ActionSO.initWithManyModels(pos, sort);
    this._childrenActions = actions;
    return actions;
  }

  async doTest(user: UserSO, useTestOutputForTest?: boolean) {
    const action = await this.toBO();
    const handler = ActionHandlersManager.getAction(this.type);
    const isVerified = handler.validateBO(action);
    if (!isVerified) {
      throw new ServerError(errors.automation.action_missing_required_configuration);
    }
    const automation = await this.getAutomation();
    const templateNodeId = await automation.toNodeSO().findTemplateFolderNodeId();
    const input = {
      spaceId: this.spaceId,
      templateNodeId,
      userId: user.id,
      actionId: this.id,
      action,
    };
    const context = await AutomationRunContextBuilder.initTestRunContext(automation, this, {
      user,
      locale: user?.locale,
      templateNodeId,
      useTestOutputForTest,
    });
    let testResult: ActionState['testResult'];
    const runTime = new Date().toISOString();
    try {
      const output = await handler.doTest(input, context);
      testResult = { success: true, output, runTime };
    } catch (error: unknown) {
      testResult = { success: false, error: getUnknownErrorMessage(error), runTime };
    }
    // save to state
    const state: ActionState = action.state ? { ...action.state, testResult } : { testResult };
    await this.updateState(state);
    return testResult;
  }

  async fetchActionOutputMaybeFake(opts?: AutomationRenderOpts): Promise<ActionOutput> {
    const action = await this.toBO();
    const handler = ActionHandlersManager.getAction(this.type);
    const isVerified = handler.validateBO(action);
    if (!isVerified) {
      throw new ServerError(errors.automation.action_missing_required_configuration);
    }
    const input = {
      spaceId: this.spaceId,
      automationId: this.automationId,
      templateNodeId: opts?.templateNodeId,
      locale: opts?.locale,
      actionSO: this,
      action,
    };
    return handler.fetchOutputMaybeFake(input);
  }

  async run(
    context: IAutomationRunContext,
    params: IAutomationRunInput,
  ): Promise<IActionRunResult & { action: Action }> {
    const action = await this.toBO();
    const updateChildrenActionState = async (childrenActionId: string, state: ActionState) => {
      const childrenAction = await ActionSO.init(childrenActionId);
      return childrenAction.updateState(state);
    };
    const input: IActionRunInput = {
      ...params,
      action,
      updateChildrenActionState,
    };
    const handler = ActionHandlersManager.getAction(this.type);
    const { state, ...result } = await handler.run(input, context);
    if (state) {
      await this.updateState(state);
    }
    return { ...result, action };
  }

  async runSchedulers(schedulerProperty: DelayActionSchedulerProperty): Promise<JobSO[]> {
    const jobs: JobSO[] = [];
    if (this.type !== 'DELAY') {
      return jobs;
    }
    // 判断自动化是否激活
    // 继续创建Job，让后续Delay的运行记录变为取消运行
    // const automation = await this.getAutomation();
    // if (!automation.isActive) {
    //   return jobs;
    // }

    const job = await JobSO.new({
      type: 'AUTOMATION_ACTION',
      actionId: this.id,
      ...schedulerProperty,
    });
    jobs.push(job);
    return jobs;
  }

  async doJob(schedulerProperty: DelayActionSchedulerProperty) {
    const { automationRunHistoryId } = schedulerProperty;
    // 校验自动化是否可运行
    const automation = await this.getAutomation();
    await automation.runAfterDelay(this.id, automationRunHistoryId);
  }

  /**
   * create action for automation.
   *
   */
  static async createAction(createParam: {
    userId: string;
    spaceId: string;
    automationId: string;
    parentActionId?: string;
    preActionId?: string;
    action: Action;
  }): Promise<ActionSO> {
    const { userId, spaceId, automationId, parentActionId, preActionId, action } = createParam;
    const parentActionConnect = parentActionId ? { connect: { id: parentActionId } } : undefined;
    const { actionCreateInput: input } = this.buildActionCreateInputNotConnectAutomation({ userId, spaceId, action });
    const actionCreateInput: Prisma.AutomationActionCreateInput = {
      automation: {
        connect: {
          id: automationId,
        },
      },
      preAction: preActionId
        ? {
            connect: {
              id: preActionId,
            },
          }
        : undefined,
      parentAction: parentActionConnect,
      ...input,
    };
    const actionPO = await db.prisma.automationAction.create({
      data: actionCreateInput,
    });
    return ActionSO.initWithModel(actionPO);
  }

  async delete(userId: string): Promise<void> {
    const operations: PrismaPromise<Prisma.BatchPayload | Omit<TrashModel, 'user'>>[] = await ActionSO.deleteById(
      this.id,
    );
    const trashOperation = await this.toTrash(userId);
    operations.push(...trashOperation);
    await db.prisma.$transaction(operations);
    EventSO.automation.onActionDeleted(userId, this);
  }

  async update(userId: string, data: Omit<AutomationActionUpdateDTO, 'actionId'>): Promise<void> {
    const operations: PrismaPromise<ActionModel>[] = [];
    if (data.action) {
      operations.push(
        db.prisma.automationAction.update({
          where: {
            id: this.id,
          },
          data: {
            description: data.action.description,
            type: data.action.actionType,
            input: data.action.input as unknown as Prisma.JsonObject,
            updatedBy: userId,
          },
        }),
      );
    }
    if (data.preActionId !== undefined) {
      operations.push(...(await this.updateSortByPreActionIdOperation(userId, data.preActionId)));
    }
    if (operations.length > 0) {
      await db.prisma.$transaction(operations);
    }
    await db.prisma.$transaction(operations);
    EventSO.automation.onActionUpdated(this);
  }

  async updateState(updatedState: ActionState): Promise<ActionState> {
    const state: ActionState = this.state ? { ...this.state, ...updatedState } : updatedState;
    await db.prisma.automationAction.update({
      where: { id: this.id },
      data: { state },
    });
    this.model.state = state as unknown as Prisma.JsonObject;
    return state;
  }

  static async deleteById(id: string): Promise<PrismaPromise<Prisma.BatchPayload>[]> {
    const nextAction = await db.prisma.automationAction.findUnique({
      where: { preActionId: id },
      select: { id: true },
    });
    const operations = [
      db.prisma.automationAction.deleteMany({
        where: { id },
      }),
    ];
    // change position
    if (nextAction) {
      const action = await db.prisma.automationAction.findUnique({ where: { id }, select: { preActionId: true } });
      operations.push(
        db.prisma.automationAction.updateMany({
          where: { id: nextAction.id },
          data: {
            preActionId: action?.preActionId,
          },
        }),
      );
    }
    // delete children
    const children = await ActionSO.getCurrentChildIds(id);
    for (const child of children) {
      const result = await ActionSO.deleteById(child);
      operations.push(...result);
    }
    return operations;
  }

  private async updateSortByPreActionIdOperation(userId: string, preActionId: string | null) {
    // old sorted actions
    const getSameLevelActions = async () => {
      // 如果当前动作没有父动作，则获取当前自动化的第一层动作
      if (!this.parentActionId) {
        const automation = await this.getAutomation();
        return automation.getFirstLevelActions();
      }
      // 如果当前动作有父动作，则获取父动作的子动作
      const parentAction = await ActionSO.init(this.parentActionId);
      return parentAction.getChildrenActions({ sort: true });
    };
    const actions = await getSameLevelActions();
    if (preActionId !== null) {
      const preAction = actions.find((a) => a.id === preActionId);
      if (preAction?.parentActionId !== this.parentActionId) {
        throw new Error(`Action cannot move to this position`);
      }
    }
    const operations: PrismaPromise<ActionModel>[] = [];
    // insert the item to old sorted actions, then get the new sorted actions
    const sortedActions = actions.reduce((prev: ActionSO[], cur, index) => {
      if (index === 0 && preActionId === null) {
        prev.push(this);
      }
      if (cur.id !== this.id) {
        prev.push(cur);
      }
      if (cur.id === preActionId) {
        prev.push(this);
      }

      return prev;
    }, []);
    // 根据新的顺序修改 action
    sortedActions.forEach((action, index) => {
      const newPreActionId = index === 0 ? null : sortedActions[index - 1].id;
      const newNextActionId = index === sortedActions.length - 1 ? null : sortedActions[index + 1].id;
      if (newPreActionId !== action.preActionId) {
        operations.push(
          db.prisma.automationAction.update({
            where: { id: action.id },
            data: {
              preAction: newPreActionId
                ? {
                    connect: {
                      id: newPreActionId,
                    },
                  }
                : { disconnect: true },
              nextAction: newNextActionId
                ? {
                    connect: {
                      id: newNextActionId,
                    },
                  }
                : undefined,
              updatedBy: userId,
            },
          }),
        );
      }
    });
    return operations;
  }

  static async updateSort(userId: string, actions: { id: string }[]): Promise<PrismaPromise<ActionModel>[]> {
    const operations: PrismaPromise<ActionModel>[] = [];
    actions.forEach((action, index) => {
      const preId = index === 0 ? null : actions[index - 1].id;
      const nextId = index === actions.length - 1 ? null : actions[index + 1].id;
      operations.push(
        db.prisma.automationAction.update({
          where: { id: action.id },
          data: {
            preAction: preId ? { connect: { id: preId } } : { disconnect: true },
            nextAction: nextId ? { connect: { id: nextId } } : undefined,
            updatedBy: userId,
          },
        }),
      );
    });
    return operations;
  }

  static async getCurrentChildIds(parentId: string): Promise<string[]> {
    return db.prisma.automationAction
      .findMany({
        where: {
          parentActionId: parentId,
        },
        select: {
          id: true,
        },
      })
      .then((result) => result.map((i) => i.id));
  }

  static createActionOperation(createParam: {
    userId: string;
    spaceId: string;
    automationId: string;
    parentActionId?: string;
    preActionId?: string;
    action: Action;
  }): { actionId: string; operation: PrismaPromise<ActionModel> } {
    const { automationId } = createParam;
    const { actionCreateInput: input } = ActionSO.buildActionCreateInputNotConnectAutomation(createParam);
    const actionCreateInput = { automationId, ...input };
    const operation = db.prisma.automationAction.create({
      data: actionCreateInput,
    });
    return { actionId: actionCreateInput.id, operation };
  }

  /**
   * 构造自动化类型动作创建数据
   * @param params
   * @param actions action from automation
   */
  static buildActionCreateManyInputNotConnectAutomation(params: {
    userId: string;
    spaceId: string;
    actions: Action[];
    parentActionId?: string;
  }): IMultiActionCreateInput {
    const { userId, spaceId, actions, parentActionId } = params;
    const actionsCreates: Prisma.AutomationActionCreateManyAutomationInput[] = [];
    const callbackFns: (() => Promise<void>)[] = [];
    let preActionId: string | undefined;
    for (const action of actions) {
      const { actionCreateInput, callbackFn } = ActionSO.buildActionCreateInputNotConnectAutomation({
        userId,
        spaceId,
        parentActionId,
        preActionId,
        action,
      });
      preActionId = actionCreateInput.id;
      actionsCreates.push(actionCreateInput);
      if (callbackFn) {
        callbackFns.push(callbackFn);
      }
      // 递归处理嵌套动作
      const { actionsInputs: nestedActionCreateInputs, callbackFns: nestedActionCallbackFns } =
        this.buildNestedActionCreateManyInputNotConnectAutomation(action, {
          userId,
          spaceId,
          parentActionId: actionCreateInput.id,
        });
      actionsCreates.push(...nestedActionCreateInputs);
      if (nestedActionCallbackFns) {
        callbackFns.push(...nestedActionCallbackFns);
      }
    }
    return { actionsInputs: actionsCreates, callbackFns };
  }

  private static buildNestedActionCreateManyInputNotConnectAutomation(
    parentAction: Action,
    params: {
      userId: string;
      spaceId: string;
      parentActionId: string;
    },
  ): IMultiActionCreateInput {
    const actions = 'actions' in parentAction ? parentAction.actions : undefined;
    if (!actions || actions.length === 0) {
      return { actionsInputs: [] };
    }
    return this.buildActionCreateManyInputNotConnectAutomation({ ...params, actions });
  }

  private static buildActionCreateInputNotConnectAutomation(params: {
    userId: string;
    spaceId: string;
    parentActionId?: string;
    preActionId?: string;
    action: Action;
  }): { actionCreateInput: Prisma.AutomationActionCreateManyAutomationInput; callbackFn?: () => Promise<void> } {
    const { userId, spaceId, parentActionId, preActionId, action } = params;
    const { templateId, description, actionType, input } = action;
    const type = actionType.toString();
    // 限制延迟动作不能嵌套，因为嵌套动作未保存运行历史，无法在延迟结束后拿到运行上下文
    if (parentActionId && type === 'DELAY') {
      throw new ServerError(errors.automation.delay_action_cannot_be_nested);
    }
    const id = action.id || generateNanoID('act');
    const actionCreateInput: Prisma.AutomationActionCreateManyAutomationInput = {
      id,
      templateId,
      description,
      spaceId,
      type,
      input: input as unknown as Prisma.JsonObject,
      parentActionId,
      preActionId,
      createdBy: userId,
      updatedBy: userId,
    };
    if (!['FORMAPP_AI', 'TOOLSDK_AI'].includes(type) || input.instanceId) {
      return { actionCreateInput };
    }
    const fetchInstance = async () => {
      // if (type === 'FORMAPP_AI') {
      //   const { actionKey, inputData } = input;
      //   return FormAppAISO.putAccountActionInstance(spaceId, id, { actionKey, inputData });
      // }
      const { packageKey, packageVersion, toolKey, inputData } = input;
      return ToolSDKAISO.putPackageInstance(spaceId, id, {
        packageKey,
        packageVersion,
        toolKey,
        inputData,
      });
    };
    // 安装模板完成后，创建 FormApp/ToolSDK AI Action 实例
    const callbackFn = async () => {
      if (isInCI()) {
        return;
      }
      const instance = await fetchInstance();
      await db.prisma.automationAction.update({
        where: {
          id,
        },
        data: {
          input: { ...input, instanceId: instance.instanceId },
        },
      });
    };
    return { actionCreateInput, callbackFn };
  }

  static async delete(actionId: string): Promise<void> {
    await db.prisma.automationAction.delete({
      where: {
        id: actionId,
      },
    });
  }

  async toVO(opts?: AutomationRenderOpts): Promise<AutomationActionVO> {
    const bo = await this.toBO();
    const handler = ActionHandlersManager.getAction(this.type);
    const isVerified = handler.validateBO(bo);
    const [action, statistic] = await Promise.all([
      // fill in bo
      handler.fillInBO(bo, { spaceId: this.spaceId, templateNodeId: opts?.templateNodeId }),
      // get statistic
      handler.statistic(this.id),
    ]);
    const nestedActions = await this.getChildrenActions({ sort: true });
    const actions = nestedActions.length > 0 ? await Promise.all(nestedActions.map((i) => i.toVO(opts))) : [];
    return {
      id: this.model.id,
      type: this.type,
      description: iStringParse(this.description, opts?.locale),
      bo: action,
      isVerified,
      statistic,
      actions,
    };
  }

  /**
   * update action operation
   * @param userId user id
   * @param action action from template
   */
  updateActionOperation(userId: string, action: Action): PrismaPromise<ActionModel> {
    const { description, actionType, input } = action;
    return db.prisma.automationAction.update({
      where: {
        id: this.id,
      },
      data: {
        description,
        type: actionType,
        input: input as unknown as Prisma.JsonObject,
        updatedBy: userId,
      },
    });
  }

  private async toTrash(userId: string): Promise<PrismaPromise<Omit<TrashModel, 'user'>>[]> {
    const trashBO: TrashBO = {
      trashType: 'ACTION',
      bo: {
        automationId: this.automationId,
        action: await this.toBO(),
        preActionId: this.preActionId || undefined,
        parentActionId: this.parentActionId || undefined,
      },
    };
    const trashCreateInput = await TrashSO.boToCreateInput(this.spaceId, await UserSO.init(userId), trashBO);
    // 保证记录首先被创建
    await db.log.write({
      kind: 'SPACE_TRASH_LOG',
      spaceid: this.spaceId,
      trashid: trashCreateInput.id,
      data: JSON.stringify(trashBO),
    });
    return [db.prisma.trash.create({ data: trashCreateInput })];
  }
}
