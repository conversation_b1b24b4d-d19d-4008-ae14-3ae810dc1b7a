import { generateNanoID } from 'basenext/utils/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { AUTOMATION_USER_ID } from '@bika/domains/database/shared';
import { db, mongoose, AutomationRunHistoryModel } from '@bika/server-orm';
import { TriggerOutput } from '@bika/types/automation/bo';
import {
  ActionRunDetailVO,
  AutomationRunHistoryData,
  AutomationRunHistoryStatus,
  TriggerRunDetailVO,
  AutomationRunHistoryDetailVO,
  AutomationRunHistoryVO,
} from '@bika/types/automation/vo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';

export class AutomationRunHistorySO {
  private readonly _model: AutomationRunHistoryModel;

  constructor(model: AutomationRunHistoryModel) {
    this._model = model;
  }

  get model(): AutomationRunHistoryModel {
    return this._model;
  }

  get id(): string {
    return this.model.id;
  }

  get automationId(): string {
    return this.model.automationId;
  }

  get status(): AutomationRunHistoryStatus {
    return this.model.status as AutomationRunHistoryStatus;
  }

  get data(): AutomationRunHistoryData {
    return this.model.data;
  }

  get createdBy(): string | undefined {
    return this.model.createdBy || undefined;
  }

  /**
   * 特殊运行标识，包含ID和鉴权令牌，一些特殊操作可以直接使用，如取消延迟执行
   */
  get runFlag(): string {
    return `${this.id}-${this.model.token}`;
  }

  static async init(id: string): Promise<AutomationRunHistorySO> {
    const model = await db.mongo.automationRunHistory.findOne({
      id,
    });
    if (!model) {
      throw new ServerError(errors.common.not_found);
    }
    return this.initWithModel(model);
  }

  /**
   * init with model
   * @param model model
   */
  static initWithModel(model: AutomationRunHistoryModel): AutomationRunHistorySO {
    return new AutomationRunHistorySO(model);
  }

  static async countBySpaceId(spaceId: string): Promise<number> {
    return db.mongo.automationRunHistory.countDocuments({ spaceId });
  }

  static async findByRunFlag(runFlag: string): Promise<AutomationRunHistorySO> {
    const [id, token] = runFlag.split('-');
    const runHistory = await this.init(id);
    if (runHistory.model.token !== token) {
      throw new ServerError(errors.common.not_found);
    }
    return runHistory;
  }

  static async find(
    query: { automationId: string; date?: { startDate: Date; endDate: Date } },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: AutomationRunHistorySO[] }> {
    const { automationId, date } = query;
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    const filter: mongoose.FilterQuery<AutomationRunHistoryModel> = {
      automationId,
    };
    if (date) {
      filter.createdAt = { $gte: date.startDate, $lt: date.endDate };
    }
    const [rows, total] = await Promise.all([
      db.mongo.automationRunHistory
        .find(filter)
        .sort({ _id: -1 })
        .skip(pageNo > 0 ? (pageNo - 1) * pageSize : 0)
        .limit(pageSize),
      db.mongo.automationRunHistory.countDocuments(filter),
    ]);
    const list = rows.map((model) => this.initWithModel(model));
    return {
      pagination: { pageNo, pageSize, total },
      list,
    };
  }

  static async findRecentTriggerOutput(automationId: string, triggerId: string): Promise<TriggerOutput | undefined> {
    // find by automation history
    const { list } = await this.find({ automationId });
    for (const history of list) {
      const triggerRunDetail = history.data.triggers.find((i) => i.id === triggerId);
      if (triggerRunDetail) {
        return triggerRunDetail.output;
      }
    }
    return undefined;
  }

  /**
   * 创建开始运行状态的记录
   */
  static async createRunningRecord(
    spaceId: string,
    automationId: string,
    triggers: TriggerRunDetailVO[],
    manual?: boolean,
    userId?: string,
  ): Promise<AutomationRunHistorySO> {
    const data: AutomationRunHistoryData = { triggers, actions: [] };
    const model = await db.mongo.automationRunHistory.create({
      id: generateNanoID('arh'),
      spaceId,
      automationId,
      status: 'RUNNING',
      data,
      manual,
      token: generateNanoID('', 8),
      createdBy: userId,
    });
    return this.initWithModel(model);
  }

  /**
   * 更新运行结果
   */
  async updateRunResult(body: { userId?: string; status: AutomationRunHistoryStatus; actions?: ActionRunDetailVO[] }) {
    const { userId, status, actions } = body;
    const updatedBy = userId || AUTOMATION_USER_ID;
    const update = actions ? { status, data: { ...this.data, actions }, updatedBy } : { status, updatedBy };
    await db.mongo.automationRunHistory.updateOne({ id: this.model.id }, update);
    this.model.status = status;
    this.model.updatedBy = updatedBy;
    if (actions) {
      this.model.data = { ...this.data, actions };
    }
  }

  toVO(): AutomationRunHistoryVO {
    return {
      id: this.model.id,
      status: this.model.status as AutomationRunHistoryStatus,
      startAt: this.model.createdAt.toISOString(),
      endAt: this.model.updatedAt?.toISOString(),
      manual: this.model.manual || false,
    };
  }

  toDetailVO(): AutomationRunHistoryDetailVO {
    return {
      ...this.toVO(),
      ...this.data,
    };
  }
}
