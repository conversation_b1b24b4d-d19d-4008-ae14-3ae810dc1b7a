// eslint-disable-next-line max-classes-per-file
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { Trigger } from '@bika/types/automation/bo';
import { AutomationTriggerVO } from '@bika/types/automation/vo';
import { CONST_PREFIX_TRIGGER } from '@bika/types/database/vo';
import { BORenderOpts, IBOProcessor } from '@bika/types/node/vo';
import { iStringParse } from '@bika/types/system';

export class TriggerBOProcessor<T extends Trigger> implements IBOProcessor<T> {
  private _trigger: T;

  constructor(trigger: T) {
    this._trigger = _.cloneDeep(trigger);
    if (!this._trigger.id) {
      this._trigger.id = generateNanoID(CONST_PREFIX_TRIGGER);
    }
  }

  get bo(): T {
    return this._trigger;
  }

  get id(): string {
    return this.bo.id!;
  }

  toVO<V>(_opts?: BORenderOpts): V | Promise<V> {
    return this.toSimpleVO(_opts) as V;
  }

  protected toSimpleVO(opts?: BORenderOpts): Omit<AutomationTriggerVO, 'bo'> {
    const { locale } = opts ?? {};
    return {
      id: this.id,
      type: this.bo.triggerType,
      description: iStringParse(this.bo.description, locale),
      isVerified: true,
    };
  }
}
