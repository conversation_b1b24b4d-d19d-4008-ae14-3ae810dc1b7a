import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { Prisma, $Enums, db, DatabaseRecordModel } from '@bika/server-orm';
import {
  TrashBO,
  TrashBOSchema,
  NodeResourceTrashBO,
  RecordTrashBO,
  TriggerTrashBO,
  ActionTrashBO,
} from '@bika/types/change/bo';
import { RecordData } from '@bika/types/database/bo';
import { RecordListDTO } from '@bika/types/database/dto';
import { CellRenderVO, RecordPaginationVO, RecordRenderVO } from '@bika/types/database/vo';
import { i18n } from '@bika/types/i18n/bo';
import { NodeResource } from '@bika/types/node/bo';
import { RenderOption } from '@bika/types/system/render-option';
import { trashDatabaseId, trashExpireDays, TrashModel, trashTableFields } from '../config/trash-table';

export class TrashSO {
  protected readonly _model: TrashModel;

  private constructor(model: TrashModel) {
    this._model = model;
  }

  get trashType(): $Enums.TrashType {
    return this._model.trashType as $Enums.TrashType;
  }

  get spaceId(): string {
    return this._model.spaceId;
  }

  get unitId(): string | null {
    return this._model.unitId;
  }

  get id(): string {
    return this._model.id;
  }

  static async init(id: string): Promise<TrashSO> {
    const model = await db.prisma.trash.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          },
        },
      },
    });
    if (!model) {
      throw new Error(`Trash not found: ${id}`);
    }
    return this.initWithModel(model as TrashModel);
  }

  static initWithModel(model: TrashModel) {
    return new TrashSO(model);
  }

  async getSpace() {
    return SpaceSO.init(this.spaceId);
  }

  static async boToCreateInput(spaceId: string, user: UserSO, trash: TrashBO): Promise<Prisma.TrashCreateInput> {
    const { trashType, bo } = trash;
    const getName = () => {
      switch (trashType) {
        case 'NODE_RESOURCE':
          return bo.resource.name;
        case 'RECORD':
          return bo.databaseName;
        case 'TRIGGER':
          return bo.trigger.description || undefined;
        case 'ACTION':
          return bo.action.description || undefined;
        default:
          return undefined;
      }
    };
    const getNodeSO = async () => {
      if (trashType === 'NODE_RESOURCE' && bo.resource.id) {
        return NodeSO.init(bo.resource.id);
      }
      if (trashType === 'RECORD') {
        return NodeSO.init(bo.databaseId);
      }
      if (trashType === 'TRIGGER') {
        return NodeSO.init(bo.automationId);
      }
      return undefined;
    };
    const node = await getNodeSO();
    const getPath = async () => {
      if (!node) return undefined;
      const parents = (await node.getParents()).map((n) => ({ id: n.id, name: n.name }));
      if (trashType === 'TRIGGER' || trashType === 'RECORD' || trashType === 'ACTION') {
        parents.splice(0, 0, { id: node.id, name: node.name });
      }
      return parents.length > 0 ? parents : undefined;
    };
    return {
      id: generateNanoID('tra'),
      spaceId,
      trashType,
      name: getName(),
      path: await getPath(),
      resourceType: trashType === 'NODE_RESOURCE' ? (bo.resource.resourceType as $Enums.NodeResourceType) : undefined,
      user: {
        connect: {
          id: user.id,
        },
      },
      updatedBy: user.id,
      unitId: node?.isPrivate ? await user.getMemberId(spaceId) : undefined,
    };
  }

  async getBO<T extends TrashBO>(): Promise<T> {
    const trashLog = await db.log.search('SPACE_TRASH_LOG', {
      where: { spaceid: this.spaceId, trashid: this.id },
    });

    if (!trashLog.records.length) {
      throw new ServerError(errors.trash.trash_not_found);
    }
    return TrashBOSchema.parse(JSON.parse(trashLog.records[0].data as string)) as T;
  }

  async recover(user: UserSO): Promise<NodeSO> {
    let nodeSO;
    switch (this.trashType) {
      case 'RECORD':
        nodeSO = await this.recoverRecords(user);
        break;
      case 'NODE_RESOURCE':
        nodeSO = await this.recoverNodeResource(user);
        break;
      case 'TRIGGER':
        nodeSO = await this.recoverTrigger(user);
        break;
      case 'ACTION':
        nodeSO = await this.recoverAction(user);
        break;
      default:
        break;
    }
    // 不需要强一致性
    if (nodeSO) {
      await this.delete();
      return nodeSO;
    }
    throw new Error(`Unsupported trash type: ${this.trashType}`);
  }

  async delete() {
    await db.prisma.trash.delete({ where: { id: this.id } });
  }

  static async getRecords(user: UserSO, space: SpaceSO, param?: RecordListDTO): Promise<RecordPaginationVO> {
    const [models, total] = await this.find(user, space, param);
    return {
      total,
      rows: models.map((model) => {
        const trashSO = this.initWithModel(model as TrashModel);
        return trashSO.toRecordRenderVO({ locale: user.locale, timeZone: user.timeZone });
      }),
    };
  }

  toRecordRenderVO(opts?: RenderOption): RecordRenderVO {
    const cells: Record<string, CellRenderVO> = {};
    for (const field of trashTableFields()) {
      if (field.toCellRendVO) {
        cells[field.id!] = field.toCellRendVO(field, this._model, opts);
      }
    }
    return {
      id: this.id,
      databaseId: trashDatabaseId(this.spaceId),
      revision: 0,
      cells,
    };
  }

  /**
   * build query input and find po list
   * @param spaceId space id
   * @param param
   * @returns
   */
  private static async find(user: UserSO, spaceSO: SpaceSO, param?: RecordListDTO) {
    const { filter, startRow, endRow, sort } = param || {};

    const unitId = await user.getMemberId(spaceSO.id);
    const owner = await spaceSO.getOwner();

    const whereInput: Prisma.TrashWhereInput = {
      spaceId: spaceSO.id,
      createdAt: {
        gte: new Date(new Date().getTime() - trashExpireDays * 24 * 60 * 60 * 1000),
      },
      AND: [
        {
          OR: [
            {
              unitId: null,
            },
            {
              unitId,
            },
          ],
        },
      ],
    };

    const fields = trashTableFields();
    const fieldMap = _.keyBy(fields, 'id');
    // filter
    if (filter && filter.conjunction === 'And') {
      for (const c of filter.conds ?? []) {
        const field = fieldMap[c.fieldId!];
        if (field && field.filter) {
          // 只有主管理员可以查看其他成员的回收站
          if (c.fieldType === 'CREATED_BY' && c.clause.value && owner.id === unitId) {
            // get userId from member(因为ui筛选用的是unitId, 这里需要做一层转换， 建议ui把member and creator 分开)
            // 区分包含/不包含和等于/不等于
            const { value } = c.clause;
            const memberIds = Array.isArray(value) ? value : [value];
            const hasSelf = memberIds.some((i) => i === 'Self');
            const filteredMemberIds = memberIds.filter((i) => i !== 'Self');
            const userIds = await MemberSO.findUserIdsByIds(filteredMemberIds);
            // 如果包含自己, 则添加当前用户id
            c.clause.value = hasSelf ? [...userIds, user.id] : userIds;
          }
          (whereInput.AND as Prisma.TrashWhereInput[]).push(field.filter(c));
        }
      }
    }

    if (owner.id !== unitId) {
      (whereInput.AND as Prisma.TrashWhereInput[]).push({
        createdBy: user.id,
      });
    }
    // 全局搜索, 支持文件路径,删除人,文件名称搜索 性能堪忧
    if (param?.keyword) {
      (whereInput.AND as Prisma.TrashWhereInput[]).push({
        OR: [
          {
            name: {
              string_contains: param.keyword,
            },
          },
          ...i18n.locales.map((lang) => ({ name: { path: [lang], string_contains: param.keyword } })),
          { path: { string_contains: param.keyword } },
          ...i18n.locales.map((lang) => ({ path: { path: ['$[*]', lang], string_contains: param.keyword } })),
          {
            user: {
              name: {
                contains: param.keyword,
              },
            },
          },
        ],
      });
    }

    const orderByInput: Prisma.TrashOrderByWithRelationInput[] = [];
    // order by toto
    if (sort) {
      sort.forEach((s) => {
        const field = fieldMap[s.fieldId!];
        if (field && field.sort) {
          orderByInput.push(field.sort(s));
        }
      });
    }

    return Promise.all([
      db.prisma.trash.findMany({
        where: whereInput,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              name: true,
              avatar: true,
            },
          },
        },
        skip: startRow || 0,
        take: endRow ? endRow - (startRow || 0) : 10,
        orderBy: orderByInput,
      }),
      db.prisma.trash.count({ where: whereInput }),
    ]);
  }

  private async recoverNodeResource(user: UserSO): Promise<NodeSO> {
    const trashData = await this.getBO<NodeResourceTrashBO>();
    const space = await SpaceSO.init(this.spaceId);
    // 检查用量
    const entitlement = await space.getEntitlement();
    await entitlement.checkUsageExceed({ feature: 'RESOURCES', value: 1 });

    const getRecoverResource = async (): Promise<{ parent: FolderSO; resource: NodeResource }> => {
      if (trashData.bo.parentId) {
        const folderPO = await FolderSO.getFolderPO(trashData.bo.parentId);
        if (folderPO) {
          return { parent: new FolderSO(folderPO.node, folderPO), resource: trashData.bo.resource };
        }
      }
      const parent = await space.getRootFolder();
      if (typeof trashData.bo.resource.name === 'string') {
        return { parent, resource: { ...trashData.bo.resource, name: `Restored_${trashData.bo.resource.name}` } };
      }
      return {
        parent,
        resource: {
          ...trashData.bo.resource,
          name: _.mapValues(trashData.bo.resource.name, (value) => `Restored_${value}`),
        },
      };
    };

    const { parent, resource } = await getRecoverResource();

    // 鉴权
    if (trashData.bo.resource.scope !== 'PRIVATE') {
      const member = await user.getMember(this.spaceId);
      const nodeAcl = await parent.toNodeSO().toAclSO();
      await nodeAcl.authorize(member, 'createNode');
    }

    const nodeId = await parent.createChildren(user, [resource], {
      scope: trashData.bo.resource.scope,
      recovery: true,
    });
    return NodeSO.init(nodeId);
  }

  private async recoverRecords(user: UserSO) {
    const trashData = await this.getBO<RecordTrashBO>();
    const database = await DatabaseSO.init(trashData.bo.databaseId);

    // check entitlement
    const space = await SpaceSO.init(this.spaceId);
    const entitlement = await space.getEntitlement();
    await entitlement.checkRecordsPerDatabaseUsage(database.id, 1);
    await entitlement.checkUsageExceed({ feature: 'RECORDS_PER_SPACE', value: 1 });

    // check permission
    const nodeAcl = await database.toNodeSO().toAclSO();
    await nodeAcl.authorize(user, 'createRow');

    const fields = _.keyBy(database.getFields(), 'id');
    const filterCells = (cells: RecordData) => {
      Object.keys(cells).forEach((key: string) => {
        if (!fields[key]) {
          // eslint-disable-next-line no-param-reassign
          delete cells[key];
        }
      });
      return cells;
    };
    const records = trashData.bo.records.map(
      (r) =>
        ({
          id: r.id,
          spaceId: this.spaceId,
          databaseId: database.id,
          templateId: r.templateId,
          data: filterCells(r.data),
          computed: {},
          values: r.values ? filterCells(r.values) : undefined,
          subscribers: [],
          revision: 0,
          status: 'OPEN',
          createdBy: user.id,
          updatedBy: user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }) as DatabaseRecordModel,
    );
    await database.createRecordsDBOperation(records, { user, member: await user.getMember(this.spaceId) });
    return database.toNodeSO();
  }

  private async recoverTrigger(user: UserSO) {
    const trashData = await this.getBO<TriggerTrashBO>();
    const automation = await AutomationSO.init(trashData.bo.automationId);

    // check permission
    const nodeAcl = await automation.toNodeSO().toAclSO();
    const member = await user.getMember(this.spaceId);
    await nodeAcl.authorize(member, 'updateNode');

    await automation.addTrigger(user, trashData.bo.trigger);
    return automation.toNodeSO();
  }

  private async recoverAction(user: UserSO) {
    const trashData = await this.getBO<ActionTrashBO>();
    const automation = await AutomationSO.init(trashData.bo.automationId);

    // check permission
    const nodeAcl = await automation.toNodeSO().toAclSO();
    const member = await user.getMember(this.spaceId);
    await nodeAcl.authorize(member, 'updateNode');

    await automation.addAction(user.id, trashData.bo.action, trashData.bo.parentActionId);
    return automation.toNodeSO();
  }
}
