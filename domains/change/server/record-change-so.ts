import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import _ from 'lodash';
import { AttachmentSO } from '@bika/domains/attachment/server/attachment-so';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server';
import { db, DatabaseRecordModel, DatabaseRecordChangeLogModel } from '@bika/server-orm';
import {
  DatabaseRecordChangeBO,
  DatabaseRecordChangeBOSchema,
  DatabaseRecordCreateChangeBO,
  DatabaseRecordUpdateChangeBO,
  RecordChangeLog,
} from '@bika/types/change/bo';
import { BaseDatabaseField, DatabaseFieldWithId, AttachmentCellData } from '@bika/types/database/bo';
import { DatabaseRecordChangeVO, AttachmentCellVO, DatabaseRecordChangeContent } from '@bika/types/database/vo';
import { Pagination } from '@bika/types/shared/pagination';
import { iStringParse, RenderOption } from '@bika/types/system';
import { ChangeSO, RecordChangeContext } from './type';

export type RecordChangeLogModel = RecordChangeLog & { createdat: string };

export class RecordChangeSO implements ChangeSO<DatabaseRecordModel> {
  private readonly _model: RecordChangeLogModel | undefined;

  private readonly _context: RecordChangeContext | undefined;

  constructor(model?: RecordChangeLogModel, context?: RecordChangeContext) {
    this._model = model;
    this._context = context;
  }

  get id(): string {
    return this._model!.id;
  }

  get model(): RecordChangeLogModel {
    return this._model!;
  }

  static initWithContext(context: RecordChangeContext): RecordChangeSO {
    return new RecordChangeSO(undefined, context);
  }

  static initWithModel(model: RecordChangeLogModel): RecordChangeSO {
    return new RecordChangeSO(model, undefined);
  }

  static init() {
    return new RecordChangeSO();
  }

  get database(): DatabaseSO {
    assert(this._context, 'RecordChangeSO context is required');
    return this._context.database as DatabaseSO;
  }

  async getSpace(): Promise<SpaceSO> {
    const database = this.database;
    return database.getSpace();
  }

  get user(): UserSO | undefined {
    assert(this._context, 'RecordChangeSO context is required');
    return this._context.user;
  }

  async getUpdatorName(spaceId?: string, userId?: string): Promise<string | null> {
    if (!userId || !spaceId) {
      return null;
    }
    return MemberSO.getNameByUserId(userId, spaceId);
  }

  get fields(): DatabaseFieldWithId[] {
    return this.database.getFields().map((f) => f.toBO());
  }

  async diff(current: DatabaseRecordModel, previous?: DatabaseRecordModel): Promise<DatabaseRecordChangeBO> {
    // Check all keys from both before and after
    const fieldMap = _.keyBy(this.fields, 'id');
    const previousFieldMap = _.keyBy(this._context?.previousFields, 'id');

    const allKeys = _.union(
      _.keys(previous?.data),
      _.keys(previous?.computed),
      _.keys(current.data),
      _.keys(current.computed),
    ).filter((key) => {
      const field = fieldMap[key];
      if (
        field?.type === 'CREATED_TIME' ||
        field?.type === 'MODIFIED_TIME' ||
        field?.type === 'CREATED_BY' ||
        field?.type === 'MODIFIED_BY' ||
        field?.type === 'AUTO_NUMBER' ||
        field?.type === 'BUTTON'
      ) {
        return false;
      }
      return true;
    });

    // Option id for storing single and multi-select fields
    const selectFieldOptionIds = new Set<string>();

    const getChangedFields = (ids: string[]) =>
      ids.reduce((acc, id) => {
        const field = fieldMap[id];
        if (field) {
          const property = _.cloneDeep(field.property);
          if (field.type === 'SINGLE_SELECT' || field.type === 'MULTI_SELECT') {
            if (property && 'options' in property) {
              property.options = property.options.filter((o) => o.id && selectFieldOptionIds.has(o.id));
            }
          }
          acc.push({
            id,
            name: field.name,
            type: field.type,
            property,
          } as BaseDatabaseField);
        }
        return acc;
      }, [] as BaseDatabaseField[]);

    const getPreviousFields = (): BaseDatabaseField[] | undefined =>
      this._context?.previousFields?.map((f) => {
        const property = _.cloneDeep(f.property);
        if (f.type === 'SINGLE_SELECT' || f.type === 'MULTI_SELECT') {
          if (property && 'options' in property) {
            property.options = property.options.filter((o) => o.id && selectFieldOptionIds.has(o.id));
          }
        }
        return {
          id: f.id!,
          name: f.name,
          type: f.type,
          property,
        } as BaseDatabaseField;
      });

    const getData = async (record: DatabaseRecordModel, fieldId: string) => {
      const field = fieldMap[fieldId];
      assert(field, `RecordChangeSO:writeLog:fail to get field ${fieldId} from fieldMap`);
      const previousField = previousFieldMap[fieldId];
      if (
        field.type === 'FORMULA' ||
        field.type === 'LOOKUP' ||
        previousField?.type === 'FORMULA' ||
        previousField?.type === 'LOOKUP'
      ) {
        return record.computed?.[fieldId];
      }
      if (field.type === 'ATTACHMENT' || previousField?.type === 'ATTACHMENT') {
        if (Array.isArray(record.data?.[fieldId])) {
          const attachments: AttachmentCellVO[] = await Promise.all(
            (record.data?.[fieldId] as AttachmentCellData[]).map(async (item: AttachmentCellData) => {
              const attachment = await AttachmentSO.initMaybeNull(item.id);
              return {
                ...item,
                thumbnailUrl: attachment?.thumbnailFullUrl,
                previewUrl: attachment?.previewFullUrl,
              };
            }),
          );
          return attachments;
        }
      }
      if (field.type === 'SINGLE_SELECT' || field.type === 'MULTI_SELECT') {
        if (Array.isArray(record.data?.[fieldId])) {
          (record.data?.[fieldId] as string[]).forEach((id) => selectFieldOptionIds.add(id));
        } else if (record.data?.[fieldId]) {
          selectFieldOptionIds.add(record.data[fieldId]);
        }
      }
      return record.data?.[fieldId];
    };

    const getValues = (record: DatabaseRecordModel, fieldId: string) => {
      const field = fieldMap[fieldId];
      assert(field, `RecordChangeSO:writeLog:fail to get field ${fieldId} from fieldMap`);
      const previousField = previousFieldMap[fieldId];
      if (
        field.type === 'LINK' ||
        field.type === 'ONE_WAY_LINK' ||
        field.type === 'LOOKUP' ||
        field.type === 'MEMBER' ||
        previousField?.type === 'LINK' ||
        previousField?.type === 'ONE_WAY_LINK' ||
        previousField?.type === 'LOOKUP' ||
        previousField?.type === 'MEMBER'
      ) {
        return record.values?.[fieldId];
      }
      return undefined;
    };

    if (!previous) {
      const changeData: DatabaseRecordCreateChangeBO['data'] = {};
      for (const key of allKeys) {
        const cellValue = await getData(current, key);
        if (cellValue) {
          changeData[key] = {
            data: cellValue,
            values: getValues(current, key),
          };
        }
      }
      return {
        recordId: current.id!,
        type: 'DATABASE_RECORD',
        changeType: 'CREATE',
        fields: getChangedFields(Object.keys(changeData)),
        data: changeData,
      };
    }

    const diffData: DatabaseRecordUpdateChangeBO['data'] = {};
    for (const key of allKeys) {
      const beforeVal = await getData(previous, key);
      const afterVal = await getData(current, key);
      if (!_.isEqual(beforeVal, afterVal)) {
        diffData[key] = {
          previous: {
            data: beforeVal,
            values: getValues(previous, key),
          },
          current: {
            data: afterVal,
            values: getValues(current, key),
          },
        };
      }
    }
    if (this._context?.previousFields?.length) {
      return {
        type: 'DATABASE_RECORD',
        changeType: 'FIELD_TYPE_CHANGE',
        data: diffData,
        fields: getChangedFields(Object.keys(diffData)),
        recordId: current.id!,
        previousFields: getPreviousFields(),
      };
    }
    return {
      type: 'DATABASE_RECORD',
      changeType: 'UPDATE',
      data: diffData,
      fields: getChangedFields(Object.keys(diffData)),
      recordId: current.id!,
    };
  }

  async writeLog(current: DatabaseRecordModel, previous?: DatabaseRecordModel) {
    await this.batchWriteLog([{ current, previous }]);
  }

  async batchWriteLog(data: { current: DatabaseRecordModel; previous?: DatabaseRecordModel }[]): Promise<void> {
    const changeBOs = await Promise.all(data.map((item) => this.diff(item.current, item.previous)));
    const mongoPOList: DatabaseRecordChangeLogModel[] = [];
    const logPOList: RecordChangeLog[] = [];
    for (const changeBO of changeBOs) {
      const changeLogId = generateNanoID('rcl');
      changeBO.updatedBy = this.user
        ? {
            userId: this.user.id,
            userName: this.user.name,
            avatar: this.user.avatar,
          }
        : undefined;
      mongoPOList.push({
        id: changeLogId,
        recordId: changeBO.recordId,
        spaceId: this.database.spaceId,
        databaseId: this.database.id,
        createdBy: this.user?.id,
        changeType: changeBO.changeType,
      } as DatabaseRecordChangeLogModel);
      logPOList.push({
        id: changeLogId,
        spaceid: this.database.spaceId,
        databaseid: this.database.id,
        recordid: changeBO.recordId,
        data: JSON.stringify(changeBO),
        kind: 'RECORD_CHANGE_LOG',
      });
    }
    // add lock Prevent openobserve from concurrent writes, MemoryTableOverflowError appears
    const lock = await db.redis.lock('lock:record_change_log', 120 * 1000);
    // console.log('lock acquired, start to write change log: ', this.database.id, new Date().toISOString());
    try {
      await db.mongo.transaction(async (session) => {
        const result = await db.log.batchWrite(logPOList);
        if (!result) {
          throw new Error(`Write change log failed: ${this.database.id}`);
        }
        await db.mongo.databaseRecordChangeLog.insertMany(mongoPOList, { session });
      });
    } finally {
      // console.log('lock released, write change log done: ', this.database.id, new Date().toISOString());
      await lock.release();
    }
  }

  async searchLog(query: { id: string; pagination?: Pagination }): Promise<{ list: RecordChangeSO[]; total: number }> {
    const { id: recordId, pagination } = query;
    const total = await db.mongo.databaseRecordChangeLog.countDocuments({ recordId });
    if (!total) {
      return {
        list: [],
        total: 0,
      };
    }
    const logIndexList: { id: string; createdAt: Date }[] = await db.mongo.databaseRecordChangeLog
      .find({
        recordId,
      })
      .select('id createdAt')
      .skip(((pagination?.pageNo ?? 1) - 1) * (pagination?.pageSize ?? 10))
      .limit(pagination?.pageSize ?? 10)
      .sort({ createdAt: -1 })
      .exec();
    const startTime = new Date(logIndexList[logIndexList.length - 1].createdAt.getTime() - 1 * 60 * 1000);
    const endTime = new Date(logIndexList[0].createdAt.getTime() + 1 * 60 * 1000);
    const sql = `SELECT * FROM "${db.log.getIndexName('RECORD_CHANGE_LOG')}" WHERE id IN (${logIndexList.map((log) => `'${log.id}'`).join(', ')})`;
    const logs = await db.log.search<RecordChangeLogModel>('RECORD_CHANGE_LOG', {
      sql,
      startTime,
      endTime,
    });
    return {
      list: logs.records.reverse().map((log) => RecordChangeSO.initWithModel(log)),
      total,
    };
  }

  async toVO(opts?: RenderOption): Promise<DatabaseRecordChangeVO> {
    assert(this._model, 'RecordChangeSO model must be initialized');
    const parsed = DatabaseRecordChangeBOSchema.safeParse(JSON.parse(this._model.data));
    if (!parsed.success) {
      console.error(`record change data [${this._model.data}] error`);
      throw new Error(`record change data error`);
    }
    const bo = parsed.data;
    const { data, fields, updatedBy, changeType } = bo;
    const previousFieldMap = _.keyBy(bo.changeType === 'FIELD_TYPE_CHANGE' ? bo.previousFields : [], 'id');
    const content: DatabaseRecordChangeContent[] = await Promise.all(
      fields.map(async (f) => ({
        field: {
          id: f.id,
          name: iStringParse(f.name, opts?.locale),
          type: f.type,
          property: f.property,
        },
        previousField: previousFieldMap[f.id!]
          ? {
              id: previousFieldMap[f.id!].id,
              name: iStringParse(previousFieldMap[f.id!].name, opts?.locale),
              type: previousFieldMap[f.id!].type,
              property: previousFieldMap[f.id!].property,
            }
          : undefined,
        data:
          changeType === 'UPDATE' || changeType === 'FIELD_TYPE_CHANGE'
            ? {
                previous: data[f.id!]?.previous?.values || data[f.id!]?.previous?.data,
                current: data[f.id!]?.current?.values || data[f.id!]?.current?.data,
              }
            : {
                current: data[f.id!]?.values || data[f.id!]?.data,
              },
      })),
    );
    return {
      id: this._model.id,
      changeType,
      createdAt: this._model.createdat,
      content,
      user: updatedBy
        ? {
            id: updatedBy.userId,
            name: (await this.getUpdatorName(this.model.spaceid, updatedBy.userId)) || updatedBy.userName,
            avatar: updatedBy.avatar || { type: 'COLOR', color: 'blue' },
          }
        : undefined,
    };
  }
}
