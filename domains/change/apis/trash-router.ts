import { z } from 'zod';
import { protectedProcedure, router } from '@bika/server-orm/trpc';
import { RecordListDTOSchema } from '@bika/types/database/dto';
import * as TrashController from './trash-controller';
import { UserSO } from '../../user/server/user-so';
import { trashTableViewVO, trashDatabaseVO } from '../config/trash-table';
import { TrashSO } from '../server/trash-so';

export const trashRouter = router({
  /**
   * enable automation.
   */
  recover: protectedProcedure.input(z.object({ trashId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TrashController.recover(user, input.trashId);
  }),

  getGridView: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    // todo check permission
    return trashTableViewVO(input.spaceId, user.locale);
  }),

  /**
   * Retrieve the records of a trash database
   */
  getRecords: protectedProcedure.input(RecordListDTOSchema).query(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TrashController.getRecords(user, input);
  }),

  nodeDetail: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return trashDatabaseVO(input.spaceId, user.locale);
  }),

  delete: protectedProcedure.input(z.object({ trashId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const trash = await TrashSO.init(input.trashId);

    const space = await trash.getSpace();
    const member = await user.getMember(space.id);
    // non-admin can only delete their own trash
    if (trash.unitId !== member.id) {
      await space.getAdminRoleAclSO().authorize(member, 'deleteTrash');
    }
    return trash.delete();
  }),
});
