import React from 'react';
import { type AIModelAction } from '@bika/types/automation/bo';
import type { ResourceFormBase } from '@bika/ui/shared/types-form/types';
import { AIModelsSelector } from '../../ai/client/ai-models/ai-models-selector/ai-models-selector';

interface Props extends ResourceFormBase {
  value: AIModelAction;
  onChange: (value: AIModelAction) => void;
}

export function AIModelActionBOInput(props: Props) {
  // const { locale, api, parentActionId } = props;

  return (
    <>
      <AIModelsSelector
        value={props.value.input.model}
        onChange={(model) => props.onChange({ ...props.value, input: { ...props.value.input, model } })}
      />
    </>
  );
}
