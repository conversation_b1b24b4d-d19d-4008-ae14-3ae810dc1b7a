import { PackageInstanceVORenderer } from '@toolsdk.ai/sdk-ts/react';
import React, { forwardRef, useImperativeHandle } from 'react';
import { useTRPC } from '@bika/api-caller/context';
import type { ToolSDKAIAction } from '@bika/types/automation/bo';
import type { AutomationActionCreateDTO } from '@bika/types/automation/dto';
import type { AutomationVO } from '@bika/types/automation/vo';
import { BIKA_PRODUCTION_URL } from 'sharelib/app-env';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import { Box } from '@bika/ui/layouts';
import type { ResourceFormBase } from '@bika/ui/shared/types-form/types';
import { VariablesTextInput } from '@bika/ui/shared/types-form/variables-text-input';
import { Skeleton } from '@bika/ui/skeleton';

export interface ToolSDKAIActionBoInputRef {
  validate?: () => Promise<boolean>;
}

interface ToolSDKAIAnyActionProps extends ResourceFormBase {
  value: ToolSDKAIAction;
  onChange: React.Dispatch<React.SetStateAction<ToolSDKAIAction>>;
  automationId?: string;
  parentActionId?: string;
}

const ToolSDKAIActionBoInputForm: React.ForwardRefRenderFunction<ToolSDKAIActionBoInputRef, ToolSDKAIAnyActionProps> = (
  props,
  ref,
) => {
  const uiFrameworkContext = useUIFrameworkContext();
  const globalContext = useGlobalContext();
  const { api, value, locale, automationId = '', parentActionId } = props;
  const formRef = React.useRef<ToolSDKAIActionBoInputRef>(null);

  const { t } = locale;

  const mutate = api.automation.useAutomationMutation();

  const { data, isLoading } = api.automation.getToolSDKAIAccountToken();

  const variables = api.automation.getAutomationGlobalVariables(value.id || parentActionId);

  const trpc = useTRPC();

  const [isCreating, setIsCreating] = React.useState(false);

  useImperativeHandle(ref, () => ({
    validate: formRef.current?.validate,
  }));

  if (isLoading) {
    return <Skeleton pos="ANY" />;
  }
  if (!data) {
    return <Box sx={{ color: 'var(--errorColor)' }}>Failed to load account token</Box>;
  }
  const variablesStringInput = (customProps: {
    value: string;
    onChange: (value: string) => void;
    label?: string;
    required?: boolean;
    helpText?: string;
  }) => (
    <VariablesTextInput
      automationVariables={variables}
      locale={locale}
      {...customProps}
      onChange={(newVal) => {
        customProps.onChange(newVal as string);
      }}
      value={customProps.value}
    />
  );

  const handleSave = async () => {
    setIsCreating(true);
    // 新建执行器
    const createDto: AutomationActionCreateDTO = {
      automationId,
      action: value,
    };
    if (parentActionId) {
      createDto.parentActionId = parentActionId;
    }
    await mutate.createAction(createDto);
    // 更新节点资源
    await api.node.invalidateNodeResourceDetail();
    // 获取最新的执行器
    const nodeDetail = await trpc.node.detail.query({
      id: automationId,
    });
    let actionId: string | undefined;
    if (parentActionId) {
      const loopAction = (nodeDetail.resource as AutomationVO)?.actions?.find(
        (action) => action.type === 'LOOP' && action.id === parentActionId,
      );
      actionId = loopAction?.actions?.slice(-1)[0]?.id;
    } else {
      const lastAction = (nodeDetail.resource as AutomationVO)?.actions?.slice(-1)[0];
      actionId = lastAction?.id;
    }

    props.onChange({
      ...value,
      id: actionId,
    });
    setIsCreating(false);
  };

  return (
    <>
      {value.id ? (
        <PackageInstanceVORenderer
          ref={formRef}
          options={{
            baseURL:
              // 优先使用环境变量，方便本地调试
              globalContext.servers.toolSDKAIBaseUrl ||
              (uiFrameworkContext.hostname === BIKA_PRODUCTION_URL
                ? 'https://toolsdk.ai/api/openapi'
                : 'https://dev.toolsdk.ai/api/openapi'),
            scope: 'DEVELOPER_STAR',
          }}
          customFields={{
            string: variablesStringInput,
            text: variablesStringInput,
          }}
          onChange={(newVal) => {
            // save in to automation action value
            props.onChange({
              ...props.value,
              input: {
                ...props.value.input,
                instanceId: newVal.instanceId,
                inputData: newVal.inputData,
                configurationInstanceId: newVal.configurationInstanceId,
                packageKey: newVal.package?.key,
                packageVersion: newVal.package?.version,
                toolKey: newVal.toolKey,
              },
            });
          }}
          accountToken={data}
          // automation action id
          consumerKey={value.id}
          defaultValue={{
            packageKey: value.input.packageKey,
            packageVersion: value.input.packageVersion,
            toolKey: value.input.toolKey,
            inputData: value.input.inputData || {},
          }}
        />
      ) : (
        <Button
          variant="soft"
          color="neutral"
          fullWidth
          startDecorator={<AddOutlined />}
          sx={{
            color: 'var(--text-primary)',
          }}
          disabled={isCreating}
          onClick={() => {
            handleSave();
          }}
        >
          {isCreating ? t.action.loading : t.automation.action.toolsdk_ai.create_and_load_toolsdk}
        </Button>
      )}
    </>
  );
};

export const ToolSDKAIActionBoInput = forwardRef(ToolSDKAIActionBoInputForm);
