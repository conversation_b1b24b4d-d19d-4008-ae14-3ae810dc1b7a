import { generateNanoID } from 'basenext/utils/nano-id';
import type { Locale } from '@bika/contents/i18n';
import { CellSOFactory } from '@bika/domains/database/server/cells/cell-factory';
import { FieldSOFactory } from '@bika/domains/database/server/fields/field-factory';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { render } from '@bika/domains/shared/server';
import { UserSO } from '@bika/domains/user/server/user-so';
import { DatabaseField } from '@bika/server-orm';
import { ManuallyTrigger, ManuallyTriggerInput, ManualTriggerOutput } from '@bika/types/automation/bo';
import { AutomationRenderOpts } from '@bika/types/automation/vo';
import { CellVO } from '@bika/types/database/vo';
import { iStringParse } from '@bika/types/system';
import { MemberVO } from '@bika/types/unit/vo';
import { getFakeMemberVO } from '../../automation/server/automation-helper';
import { AbstractTriggerOriginalBuilderHandler } from '../../automation/server/trigger/abstract-trigger-builder-handler';
import { IManualTriggerEventContext, ITriggerParams, ITriggerTestResult } from '../../automation/server/trigger/types';

/**
 * 手工触发
 */
export class ManualTriggerHandler<
  T extends ManuallyTrigger = ManuallyTrigger,
> extends AbstractTriggerOriginalBuilderHandler<T> {
  override buildCreatedInput(trigger: T) {
    const input = trigger.input;
    if (!input || !input.fields) {
      return undefined;
    }
    const fields = input.fields.map((f) => {
      if (f.id) {
        return f;
      }
      return {
        ...f,
        id: generateNanoID('fld'),
      };
    });
    return { ...input, fields };
  }

  override buildUpdatedInput(trigger: T) {
    return this.buildCreatedInput(trigger);
  }

  override fetchOutputMaybeFake(params: ITriggerParams<T>, opts?: AutomationRenderOpts): ManualTriggerOutput {
    return this.fetchCells(params.trigger, getFakeMemberVO(), opts?.locale);
  }

  override async runTest(params: ITriggerParams<T>, user: UserSO): Promise<ITriggerTestResult<ManualTriggerOutput>> {
    const { spaceId, trigger } = params;
    const member = await user.getMember(spaceId);
    const clickedMember = await member.toVO();
    return { output: this.fetchCells(trigger, clickedMember, user.locale) };
  }

  private fetchCells(trigger: T, member: MemberVO, locale?: Locale) {
    const { fields, result } = trigger.input || {};
    const fieldSOs = fields?.map((field) =>
      FieldSOFactory.newWithModel({
        id: field.id,
        templateId: field.templateId,
        name: field.name,
        type: field.type,
        property: field.property ?? null,
      } as DatabaseField),
    );
    const cells = fieldSOs ? RecordSO.fetchFakeCellData(fieldSOs, { locale }) : undefined;
    return {
      clickedMember: member,
      clickedAt: new Date().toISOString(),
      result,
      cells,
    };
  }

  override async match(params: ITriggerParams<T>, context: IManualTriggerEventContext): Promise<ManualTriggerOutput> {
    const { spaceId, triggerId, triggerTemplateId, triggerInput } = params;
    const { user, cellData } = context;

    const convertToCells = async (input: ManuallyTriggerInput) => {
      const cells: Record<string, CellVO> = {};
      const { fields } = input;
      if (!fields?.length || !cellData || Object.keys(cellData).length === 0) {
        return cells;
      }
      for (const [key, value] of Object.entries(cellData)) {
        const field = fields.find((f) => f.id === key || (f.templateId && f.templateId === key));
        if (!field?.id || !value) {
          continue;
        }
        // input.fields 共享字段UI组件一时爽，output构建cell数据火葬场（无真实field、record数据）
        const fieldSO = FieldSOFactory.newWithModel({
          id: field.id,
          templateId: field.templateId,
          name: field.name,
          type: field.type,
          property: field.property ?? null,
        } as DatabaseField);
        const data = fieldSO.processCellDataOnCreate(value, { user, member: null, now: new Date() });
        const cellSO = CellSOFactory.newByFakeRecord(
          {
            spaceId,
            data: { [field.id]: data },
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          fieldSO,
        );
        const cell = {
          id: field.id,
          name: iStringParse(field.name, user.locale),
          data,
          value: await cellSO.doValue(),
        };
        cells[field.id] = cell;
        if (field.templateId && !cellData[field.templateId]) {
          cells[field.templateId] = cell;
        }
      }
      return cells;
    };

    const fetchTriggersAndPartOutput = async () => {
      if (!triggerInput) {
        return {};
      }
      const input = triggerInput;
      const cells = await convertToCells(input);

      const { result } = input;
      // 未设置运行结果，或者未传入字段数据，不用考虑变量渲染问题
      if (!result || Object.keys(cells).length === 0) {
        return { result, cells };
      }
      // 渲染运行结果
      const data: { [key: string]: unknown } = {};
      data[triggerId] = { cells };
      if (triggerTemplateId) {
        data[triggerTemplateId] = { cells };
      }
      return { result: render(result, { _triggers: data }), cells };
    };

    // 获取触发器等信息
    const { result, cells } = await fetchTriggersAndPartOutput();
    // 获取成员信息
    const member = await user.getMember(spaceId);
    const clickedMember = await member.toVO();
    return {
      clickedMember,
      clickedAt: new Date().toISOString(),
      result,
      cells,
    };
  }
}
