/* eslint-disable @typescript-eslint/no-explicit-any */
import { TiptapTransformer } from '@hocuspocus/transformer';
import { generateJSON, generateHTML } from '@tiptap/html';
import { generateNanoID } from 'basenext/utils/nano-id';
import { XMLParser } from 'fast-xml-parser';
import _ from 'lodash';
import * as marked from 'marked';
import * as Y from 'yjs';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { NodeResourceSO } from '@bika/domains/node/server/types';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { db, type $Enums, type Prisma, type PrismaPromise } from '@bika/server-orm';
import type { DocVO } from '@bika/types/database/vo';
import type { DocumentBO } from '@bika/types/document/bo';
import type { DocumentCreateDTO, DocumentUpdateDTO } from '@bika/types/document/dto';
import { iStringParse, LocaleType, type iString } from '@bika/types/i18n/bo';
import type { ToBoOptions, NodeResourceType, ToTemplateOptions } from '@bika/types/node/bo';
import type { NodeRenderOpts, ResourceVO } from '@bika/types/node/vo';
import { DocumentEditorExtentions } from '@bika/ui/editor/document-editor/doc-extensions';
import { html2mk } from '@bika/ui/editor/rich-text-editor/html2mk';

export type DocPGModel = Prisma.DocumentGetPayload<{
  include: {
    node: true;
    documentData: true;
  };
}>;

/**
 * 文档服务对象，可被Doc Field、Doc Node等多个地方共享引用
 */
export class DocSO extends NodeResourceSO {
  private _pgModel: DocPGModel;

  private constructor(private readonly docPO: DocPGModel) {
    super();
    this._pgModel = docPO;
  }

  get id() {
    return this.model.id;
  }

  get spaceId(): string {
    return this._pgModel.spaceId;
  }

  get resourceType(): NodeResourceType {
    return 'DOCUMENT';
  }

  get templateId(): string | undefined {
    return this._pgModel.templateId || undefined;
  }

  get name(): iString {
    return this.model.name as iString;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  get model() {
    return this._pgModel;
  }

  get json() {
    return this._pgModel.json;
  }

  get data() {
    return this._pgModel.documentData.data;
  }

  public static async init(id: string) {
    const docPO = await db.prisma.document.findUnique({ where: { id }, include: { node: true, documentData: true } });

    return new DocSO(docPO!);
  }

  public static initWithModel(model: DocPGModel) {
    return new DocSO(model);
  }

  static async findNameById(id: string): Promise<iString | null> {
    const PO = await db.prisma.document.findUnique({ where: { id }, include: { node: true, documentData: true } });
    if (!PO) {
      return null;
    }
    return PO && (PO.name as iString);
  }

  async getSpace(): Promise<SpaceSO> {
    if (this._pgModel.node) {
      return this.toNodeSO().getSpace();
    }
    return SpaceSO.init(this.spaceId);
  }

  toNodeSO(): NodeSO {
    // 当前文档如果只是一个表格的一个字段, 它不能转换成Node对象
    if (!this._pgModel.node) {
      throw new Error('Document is not a resource node');
    }
    return NodeSO.initWithModel(this._pgModel.node);
  }

  /**
   * 文档tiptap存储的是json格式的数据, 抓换成markdown变成BO
   * @param opts
   */
  async toBO(_opts?: ToBoOptions): Promise<DocumentBO> {
    return {
      id: this._pgModel.id,
      templateId: this.model?.templateId || undefined,
      resourceType: 'DOCUMENT',
      //
      name: this._pgModel.name as iString,
      description: (this._pgModel.description as iString) || undefined,
      markdown: this.toMarkdown(),
      json: this.toJSON(),
      icon: this.toNodeSO().icon,
    };
  }

  async toVO(_opts?: NodeRenderOpts): Promise<ResourceVO> {
    return {
      resourceType: 'DOCUMENT',
      id: this._pgModel.id,
      summary: this._pgModel.summary,
    };
  }

  public toDocVO(): DocVO {
    return {
      id: this._pgModel.id,
      name: iStringParse(this._pgModel.name as iString),
      markdown: this.toMarkdown(),
    };
  }

  async toTemplate(_opts?: ToTemplateOptions): Promise<DocumentBO> {
    const doc = _.omit(await this.toBO(), 'id');
    if (!doc.templateId) {
      doc.templateId = this.id;
    }
    return doc;
  }

  async setTemplateId(): Promise<PrismaPromise<Prisma.BatchPayload>[]> {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.document.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: {
              templateId: this.id,
            },
          }),
        ],
      );
    }
    return operations;
  }

  /**
   * database 字段的doc 创建方式
   *
   * @param userId
   * @param spaceId
   * @param name
   * @param markdown
   * @returns
   */
  public static async create(userId: string, spaceId: string, name: string, markdown: string): Promise<DocSO> {
    const docId = generateNanoID('doc');
    const { json, html, buffer } = this.parseMarkdown(markdown);
    const po = (await db.prisma.document.create({
      data: {
        id: docId,
        relationType: 'DATABASE_FIELD',
        spaceId,
        documentData: {
          create: {
            id: docId,
            data: buffer ? new Uint8Array(buffer) : undefined,
          },
        },
        name,
        description: '',
        createdBy: userId,
        updatedBy: userId,
        json,
        html,
      },
    })) as DocPGModel;

    return DocSO.initWithModel(po);
  }

  /**
   * database 字段的doc 创建方式
   *
   * @param userId
   * @param spaceId
   * @param name
   * @param markdown
   * @returns
   */
  public static createByJsonString(
    userId: string,
    spaceId: string,
    name: string,
    jsonString: string,
  ): { operation: PrismaPromise<DocPGModel>; id: string } {
    const buffer = this.parseJSON(jsonString);
    const docId = generateNanoID('doc');
    const operation = db.prisma.document.create({
      data: {
        id: docId,
        relationType: 'DATABASE_FIELD',
        spaceId,
        documentData: {
          create: {
            id: docId,
            data: buffer ? new Uint8Array(buffer) : undefined,
          },
        },
        name,
        description: '',
        createdBy: userId,
        updatedBy: userId,
        json: jsonString,
      },
    }) as unknown as PrismaPromise<DocPGModel>;
    return { operation, id: docId };
  }

  public static async updateName(id: string, name: string) {
    return db.prisma.document.update({
      where: {
        id,
      },
      data: {
        name,
      },
    });
  }

  // DocumentUncheckedCreateNestedOneWithoutNodeInput

  updateWithNodeInput(userId: string, data: DocumentUpdateDTO): PrismaPromise<DocPGModel> {
    const { name, description, summary, json } = data;
    return db.prisma.document.update({
      where: {
        id: this.id,
      },
      data: {
        name,
        description,
        updatedBy: userId,
        summary,
        json,
        node: {
          update: {
            name,
            description,
            updatedBy: userId,
          },
        },
      } as Prisma.MirrorUpdateInput,
      include: {
        node: true,
        documentData: true,
      },
    });
  }

  async updateData(userId: string, data: Buffer) {
    const docData = await DocSO.updateDocumentData(userId, this.id, data);
    this._pgModel.documentData.data = docData.data;
  }

  public static async writeDocumentData(userId: string, id: string, data: Buffer) {
    const po = await this.updateDocumentData(userId, id, data);
    return po.data;
  }

  public static updateDocumentData(userId: string, id: string, data: Buffer) {
    return db.prisma.documentData.upsert({
      where: {
        id,
      },
      create: {
        id,
        data,
      },
      update: {
        id,
        data,
        document: {
          update: {
            updatedBy: userId,
          },
        },
      },
    });
  }

  // Document Data表
  public static async getDocumentData(id: string) {
    const po = await db.prisma.documentData.findUnique({ where: { id } });
    return po && po.data;
  }

  public static async getDocumentDataById(id: string): Promise<DocSO | null> {
    const po = await db.prisma.document.findUnique({
      where: { id },
      include: {
        node: true,
        documentData: true,
      },
    });
    return po && this.initWithModel(po);
  }

  public static async getDocumentsByIds(ids: string[]): Promise<DocSO[]> {
    if (ids.length === 0) {
      return [];
    }
    const pos = await db.prisma.document.findMany({
      where: {
        id: {
          in: ids,
        },
      },
      include: {
        node: true,
        documentData: true,
      },
    });
    return pos.map((po) => DocSO.initWithModel(po));
  }

  public static async getDocumentDataByIds(ids: string[]): Promise<DocSO[]> {
    if (ids.length === 0) {
      return [];
    }
    const pos = (await db.prisma.document.findMany({
      where: {
        id: {
          in: ids,
        },
      },
      include: {
        documentData: true,
      },
    })) as DocPGModel[];
    return pos.map((po) => DocSO.initWithModel(po));
  }

  public static async getDocumentJsonStringByIds(
    ids: string[],
  ): Promise<Record<string, { value: string; name: string }>> {
    const docs = await this.getDocumentDataByIds(ids);
    return docs.reduce(
      (acc, doc) => {
        const json = doc.toJSON();
        if (json) {
          acc[doc.id] = { value: JSON.stringify(json), name: iStringParse(doc.name) };
        }
        return acc;
      },
      {} as Record<string, { value: string; name: string }>,
    );
  }

  static boToCreateInput(
    userId: string,
    spaceId: string,
    nodeId: string,
    data: DocumentCreateDTO,
    attachType: $Enums.DocumentAttachType = 'NODE',
  ): Prisma.DocumentUncheckedCreateNestedOneWithoutNodeInput | undefined {
    if (data.resourceType === 'DOCUMENT') {
      // markdown to html, html to y.doc via tiptap Transformer
      let buffer;
      let html: string | undefined;
      let json;

      if (data.markdown) {
        // 使用不依赖extensions的方法
        const res = this.parseMarkdownWithoutExtensions(data.markdown);
        html = res.html;
        buffer = res.buffer;
        json = res.json;
        console.log('🚀 ~ DocSO.boToCreateInput ~ html:', res.html);
      }
      if (data.json) {
        buffer = this.parseJSON(data.json);
        json = data.json;
      }
      return {
        create: {
          id: nodeId,
          // revision: 0,
          relationType: attachType,
          // relationId: nodeId, // relationId自动补上node了
          spaceId,
          documentData: {
            connectOrCreate: {
              where: {
                id: nodeId,
              },
              create: {
                id: nodeId,
                data: buffer ? new Uint8Array(buffer) : undefined,
              },
            },
          },
          name: data.name,
          description: data.description,
          templateId: data.templateId,
          createdBy: userId,
          updatedBy: userId,
          json,
          html,
        },
      };
    }

    return undefined;
  }

  /**
   * markdown -> html -> json -> y.doc -> buffer
   * @param markdown
   * @returns
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static parseMarkdown(markdown: string): { json: any; html: string; buffer: Buffer } {
    const html = marked.parse(markdown) as string;
    const json = generateJSON(html, DocumentEditorExtentions);
    const ydoc = TiptapTransformer.toYdoc(json, 'default', DocumentEditorExtentions);
    const bytes = Y.encodeStateAsUpdate(ydoc);
    const buffer = Buffer.from(bytes);
    return {
      json,
      html,
      buffer,
    };
  }

  /**
   * markdown -> html -> json -> y.doc -> buffer (不依赖extensions)
   * @param markdown
   * @returns
   */
  static parseMarkdownWithoutExtensions(markdown: string): { html: string; json: any; buffer: Buffer } {
    const html = marked.parse(markdown) as string;
    const xml = this.htmlToXML(html);

    // 将 XML 转换为 Y.Doc buffer
    const buffer = this.xmlToYDocBuffer(xml) || Buffer.alloc(0);

    const json = this.bufferToJSON(buffer);

    return {
      html,
      json,
      buffer: buffer || Buffer.alloc(0),
    };
  }

  /**
   * 将 XML 字符串转换为 Y.Doc buffer
   * @param xml XML 字符串
   * @returns Buffer
   */
  static xmlToYDocBuffer(xml: string): Buffer | undefined {
    // 递归将 fast-xml-parser 解析的对象转为 Y.XmlElement/Y.XmlText
    const walkXmlNode = (node: any): Y.XmlElement | Y.XmlText => {
      if (typeof node === 'string') {
        // Text node
        return new Y.XmlText(node);
      }
      if (typeof node === 'object' && node !== null) {
        // Element node
        const tagName = Object.keys(node)[0];
        if (!tagName || tagName === '#text') {
          // Text content
          return new Y.XmlText(node['#text'] || '');
        }

        const element = new Y.XmlElement(tagName);
        const nodeContent = node[tagName];

        if (nodeContent && typeof nodeContent === 'object' && !Array.isArray(nodeContent)) {
          // Handle attributes if present
          if (nodeContent[':@']) {
            const attrs = nodeContent[':@'];
            Object.keys(attrs).forEach((attrName) => {
              element.setAttribute(attrName, attrs[attrName]);
            });
          }

          // Handle child nodes
          Object.keys(nodeContent).forEach((key) => {
            if (key !== ':@' && key !== '#text') {
              const childNode = nodeContent[key];
              if (Array.isArray(childNode)) {
                childNode.forEach((child) => {
                  element.insert(element.length, [walkXmlNode({ [key]: child })]);
                });
              } else {
                element.insert(element.length, [walkXmlNode({ [key]: childNode })]);
              }
            }
          });

          // Handle text content
          if (nodeContent['#text']) {
            element.insert(element.length, [new Y.XmlText(nodeContent['#text'])]);
          }
        } else if (Array.isArray(nodeContent)) {
          // Handle array of child nodes
          nodeContent.forEach((child) => {
            element.insert(element.length, [walkXmlNode({ [tagName]: child })]);
          });
        } else if (typeof nodeContent === 'string') {
          // Simple text content
          element.insert(element.length, [new Y.XmlText(nodeContent)]);
        }

        return element;
      }
      return new Y.XmlText('');
    };

    try {
      const ydoc = new Y.Doc();
      const xmlFragment = ydoc.getXmlFragment('default');

      // Use fast-xml-parser instead of DOMParser
      const parser = new XMLParser({
        ignoreAttributes: false,
        attributeNamePrefix: '',
        textNodeName: '#text',
        parseAttributeValue: true,
        parseTagValue: true,
        trimValues: true,
      });

      const parsedXml = parser.parse(`<root>${xml}</root>`);
      const root = parsedXml.root;

      if (root) {
        Object.keys(root).forEach((key) => {
          if (key !== ':@') {
            const childNode = root[key];
            if (Array.isArray(childNode)) {
              childNode.forEach((child) => {
                xmlFragment.insert(xmlFragment.length, [walkXmlNode({ [key]: child })]);
              });
            } else {
              xmlFragment.insert(xmlFragment.length, [walkXmlNode({ [key]: childNode })]);
            }
          }
        });
      }

      return Buffer.from(Y.encodeStateAsUpdate(ydoc));
    } catch (e) {
      console.error('xmlToYDocBuffer error', e);
      return undefined;
    }
  }

  /**
   * 将HTML转换为XML格式
   * @param html
   * @returns
   */
  static htmlToXML(html: string): string {
    if (!html) return '';

    let xml = html;

    // 处理 heading 标签（h1-h6 转换为 heading 并添加 level 属性）
    xml = xml.replace(
      /<h(\d)([^>]*)>(.*?)<\/h\1>/g,
      (_match, level, attrs, content) => `<heading level="${level}"${attrs}>${content}</heading>`,
    );

    // 处理其他标签的映射（与 xmlToHTML 中的 tagMap 相反）
    const tagMap: Record<string, string> = {
      h1: 'title',
      p: 'paragraph',
      ul: 'bulletList',
      ol: 'orderedList',
      li: 'listItem',
      blockquote: 'blockquote',
      pre: 'codeBlock',
      code: 'code',
      strong: 'bold',
      b: 'bold',
      em: 'italic',
      i: 'italic',
      u: 'underline',
      del: 'strike',
      s: 'strike',
      a: 'link',
      img: 'image',
    };

    // 替换所有标签（除了已经处理的 heading）
    Object.entries(tagMap).forEach(([htmlTag, xmlTag]) => {
      const regex = new RegExp(`<${htmlTag}([^>]*)>(.*?)</${htmlTag}>`, 'g');
      xml = xml.replace(regex, `<${xmlTag}$1>$2</${xmlTag}>`);
    });

    // 处理自闭合标签（如 img）
    xml = xml.replace(/<img([^>]*)\/>/g, '<image$1></image>');

    return xml;
  }

  /**
   * 简单的 XML 到 HTML 转换
   * @param xmlString XML 字符串
   * @returns HTML 字符串
   */
  static xmlToHTML(xmlString: string): string {
    if (!xmlString) return '';

    let html = xmlString;

    // 处理 heading 标签（需要根据 level 属性确定标签级别）
    html = html.replace(/<heading([^>]*level="(\d+)"[^>]*)>(.*?)<\/heading>/g, (_match, attrs, level, content) => {
      const headingTag = `h${level}`;
      return `<${headingTag}${attrs}>${content}</${headingTag}>`;
    });

    // 处理没有 level 属性的 heading 标签（默认为 h1）
    html = html.replace(/<heading([^>]*)>(.*?)<\/heading>/g, (_match, attrs, content) => `<h1${attrs}>${content}</h1>`);

    // 处理其他标签的映射
    const tagMap: Record<string, string> = {
      title: 'h1',
      paragraph: 'p',
      bulletList: 'ul',
      orderedList: 'ol',
      listItem: 'li',
      blockquote: 'blockquote',
      codeBlock: 'pre',
      code: 'code',
      bold: 'strong',
      italic: 'em',
      underline: 'u',
      strike: 'del',
      link: 'a',
      image: 'img',
    };

    // 替换所有标签（除了已经处理的 heading）
    Object.entries(tagMap).forEach(([xmlTag, htmlTag]) => {
      const regex = new RegExp(`<${xmlTag}([^>]*)>(.*?)</${xmlTag}>`, 'g');
      html = html.replace(regex, `<${htmlTag}$1>$2</${htmlTag}>`);
    });

    return html;
  }

  // buffer -> y.doc -> html -> markdown
  static bufferToMarkdown(buffer: Buffer) {
    const html = this.bufferToHTML(buffer);
    const markdown = this.htmlToMarkdown(html);
    return markdown;
  }

  /**
   * buffer -> y.doc -> json object
   * @param buffer buffer
   * @returns json object {default: {content: [{type: 'paragraph', content: [{type: 'text', text: 'Hello, world!'}]}]} or {}
   */
  static bufferToJSON(buffer: Buffer) {
    const doc = new Y.Doc();
    const uint8Array = new Uint8Array(buffer);
    Y.applyUpdate(doc, uint8Array);
    return TiptapTransformer.fromYdoc(doc);
  }

  // buffer -> y.doc -> json -> html
  static bufferToHTML(buffer: Buffer) {
    const json = this.bufferToJSON(buffer);
    if (_.isEmpty(json)) {
      return '';
    }
    const jsonDoc = this.filterNodeType(
      json.default,
      DocumentEditorExtentions.map((i) => i.name),
    );
    if (jsonDoc) {
      const html = generateHTML(jsonDoc, DocumentEditorExtentions);
      const markdown = this.htmlToMarkdown(html);
      return markdown;
    }
    return '';
  }

  static dataToMarkdown(buffer: Uint8Array<ArrayBufferLike>) {
    const doc = new Y.Doc();
    Y.applyUpdate(doc, buffer);

    const xmlFragment = doc.getXmlFragment('default');

    const xmlString = xmlFragment.toString();

    // 将 XML 转换为 HTML，然后使用现有的 html2mk 工具
    const html = this.xmlToHTML(xmlString);

    const markdown = this.htmlToMarkdown(html);
    return markdown;
  }

  static htmlToMarkdown(html: string) {
    const markdown = html2mk(html);
    return markdown;
  }

  // y.doc -> html -> markdown
  toMarkdown(): string {
    const bytes = this.data;
    if (!bytes || bytes.length === 0) return '';
    return DocSO.dataToMarkdown(bytes);
  }

  /**
   *
   * @returns
   * console.log(json) // { type: 'doc', content: [{ type: 'paragraph', content: [{ type: 'text', text: 'Hello, world!' }] }] }
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  toJSON(): any {
    const bytes = this._pgModel.documentData.data;
    if (!bytes || bytes.length === 0) return undefined;
    return DocSO.bufferToJSON(Buffer.from(bytes));
  }

  /**
   * 递归校验节点类型是否合法, 并返回合法的jsonDoc
   * @param jsonDoc
   * @param availableNodes
   * @returns availableJsonDoc
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static filterNodeType(jsonDoc: any, availableNodes: string[]) {
    const cloneDoc = _.cloneDeep(jsonDoc);
    // Check if the current node is valid
    if (!availableNodes.includes(cloneDoc.type)) {
      return null;
    }

    // If the node has content, apply validation to each child
    if (cloneDoc.content) {
      cloneDoc.content = cloneDoc.content
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((child: any) => {
          const marks = child.marks
            ?.map((mark: { type: string }) => {
              if (!availableNodes.includes(mark.type)) {
                return null;
              }
              return mark;
            })
            ?.filter((mark: { type: string }) => mark !== null);
          return DocSO.filterNodeType({ ...child, marks }, availableNodes);
        })
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .filter((child: any) => child !== null);
    }
    return cloneDoc;
  }

  /**
   * 使用原生的yjs,将json转换为buffer,去除对extensions的依赖
   * @param json
   * @returns
   */
  static parseJSON(json: any): Buffer | undefined {
    try {
      const ydoc = new Y.Doc();
      const xmlFragment = ydoc.getXmlFragment('default');

      // 递归处理节点的函数
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const processNode = (node: any): Y.XmlElement | Y.XmlText => {
        if (node.type === 'text') {
          return new Y.XmlText(node.text);
        }

        const element = new Y.XmlElement(node.type);

        // 处理属性
        if (node.attrs) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          Object.entries(node.attrs).forEach(([key, value]) => {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            element.setAttribute(key as string, value as string);
          });
        }

        // 递归处理子节点
        if (node.content) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const children = node.content.map((child: any) => processNode(child));
          element.insert(0, children);
        }

        return element;
      };

      const docData = typeof json === 'object' ? json : JSON.parse(json);
      // 处理文档内容
      if (docData.default?.content) {
        const elements = docData.default.content.map(processNode);
        xmlFragment.insert(0, elements);
      }
      return Buffer.from(Y.encodeStateAsUpdate(ydoc));
    } catch (error) {
      console.error('parse doc json error', error);
      return undefined;
    }
  }
}
