# FROM node:20-alpine AS base
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
# RUN apk add --no-cache libc6-compat make
FROM node:22-bookworm AS base


RUN apt update && apt install -y rsync build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev

# enable pnpm
RUN corepack enable pnpm && corepack prepare pnpm@latest-10 --activate
# install bun
RUN npm install -g bun


FROM base AS builder

WORKDIR /app

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

COPY . .

# Load Env config
SHELL ["/bin/bash", "-c"] 
RUN [ -f apps/web/.env.local ] \
    && source apps/web/.env.local

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED 1

# use pnpm cache & pnpm install 
RUN --mount=type=cache,id=pnpm,target=/pnpm/store export PUPPETEER_SKIP_DOWNLOAD=true && export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true && pnpm install --frozen-lockfile

# build web
RUN cd apps/web && npm run build

# rename node_modules
RUN mv apps/web/.next/standalone/node_modules apps/web/.next/standalone/web_node_modules

# RUN build api server (bun + bun exe + node + doc)
RUN cd apps/server && npm run build

# COPY native lib for server
RUN mkdir native_node_modules \
    && find ./node_modules -name "*.node" -type f -exec cp --parents {} ./native_node_modules \; \
    && cp -a ./node_modules/prisma ./native_node_modules/node_modules/prisma \
    && rsync -a ./node_modules/@img/ ./native_node_modules/node_modules/@img/ \
    && cp -a ./node_modules/prettier ./native_node_modules/node_modules/prettier \
    && cp -a ./node_modules/\@node-rs/* ./native_node_modules/node_modules/\@node-rs \
    && cp -a ./node_modules/\@next/* ./native_node_modules/node_modules/\@next/ 


# build init  image with merge node_modules (web)
FROM base AS init-builder
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

### For init seed

COPY ./package.json ./package.json
COPY ./pnpm-lock.yaml ./pnpm-lock.yaml
COPY ./pnpm-workspace.yaml ./pnpm-workspace.yaml

COPY ./packages/bika-server-orm ./packages/bika-server-orm
COPY ./packages/sharelib  ./packages/sharelib
COPY ./projects/bika/packages/bika-types ./projects/bika/packages/bika-types
COPY ./packages/bika-dev-content-server ./packages/bika-dev-content-server
COPY ./projects/toolsdk/basenext  ./projects/toolsdk/basenext
COPY ./projects/toolsdk/packages/sdk-ts  ./projects/toolsdk/packages/sdk-ts

COPY ./packages/bika-server-orm/package.json  ./packages/bika-server-orm/package.json
COPY ./packages/bika-server-orm/tsconfig.json  ./packages/bika-server-orm/tsconfig.json

COPY ./contents ./contents

# delay pnpm install  
COPY --from=builder /app/package.json ./

RUN --mount=type=cache,id=pnpm,target=/pnpm/store export PUPPETEER_SKIP_DOWNLOAD=true && export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true && pnpm install --frozen-lockfile

COPY --from=builder /app/apps/web/public ./apps/web/public

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
# Merge node_modules
RUN cp -arn web_node_modules/* ./node_modules || echo "skip duplicate directory" \
    && rm -rf web_node_modules


# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Copy init and web
COPY --from=init-builder /app ./

### Copy api server
COPY --from=builder /app/native_node_modules/* ../node_modules
COPY --from=builder /app/apps/server/build ./
COPY --from=builder /app/apps/server/dist ./
COPY --from=builder /app/apps/server/bika-server ./

# init run as root
# USER nextjs

ENV NODE_ENV=production

EXPOSE 3000

ENV PORT=3000
# set hostname for run
ENV HOSTNAME "0.0.0.0"

ARG RELEASE_DATE
ENV RELEASE_DATE=${RELEASE_DATE}

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "apps/web/server.js"]


# WORKDIR /app
# CMD ['node', "server-doc.js"]

# WORKDIR /app/packages/server-orm
# CMD ["sh", "-c", "cd /app/packages/bika-server-orm && npm run db:deploy && npm run db:seed"]
