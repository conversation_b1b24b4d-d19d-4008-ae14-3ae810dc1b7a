name: 'Test'
description: 'make test and build for testing'
# inputs:
#   who-to-greet:  # id of input
#     description: 'Who to greet'
#     required: true
#     default: 'World'
# outputs:
#   random-number:
#     description: "Random number"
#     value: ${{ steps.random-number-generator.outputs.random-number }}
runs:
  using: 'composite'
  steps:
    - name: Run lint
      shell: bash
      run: |
        make lint
        pnpm check

    - name: Run Tests
      shell: bash
      run: |
        # make test-formapp
        make test-toolsdk
        make test-bika

    - name: Run Build for Testing
      shell: bash
      run: |
        make build-web
        make build-desktop
        # make build-formapp
        make build-toolsdk
        make build-server
        # make build-editor-vite
        # make build-editor-next
        make build-storybook
    # 由于local-db action，改成了每次都 reset db，这里不关数据库
    # - name: cleanup
    #   shell: bash
    #   run: |
    #     make db-kill
