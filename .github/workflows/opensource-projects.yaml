name: Open Source Projects (develop)

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_DEV_PROJECT_ID }}
  GITHUB_BUILD_NUMBER: ${{ github.run_number }}

on:
  workflow_dispatch:
  # pull_request:
  #   types: [opened, synchronize, reopened]
  #   branches:
  #     - develop

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: '${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}'
  cancel-in-progress: true

jobs:
  bika:
    name: Open Source Bika
    runs-on: github-runners-set-k3s
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Open Source ToolSDK project
        shell: bash
        run: |
          make opensource-toolsdk

      - name: Open Source Bika project
        shell: bash
        run: |
          make opensource-bika
