import { z } from 'zod';
import { PresetLanguageAIModelDef, PresetLanguageAIModelDefSchema, PresetLanguageAIModelDefs } from '@bika/types/ai/bo';
import { AIModelPrice, AIModelPriceSchema } from '@bika/types/pricing/bo';
import aiModelPricesJSON from './ai-models-price.json';

const RecordMapSchema = z.record(PresetLanguageAIModelDefSchema.or(z.literal('default')), AIModelPriceSchema);
// type IRecordMap = z.infer<typeof RecordMapSchema>;

type RecordMap = Record<PresetLanguageAIModelDef | 'default', AIModelPrice>;
let _cacheAIModelPrices: RecordMap | undefined;

/**
 * lazy load sku configs
 * @returns
 */
const getAIModelPricesConfigs = () => {
  if (_cacheAIModelPrices) return _cacheAIModelPrices!;
  const safeParse = RecordMapSchema.safeParse(aiModelPricesJSON);
  if (safeParse.success === false) {
    throw new Error(safeParse.error.message);
  }

  _cacheAIModelPrices = safeParse.data as RecordMap;

  // 找出不同的模型，并打印出来，报错
  const configuredModels = new Set(Object.keys(_cacheAIModelPrices));
  const presetModels = new Set(PresetLanguageAIModelDefs);
  const missingModels = [...presetModels].filter((model) => !configuredModels.has(model));
  const extraModels = [...configuredModels].filter(
    (model) => model !== 'default' && !presetModels.has(model as PresetLanguageAIModelDef),
  );

  if (missingModels.length > 0 || extraModels.length > 0) {
    console.error('AI Models configuration mismatch:');
    if (missingModels.length > 0) {
      console.error('Missing models in config:', missingModels);
    }
    if (extraModels.length > 0) {
      console.error('Extra models in config:', extraModels);
    }
    throw new Error(
      `AI Models configuration mismatch. Missing: ${missingModels.join(', ')}, Extra: ${extraModels.join(', ')}`,
    );
  }

  return _cacheAIModelPrices!;
};

export const getAIModelPricesConfig = (model: PresetLanguageAIModelDef | 'default') => {
  const configs = getAIModelPricesConfigs();
  const config = configs[model];
  if (!config) {
    console.warn(`AI model ${model} not found, using default config, plese set AI models price table!`);
    return configs.default;
  }
  return config;
};
