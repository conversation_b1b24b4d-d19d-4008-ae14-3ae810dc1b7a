{"mock": {"inputCredit": 1, "outputCredit": 2}, "qwen/qwen-turbo": {"inputCredit": 1, "outputCredit": 2}, "qwen/qwen-plus": {"inputCredit": 2, "outputCredit": 6}, "qwen/qwen-pro": {"inputCredit": 5, "outputCredit": 28}, "qwen/qwen3-coder": {"inputCredit": 5, "outputCredit": 28}, "bytedance/doubao-lite-128k": {"inputCredit": 3, "outputCredit": 3}, "bytedance/doubao-pro-256k": {"inputCredit": 15, "outputCredit": 26}, "bytedance/doubao-pro-32k": {"inputCredit": 3, "outputCredit": 3}, "deepseek/deepseek-v3": {"inputCredit": 6, "outputCredit": 23}, "deepseek/deepseek-r1": {"inputCredit": 12, "outputCredit": 46}, "google/gemini-2.5-flash": {"inputCredit": 12, "outputCredit": 46}, "google/gemini-2.5-pro": {"inputCredit": 12, "outputCredit": 46}, "openai/gpt-4o-mini": {"inputCredit": 3, "outputCredit": 12}, "openai/gpt-4o": {"inputCredit": 42, "outputCredit": 200}, "openai/gpt-4.1": {"inputCredit": 34, "outputCredit": 160}, "openai/gpt-4.1-mini": {"inputCredit": 8, "outputCredit": 32}, "openai/o3": {"inputCredit": 29, "outputCredit": 160}, "openai/o4-mini": {"inputCredit": 19, "outputCredit": 88}, "anthropic/claude-sonnet-4": {"inputCredit": 50, "outputCredit": 300}, "anthropic/claude-opus-4": {"inputCredit": 215, "outputCredit": 1500}, "anthropic/claude-sonnet-3.7": {"inputCredit": 50, "outputCredit": 300}, "anthropic/claude-haiku-3.5": {"inputCredit": 16, "outputCredit": 80}}