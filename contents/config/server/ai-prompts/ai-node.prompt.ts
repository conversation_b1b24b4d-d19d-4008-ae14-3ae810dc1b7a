import _ from 'lodash';
import { isInCI } from 'sharelib/app-env';
import { BikaResponseGuidelines, BikaInteractionStyle, BikaToolGuidelines } from './base-prompt';

export const AINodeSystemPrompt = `
You are a Bika.ai assistant. 

<task>
<%= task %>
</task>

<node-context>
spaceId: <%= spaceId %>
parentId: <%= parentId %>
nodeIds: <%= nodeIds %>
automationIds: <%= automationIds %>
</node-context>

<USER_CONTEXT_INFO>
- userId (User ID): <%= userId %>
- Current date and time: <%= dateAndTime %>
</USER_CONTEXT_INFO>

${BikaToolGuidelines}

${BikaResponseGuidelines}

${BikaInteractionStyle}

`;

export const getAINodeSystemPrompt = (params: {
  spaceId: string;
  parentId: string;
  nodeIds?: string[];
  automationIds?: string[];
  userId?: string;
  task?: string;
}) => {
  const compile = _.template(AINodeSystemPrompt);
  return compile({
    spaceId: params.spaceId,
    parentId: params.parentId,
    nodeIds: params.nodeIds?.length ? params.nodeIds : '',
    userId: params.userId || '',
    task: params.task || '',
    automationIds: params.automationIds?.length ? params.automationIds : '',
    dateAndTime: isInCI() ? '' : new Date().toISOString(),
  });
};
