import type { PresetLanguageAIModelDef, AIImageModelDef } from '@bika/types/ai/bo';
import type { CustomAIProviderIntegration } from '@bika/types/integration/bo';
import { getAppEnv } from 'sharelib/app-env';

const gcpAIKey =
  'ya29.c.c0ASRK0GaMY2GceWrT9JjLR0mJpsjxy4uZDYrHkNvKLdkNNSNcpdbnC7xRzTSbFyG7dzJv1OgG6CRrW2DE7LgnyvPiLHlHT_AjFMFSGtilgZn-r9h11VIq-fkneSJib2aBVs4C5wsWn2XTW7AJsCWVtE6vkKAcBCfFazdXBVcRwM3YR6Dr5PVVbtZ_PF-ZkexcHEQ4oxTy7JxZBEsANaVCL6vV_8vH2TTYd8cH7GytitJ6-zDb69zGE0l_ZyAQiU28wP3qh1E6mdddLPLM1gFR-SIS1FBiAcw2Y-xwUyLz0Mk3rTsC_Ic10qSndhsZvM6CDWhD8uUZkSYNi8eaEBnbv90WQr3HC4wyvHwefMqE4l3BzBev_PxfULaaZXPkj1cM6dPrjSSTuAsZT6HK2ZF76DsQmTeyHzBe6LiaaAP247FiNJFGZK8wvESVPlMeoJV5WNlqZ6P2ivViEGGlD0Faa1A9E-WuAc6F7nJXV4Al5GDdrHSXqhVtRq2cU_V4LQG_gDXvLZ5MT6tmyawwSbguZfn-nzR1VCprriB3906JlSrOOZ8nPaRe8yRDI_NVU7l9D8VKvC2Rimgn0q78x0eBx-dG3cIglqijaSAgswXthdZJ3Y865WYJbrtwtaElgWWBH-UwzLZ_p2syjxzp9XxDd1Dkf8f5L677CtOrxx6-m2_c4h4tIuVq3k_fFJr2W5umSp1jgn9tqqBZ8upRRxQfzihohIalnyjx75f-ZmIgjce3tOh_W0wMFmU67rkxbtuFzg8VtqM7pvvtBW-_gxYOufv5yjkIzQ9jOjo0_hS5tMlfvtJ78yBzcQryIerZtowsi9zYgxukutnwklqt39SRsVXhj-7jb3wus6ncuOM4w3os1Jdy728WW03JSwS5aIfOMzOfFZVnZ7UpY-kfO9uZzawRsV05BuvJofp7_-xZ2oW7pf18U-Qr6XsS-i4ojJM2iMBarWli-0--JxYQ-ymJl7wUWJ6f9VtwYbsSeMSYIrmBlJcWO4fuYde';

export function getDefaultAIModel(): PresetLanguageAIModelDef {
  if (getAppEnv() === 'LOCAL') {
    // return 'qwen-plus'; // 'doubao'; // lite //
    return 'qwen/qwen-turbo'; // 'doubao'; // lite //
    //   // return 'siliconflow/DeepSeek-R1-Distill-Qwen-7B';
    //   // return 'siliconflow/DeepSeek-R1';
  }
  return 'qwen/qwen-plus';
}

export type IAILanguageModelConfig = CustomAIProviderIntegration & {
  modelId: string;
  fallbacks?: IAILanguageModelConfig[];
};

export type IAIImageModelConfig = CustomAIProviderIntegration & {
  modelId: string;
  fallbacks?: IAIImageModelConfig[];
};

// export type IAIModelConfig = {
//   baseUrl: string;
//   modelId: string;
//   apiKey: string;
//   provider?: string;
//   secretAccessKey?: string;
// };
/** IAILanguageModelConfig
 * 配置AI Model和服务器的关系
 *
 * AI Model 定价，放在ai-models-price.xlsx
 */
export const PresetLanguageModelServerConfig: Record<PresetLanguageAIModelDef, IAILanguageModelConfig> = {
  // llama3: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'llama3',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // phi3: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'phi3',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // gemma: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'gemma',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // mistral: {
  //   baseUrl: 'https://ollama.bika.hk/v1',
  //   modelId: 'mistral',
  //   apiKey: 'NO_OPENAI_API_KEY_NEEDED',
  // },
  // 'gpt-3.5': {
  //   baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
  //   modelId: 'gpt-3.5-turbo-0125',
  //   type: 'OPENAI',
  //   apiKey: process.env.OPENAI_API_KEY!,
  // },
  // 'gpt-4o-mini': {
  //   baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
  //   modelId: 'gpt-4o-mini-2024-07-18',
  //   type: 'OPENAI',
  //   apiKey: process.env.OPENAI_API_KEY!,
  // },
  // 'gpt-4.1': ,
  // 'gpt-4.1-mini': ,
  // 'gpt-image-1': {
  //   baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
  //   modelId: 'gpt-image-1',
  //   type: 'OPENAI',
  //   apiKey: process.env.OPENAI_API_KEY!,
  // },
  'openai/gpt-4o': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4o',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    fallbacks: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4o-2024-08-06',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/gpt-4o-mini': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4o-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    fallbacks: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/gpt-4.1': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4.1',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    fallbacks: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/gpt-4.1-mini': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-4.1-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    fallbacks: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/o4-mini': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'o4-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    fallbacks: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/o3': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'o3-mini',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    fallbacks: [
      {
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-4.1-mini-2025-04-14',
        type: 'OPENAI',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  // 'bytedance/doubao': {
  //   type: 'OPENAI',
  //   baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
  //   modelId: 'doubao-1-5-lite-32k-250115',
  //   apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  // },
  'bytedance/doubao-pro-32k': {
    type: 'OPENAI',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'doubao-1-5-pro-32k-250115', // 32k
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'bytedance/doubao-pro-256k': {
    type: 'OPENAI',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'doubao-1-5-pro-256k-250115',
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'deepseek/deepseek-r1': {
    type: 'OPENAI',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'deepseek-r1-250528',
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'deepseek/deepseek-v3': {
    type: 'OPENAI',
    baseUrl: process.env.ARK_BASE_URL! || 'https://ark.cn-beijing.volces.com/api/v3',
    modelId: 'deepseek-v3-250324',
    apiKey: '38d97c8a-2535-491c-b4df-b50f519f3bc3',
  },
  'qwen/qwen3-coder': {
    type: 'OPENAI',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'qwen3-coder-plus', // 'qwen3
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
  'qwen/qwen-plus': {
    type: 'OPENAI',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'qwen-plus-latest', // 'qwen-plus-2025-04-28', // 'qwen3-235b-a22b',
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
  'qwen/qwen-turbo': {
    type: 'OPENAI',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'qwen-turbo-latest', // 'qwen-turbo-2025-04-28', // 'qwen3-235b-a22b',
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
  // 'siliconflow/DeepSeek-V3': {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.siliconflow.cn/v1',
  //   modelId: 'deepseek-ai/DeepSeek-V3',
  //   apiKey: 'sk-kkdxtpugigfiyuddlqvobfujegssmcwypwjifijdbjtbytmm',
  // },
  // 'siliconflow/DeepSeek-R1': {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.siliconflow.cn/v1',
  //   modelId: 'deepseek-ai/DeepSeek-R1',
  //   apiKey: 'sk-kkdxtpugigfiyuddlqvobfujegssmcwypwjifijdbjtbytmm',
  // },
  // 'siliconflow/DeepSeek-R1-Distill-Qwen-7B': {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.siliconflow.cn/v1',
  //   modelId: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
  //   apiKey: 'sk-kkdxtpugigfiyuddlqvobfujegssmcwypwjifijdbjtbytmm',
  // },
  'google/gemini-2.5-pro': {
    type: 'OPENAI',
    baseUrl:
      'https://us-central1-aiplatform.googleapis.com/v1beta1/projects/apitable-378509/locations/us-central1/endpoints/openapi',
    modelId: 'google/gemini-1.5-pro-001',
    apiKey: gcpAIKey,
  },
  'google/gemini-2.5-flash': {
    type: 'OPENAI',
    baseUrl:
      'https://us-central1-aiplatform.googleapis.com/v1beta1/projects/apitable-378509/locations/us-central1/endpoints/openapi',
    modelId: 'google/gemini-1.5-flash-001',
    apiKey: gcpAIKey,
  },
  // gptproto: {
  //   type: 'OPENAI',
  //   baseUrl: 'https://api.gptproto.com/v1',
  //   apiKey: 'sk-d09d0b64ed074f1aa3986d3660d54fe7',
  //   modelId: 'gpt-4o',
  // },
  // 'anthropic/claude-sonnet': {
  //   type: 'AMAZON_BEDROCK',
  //   baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
  //   modelId: 'us.anthropic.claude-3-sonnet-20240229-v1:0',
  //   apiKey: process.env.AWS_ACCESS_KEY_ID!,
  //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  // },
  'anthropic/claude-sonnet-3.7': {
    type: 'AMAZON_BEDROCK',
    baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
    modelId: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
    apiKey: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
  'anthropic/claude-opus-4': {
    type: 'AMAZON_BEDROCK',
    baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
    modelId: 'us.anthropic.claude-opus-4-20250514-v1:0',
    apiKey: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
  'anthropic/claude-sonnet-4': {
    type: 'AMAZON_BEDROCK',
    baseUrl: process.env.AWS_BEDROCK_BASE_URL || 'https://bedrock.bika.ltd',
    modelId: 'us.anthropic.claude-sonnet-4-20250514-v1:0',
    apiKey: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
  mock: {
    type: 'MOCK',
    modelId: 'mock',
    // baseUrl: '',
    // modelId: '',
    // apiKey: '',
    // provider: undefined,
    // secretAccessKey: undefined,
  },
  'bytedance/doubao-lite-128k': {
    type: 'MOCK',
    modelId: 'mock',
  },
  'qwen/qwen-pro': {
    type: 'MOCK',
    modelId: 'mock',
  },
  'anthropic/claude-haiku-3.5': {
    type: 'MOCK',
    modelId: 'mock',
  },
};

export const PresetImageModelsServerConfig: Record<AIImageModelDef, IAIImageModelConfig> = {
  'openai/gpt-image-1': {
    baseUrl: process.env.AZURE_BASE_URL!,
    modelId: 'gpt-image-1',
    type: 'AZURE_AI',
    apiKey: process.env.AZURE_API_KEY!,
    fallbacks: [
      {
        type: 'OPENAI',
        baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
        modelId: 'gpt-image-1',
        apiKey: process.env.OPENAI_API_KEY!,
      },
    ],
  },
  'openai/dall-e-3': {
    type: 'OPENAI',
    baseUrl: process.env.OPENAI_BASE_URL! || 'https://openai.bika.ltd/v1',
    modelId: 'dall-e-3',
    apiKey: process.env.OPENAI_API_KEY!,
  },
  'flux/flux-kontext-pro': {
    type: 'OPENAI',
    baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    modelId: 'flux-kontext-pro',
    apiKey: 'sk-193e96c2115a410ba45674c5c5b3af59',
  },
};
