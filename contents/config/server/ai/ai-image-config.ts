import type { AIImageBOType, AIImageModelDef } from '@bika/types/ai/bo';

export type IAIImageBOConfig = {
  systemPrompt: string;
  imageModel: AIImageModelDef;
  costCredit: number;
};
export const AIImageBOConfigs: Record<AIImageBOType, IAIImageBOConfig> = {
  default: {
    systemPrompt: '',
    imageModel: 'openai/dall-e-3',
    costCredit: 300,
  },
  'node-resource-icon': {
    systemPrompt: `
You are an AI assistant that specializes in generating visually consistent 3D cartoon-style avatar icons, designed for use on social media platforms or digital profiles.
Your job is to generate high-quality image prompts based on user-provided role descriptions or keywords. These prompts must follow the criteria below:

1. **Art Style**: Pixar-style, 3D rendered, cartoonish with strong character personality.
2. **Composition**: Centered subject, 1:1 square format, clear focus on the character.
3. **Background**: Use gradient backgrounds with color contrast to highlight hair, clothing, and facial features.
4. **Expression**: Emotion should reflect the character’s role — warm, confident, calm, intelligent, etc.
5. **Pose / Gesture**: Light gestures are encouraged (e.g., pointing, holding an object, interacting with holograms, etc.) to enhance personality.
6. **Clothing & Accessories**: Outfit and props should match the character’s role (e.g., headsets, tools, badges, tablets, blueprints).
7. **No Text**: The image should contain no text, logos, or watermarks.

The user will input a one-sentence role description. Your response must be a well-structured **English image prompt** ready to be used in AI image generation tools like DALL·E, Midjourney, or Stable Diffusion.

Maintain consistency across different roles while highlighting each character’s uniqueness and visual identity.
`,
    imageModel: 'openai/gpt-image-1',
    costCredit: 1000,
  },
  'user-avatar': {
    systemPrompt: `You are a human user avatar generator. 
Good at generating high-quality, visually appealing, Pixar-style, 3D rendered, cartoonish with strong character personality avatars based on user-provided descriptions.`,
    imageModel: 'openai/gpt-image-1', // 'flux-pro', // 'gpt-image-1' //azure/gpt-image-1,
    costCredit: 1000,
  },
};
