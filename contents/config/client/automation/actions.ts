import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { Action, ActionType } from '@bika/types/automation/bo';
import type { IntegrationType } from '@bika/types/integration/bo';
import type { IFeatureAbilityConfig } from '@bika/types/website/bo';

export function getActionTypesConfig(locale: ILocaleContext): Record<
  ActionType,
  IFeatureAbilityConfig & {
    // integrations，这个会给架构图，显示关联的集成
    integrations?: IntegrationType[];
    defaultValue: Action;
  }
> {
  const { t } = locale;
  return {
    FIND_MEMBERS: {
      display: 'SHOW',
      label: t.automation.action.find_members.name,
      iconPath: '/assets/icons/automation/find_member.png',
      defaultValue: {
        actionType: 'FIND_MEMBERS',
        input: {
          type: 'MEMBER',
          by: [],
        },
      },
      description: t.automation.action.find_members.description ?? '',
    },
    FIND_RECORDS: {
      display: 'SHOW',
      label: t.automation.action.find_records.name,
      iconPath: '/assets/icons/automation/find_record.png',
      defaultValue: {
        actionType: 'FIND_RECORDS',
        input: {
          type: 'DATABASE_WITH_FILTER',
          databaseId: '',
        },
      },
      description: t.automation.action.find_records.description ?? '',
    },
    CREATE_RECORD: {
      display: 'SHOW',
      label: t.automation.action.create_record.name,
      iconPath: '/assets/icons/automation/new_record.png',
      defaultValue: {
        actionType: 'CREATE_RECORD',
        input: {
          type: 'RECORD_BODY',
          databaseId: '',
          data: {},
        },
      },
      description: t.automation.action.create_record.description ?? '',
    },
    UPDATE_RECORD: {
      display: 'SHOW',
      label: t.automation.action.update_record.name,
      iconPath: '/assets/icons/automation/update_record.png',
      defaultValue: {
        actionType: 'UPDATE_RECORD',
        input: {
          type: 'SPECIFY_RECORD_BODY',
          databaseId: '',
          recordId: '',
          data: {},
        },
      },
      description: t.automation.action.update_record.description ?? '',
    },
    FIND_DASHBOARD: {
      display: 'SHOW',
      label: t.automation.action.find_dashboard.name,
      iconPath: '/assets/icons/automation/find_dashboard.png',
      defaultValue: {
        actionType: 'FIND_DASHBOARD',
        input: {
          type: 'DASHBOARD',
          dashboardId: '',
        },
      },
      description: t.automation.action.find_dashboard.description ?? '',
    },
    FIND_WIDGET: {
      display: 'SHOW',
      label: t.automation.action.find_widget.name,
      iconPath: '/assets/icons/automation/find_widget.png',
      defaultValue: {
        actionType: 'FIND_WIDGET',
        input: {
          type: 'WIDGET',
        },
      },
      description: t.automation.action.find_widget.description ?? '',
    },
    FIND_MISSIONS: {
      display: 'COMING_SOON',
      label: t.automation.action.find_missions.name,
      iconPath: '/assets/icons/automation/find_mission.png',
      defaultValue: {
        actionType: 'FIND_MISSIONS',
        input: {
          type: 'MISSION',
          missionType: 'QUEST',
        },
      },
      description: t.automation.action.find_missions.description ?? '',
    },
    CREATE_MISSION: {
      display: 'SHOW',
      label: t.automation.action.create_mission.name,
      iconPath: '/assets/icons/automation/create_mission.png',
      defaultValue: {
        actionType: 'CREATE_MISSION',
        input: {
          type: 'MISSION_BODY',
          mission: {
            name: '',
            type: 'REMINDER',
            to: [],
            dueDate: {},
          },
        },
      },
      description: t.automation.action.create_mission.description ?? '',
    },
    RUN_SCRIPT: {
      display: 'SHOW',
      label: t.automation.action.run_script.name,
      iconPath: '/assets/icons/automation/script.png',
      defaultValue: {
        actionType: 'RUN_SCRIPT',
        input: {
          type: 'SCRIPT',
          language: 'python',
          script: `def main():
  data = {
    "text": "Hello, world!",
    "code": 200,
    "status": True
  }
  return data;

result = main();`,
        },
      },
      description: t.automation.action.run_script.description ?? '',
    },
    FORMAPP_AI: {
      display: 'HIDDEN',
      label: t.automation.action.formapp_ai.name,
      iconPath: '/assets/icons/automation/formapp.png',
      defaultValue: {
        actionType: 'FORMAPP_AI',
        input: {
          type: 'FORMAPP_AI',
          inputData: {},
        },
      },
      description: t.automation.action.formapp_ai.description ?? '',
    },
    TOOLSDK_AI: {
      display: 'SHOW',
      label: t.automation.action.toolsdk_ai.name,
      iconPath: '/assets/icons/automation/toolsdk_logo.png',
      defaultValue: {
        actionType: 'TOOLSDK_AI',
        input: {
          type: 'TOOLSDK_AI',
          inputData: {},
        },
      },
      description: t.automation.action.toolsdk_ai.description ?? '',
    },
    WEBHOOK: {
      display: 'SHOW',
      label: t.automation.action.webhook.name,
      iconPath: '/assets/icons/automation/webhook.png',
      defaultValue: {
        actionType: 'WEBHOOK',
        input: {
          type: 'WEBHOOK',
          method: 'GET',
          url: 'https://bika.ai/api/meta',
          headers: [],
          timeout: 120,
        },
      },
      description: t.automation.action.webhook.description ?? '',
    },
    WECOM_WEBHOOK: {
      display: 'SHOW',
      label: t.automation.action.wecom_webhook.name,
      iconPath: '/assets/icons/automation/we_com.png',
      integrations: ['WE_COM'],
      defaultValue: {
        actionType: 'WECOM_WEBHOOK',
        input: {
          type: 'WECOM_WEBHOOK',
          urlType: 'URL',
          url: '',
          data: {
            msgtype: 'markdown',
            markdown: {
              content:
                '## BIKA Scheduled Reminder\n\n' +
                'Hello! This is a <font color="warning">Markdown</font> type message from Bika.\n\n\n\n' +
                '👇 For more markdown syntax, please click\n\n' +
                '[View Configuration Tutorial](https://developer.work.weixin.qq.com/document/path/99110#markdown%E7%B1%BB%E5%9E%8B)',
            },
          },
        },
      },
      description: t.automation.action.wecom_webhook.description ?? '',
    },
    FEISHU_WEBHOOK: {
      display: 'SHOW',
      integrations: ['FEI_SHU'],
      label: t.automation.action.feishu_webhook.name,
      iconPath: '/assets/icons/automation/feishu.png',
      defaultValue: {
        actionType: 'FEISHU_WEBHOOK',
        input: {
          type: 'FEISHU_WEBHOOK',
          urlType: 'URL',
          url: '',
          data: {
            msg_type: 'text',
            content: {
              text: 'Bika Scheduled Reminder\nHello! This is a Text message from Bika.',
            },
          },
        },
      },
      description: t.automation.action.feishu_webhook.description ?? '',
    },
    DINGTALK_WEBHOOK: {
      display: 'SHOW',
      integrations: ['DING_TALK'],
      label: t.automation.action.dingtalk_webhook.name,
      iconPath: '/assets/icons/automation/ding_talk.png',
      defaultValue: {
        actionType: 'DINGTALK_WEBHOOK',
        input: {
          type: 'DINGTALK_WEBHOOK',
          urlType: 'URL',
          url: '',
          data: {
            msgtype: 'markdown',
            markdown: {
              title: 'This is a Markdown title from Bika',
              text:
                'Hello! This is a Markdown message from Bika.\n\n' +
                '![demo](https://bika.ai/_next/image?url=%2Fassets%2Fblog%2Fwhat-is-bika-ai%2Fblog-cover.zh-CN.png&w=3840&q=75)\n\n' +
                '👇 For more markdown syntax, please click\n\n' +
                '[View Configuration Tutorial](https://open.dingtalk.com/document/isvapp/message-type)',
            },
          },
        },
      },
      description: t.automation.action.dingtalk_webhook.description ?? '',
    },
    SLACK_WEBHOOK: {
      display: 'SHOW',
      label: t.automation.action.slack_webhook.name,
      integrations: ['SLACK'],
      iconPath: '/assets/icons/automation/slack.png',
      defaultValue: {
        actionType: 'SLACK_WEBHOOK',
        input: {
          type: 'SLACK_WEBHOOK',
          urlType: 'URL',
          url: '',
          data: {
            msgtype: 'blocks',
            blocks: [
              {
                type: 'header',
                text: {
                  type: 'plain_text',
                  text: 'Hi there! 👋',
                  emoji: true,
                },
              },
              {
                type: 'section',
                text: {
                  type: 'mrkdwn',
                  text:
                    'This is a scheduled reminder message from Bika. You can customize the message content and format.\n' +
                    '*Date:* <%= new Date().toLocaleDateString() %>\n' +
                    '*Sent from:* <https://bika.ai|Bika.ai>',
                },
                accessory: {
                  type: 'image',
                  image_url: 'https://api.slack.com/img/blocks/bkb_template_images/notifications.png',
                  alt_text: 'calendar thumbnail',
                },
              },
              {
                type: 'divider',
              },
              {
                type: 'section',
                text: {
                  type: 'mrkdwn',
                  text: '👇 Check out the tutorial on how to send messages to Slack channel',
                },
              },
              {
                type: 'actions',
                elements: [
                  {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: '📚 Create webhooks',
                      emoji: true,
                    },
                    style: 'danger',
                    url: 'https://api.slack.com/messaging/webhooks#advanced_message_formatting',
                  },
                  {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: '⭐ Formatting Tutorial',
                      emoji: true,
                    },
                    url: 'https://api.slack.com/reference/surfaces/formatting',
                  },
                  {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: '💡Rich Text Tutorial',
                      emoji: true,
                    },
                    url: 'https://api.slack.com/tutorials/tracks/rich-text-tutorial',
                  },
                  {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: '🌏 Bika.ai website',
                      emoji: true,
                    },
                    url: 'https://bika.ai',
                    style: 'primary',
                  },
                ],
              },
            ],
          },
        },
      },
      description: t.automation.action.slack_webhook.description ?? '',
    },
    TELEGRAM_SEND_MESSAGE: {
      display: 'SHOW',
      integrations: ['TELEGRAM'],
      label: t.automation.action.telegram_send_message.name,
      iconPath: '/assets/icons/automation/telegram.png',
      defaultValue: {
        actionType: 'TELEGRAM_SEND_MESSAGE',
        input: {
          urlType: 'URL',
          type: 'TELEGRAM_SEND_MESSAGE',
          token: '',
          chatId: '',
          parseMode: 'Plain',
          text: 'Hello, Bika!',
        },
      },
      description: t.automation.action.telegram_send_message.description ?? '',
    },
    AI_SUMMARY: {
      display: 'COMING_SOON',
      integrations: ['OPENAI'],
      label: t.automation.action.ai_summary.name,
      iconPath: '/assets/icons/automation/ai_summary.png',
      defaultValue: {
        actionType: 'AI_SUMMARY',
        input: {
          type: 'AI_SUMMARY',
          prompt: '',
        },
      },
      description: t.automation.action.ai_summary.description ?? '',
    },
    CALL_AGENT: {
      display: 'SHOW',
      label: t.automation.action.call_agent.name,
      iconPath: '/assets/icons/automation/script.png',
      defaultValue: {
        actionType: 'CALL_AGENT',
        input: {
          type: 'CALL_AGENT',
          agentId: '',
          message: '',
        },
      },
      description: t.automation.action.call_agent.description ?? '',
    },
    X_CREATE_TWEET: {
      display: 'SHOW',
      integrations: ['TWITTER'],
      label: t.automation.action.x_create_tweet.name,
      iconPath: '/assets/icons/automation/x.png',
      defaultValue: {
        actionType: 'X_CREATE_TWEET',
        input: {
          urlType: 'INTEGRATION',
          type: 'X_CREATE_TWEET',
          authMethod: 'OAUTH2',
          integrationId: '',
          data: {
            text: 'Hello, Bika!',
          },
        },
      },
      description: t.automation.action.x_create_tweet.description ?? '',
    },
    TWITTER_UPLOAD_MEDIA: {
      display: 'SHOW',
      integrations: ['TWITTER_OAUTH_1A'],
      label: t.automation.action.twitter_upload_media.name,
      iconPath: '/assets/icons/automation/x.png',
      defaultValue: {
        actionType: 'TWITTER_UPLOAD_MEDIA',
        input: {
          urlType: 'INTEGRATION',
          type: 'TWITTER_UPLOAD_MEDIA',
          integrationId: '',
          data: {
            mediaUrls: '',
          },
        },
      },
      description: t.automation.action.twitter_upload_media.description ?? '',
    },
    DUMMY_ACTION: {
      display: 'HIDDEN',
      label: t.automation.action.dummy_action.name,
      iconPath: '/assets/icons/automation/bika.png',
      defaultValue: {
        actionType: 'DUMMY_ACTION',
      },
      description: t.automation.action.dummy_action.description ?? '',
    },
    CONDITION: {
      display: 'COMING_SOON',
      label: t.automation.action.condition.name,
      iconPath: '/assets/icons/automation/match_condition.png',
      defaultValue: {
        actionType: 'CONDITION',
        input: {
          type: 'PREV_ACTION',
        },
      },
      description: t.automation.action.condition.description ?? '',
    },
    DELAY: {
      display: 'SHOW',
      label: t.automation.action.delay.name,
      iconPath: '/assets/icons/automation/delay.png',
      defaultValue: {
        actionType: 'DELAY',
        input: {
          type: 'DELAY',
          unit: 'DAY',
          value: 1,
        },
      },
      description: t.automation.action.delay.description ?? '',
    },
    LOOP: {
      display: 'SHOW',
      label: t.automation.action.loop.name,
      iconPath: '/assets/icons/automation/loop.png',
      defaultValue: {
        actionType: 'LOOP',
        input: {
          type: 'LOOP',
          ordered: true,
          interruptIfItemError: false,
        },
      },
      description: t.automation.action.loop.description ?? '',
    },
    RANDOM: {
      display: 'COMING_SOON',
      label: t.automation.action.random.name,
      iconPath: '/assets/icons/automation/random.png',
      defaultValue: {
        actionType: 'RANDOM',
        input: {
          type: 'PREV_ACTION',
        },
      },
      description: t.automation.action.random.description ?? '',
    },
    ROUND_ROBIN: {
      display: 'SHOW',
      label: t.automation.action.round_robin.name,
      iconPath: '/assets/icons/automation/round_robin.png',
      defaultValue: {
        actionType: 'ROUND_ROBIN',
        input: {
          type: 'PREV_ACTION',
        },
      },
      description: t.automation.action.round_robin.description ?? '',
    },
    FILTER: {
      display: 'SHOW',
      label: t.automation.action.filter.name,
      iconPath: '/assets/icons/automation/filter.png',
      defaultValue: {
        actionType: 'FILTER',
        input: {
          type: 'FILTER',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
        },
      },
      description: t.automation.action.filter.description ?? '',
    },
    SEND_REPORT: {
      display: 'SHOW',
      label: t.automation.action.send_report.name,
      iconPath: '/assets/icons/automation/report.png',
      defaultValue: {
        actionType: 'SEND_REPORT',
        input: {
          type: 'MARKDOWN',
          to: [
            {
              type: 'ADMIN',
            },
          ],
          subject: 'Your Report subject',
          markdown: 'Your markdown content',
        },
      },
      description: t.automation.action.send_report.description ?? '',
    },
    SEND_EMAIL: {
      display: 'SHOW',
      label: t.automation.action.send_email.name,
      iconPath: '/assets/icons/automation/email.png',
      defaultValue: {
        actionType: 'SEND_EMAIL',
        input: {
          type: 'SERVICE',
          subject: 'Your Email subject',
          body: 'Your email body',
          to: [{ type: 'EMAIL_STRING', email: '<EMAIL>' }],
          cc: [],
          bcc: [],
          replyTo: [],
          senderName: '',
        },
      },
      description: t.automation.action.send_email.description ?? '',
    },
    OPENAI_GENERATE_TEXT: {
      display: 'SHOW',
      label: t.automation.action.openai_generate_text.name,
      iconPath: '/assets/icons/automation/openai.png',
      integrations: ['OPENAI'],
      defaultValue: {
        actionType: 'OPENAI_GENERATE_TEXT',
        input: {
          urlType: 'INTEGRATION',
          type: 'OPENAI_GENERATE_TEXT',
          integrationId: '',
          prompt: 'What is AI?',
          model: 'gpt-4o-mini',
          timeout: 300,
        },
      },
      description: t.automation.action.openai_generate_text.description ?? '',
    },
    AI_MODEL: {
      display: 'SHOW',
      label: 'AI',
      iconPath: '/assets/icons/automation/openai.png',
      defaultValue: {
        actionType: 'AI_MODEL',
        input: {
          type: 'AI_MODEL',
          model: { kind: 'auto' },
        },
      },
      description: t.automation.action.find_members.description ?? '',
    },
    DEEPSEEK: {
      display: 'SHOW',
      label: t.automation.action.deepseek_generate_text.name,
      iconPath: '/assets/icons/automation/deepseek.png',
      integrations: ['DEEPSEEK'],
      defaultValue: {
        actionType: 'DEEPSEEK',
        input: {
          urlType: 'INTEGRATION',
          type: 'OPENAI_GENERATE_TEXT',
          integrationId: '',
          prompt: 'What is AI?',
          model: 'deepseek-chat',
          timeout: 300,
        },
      },
      description: t.automation.action.deepseek_generate_text.description ?? '',
    },
    CREATE_DOCUMENT: {
      display: 'COMING_SOON',
      label: t.automation.action.create_document.name,
      iconPath: '/assets/icons/automation/create_document.png',
      defaultValue: {
        actionType: 'CREATE_DOCUMENT',
        input: {
          type: 'CREATE_DOCUMENT',
          createTo: {
            createToType: 'NODE_RESOURCE',
            parentFolderId: 'storybook',
          },
          documentCreateDTO: {
            name: 'Test document',
            resourceType: 'DOCUMENT',
            markdown: 'test markdown',
          },
        },
      },
      description: t.automation.action.create_document.description ?? '',
    },
    CREATE_NODE_RESOURCE: {
      display: 'COMING_SOON',
      label: t.automation.action.create_node_resource.name,
      iconPath: '/assets/icons/automation/create_node_resource.png',
      defaultValue: {
        actionType: 'CREATE_NODE_RESOURCE',
        input: {
          type: 'CREATE_NODE_RESOURCE',
        },
      },
      description: t.automation.action.create_node_resource.description ?? '',
    },
    REPLACE_FILE: {
      display: 'COMING_SOON',
      label: t.automation.action.replace_file.name,
      iconPath: '/assets/icons/automation/replace_file.png',
      defaultValue: {
        actionType: 'REPLACE_FILE',
        input: {
          type: 'REPLACE_FILE',
          attachmentId: undefined,
          variables: {},
        },
      },
      description: t.automation.action.replace_file.description ?? '',
    },
  };
}
