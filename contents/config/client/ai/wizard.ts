import { type AppEnv } from 'sharelib/app-env';
import type { AIIntentType } from '@bika/types/ai/bo';
import type { AIChatOption } from '@bika/types/ai/vo';
import type { iString } from '@bika/types/i18n/bo';
import { searchPrompts } from './search';
import { type ILocaleContext } from '../../../i18n/context';

export function getAIIntentTypesConfig(
  locale: ILocaleContext,
  _appEnv?: AppEnv,
): Record<
  AIIntentType,
  {
    display: 'SHOW' | 'HIDDEN' | 'COMING_SOON';
    label: string;
    description: string;
    // 这个会显示帮助文档等的额外链接
    links?: string[];
    prompts: iString[];
    // integrations，这个会给架构图，显示关联的集成
    // integrations?: IntegrationType[];
    iconPath: string;
    screenshots?: { url: string }[];
    options?: AIChatOption[];
  }
> {
  // AIIntentTypes
  return {
    IMPORT_VIKA_DATASOURCE: {
      display: 'HIDDEN',
      prompts: [],
      label: 'IMPORT_VIKA_DATASOURCE',
      description: '',
      iconPath: '',
      screenshots: [{ url: '' }],
    },
    ADMIN: {
      display: 'HIDDEN',
      prompts: [],
      label: 'Admin',
      description: 'Debug AI via Admin UI',
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/template.png' }],
      options: [
        {
          value: 'reasoning-chat',
          label: 'Reasoning Chat',
        },
        {
          value: 'consultant',
          label: 'Consultant(商务顾问)',
        },
        {
          value: 'business-analyst',
          label: 'Business Analyst(商业分析师)',
        },
        {
          value: 'solution-designer',
          label: 'Product Designer(产品策划师)',
        },
        {
          value: 'database-engineer',
          label: 'Database Engineer(数据表工程师)',
        },
        {
          value: 'solution-designer-reverse',
          label: 'Solution Designer Reverse(逆向产品策划师)',
          tips: '输入 Template 的 JSON Schema，反过来输出产品策划案',
        },
        {
          value: 'automation-engineer',
          label: 'Automation Engineer(自动化工程师)',
        },
        {
          value: 'node-resource-engineer',
          label: 'Node Resource Engineer(节点资源工程师)',
        },
        {
          value: 'i18n',
          label: 'iString翻译家',
          tips: '请输入一个 iString，比如 "你好"，就会变成 {"zh-CN": "你好"}',
        },
        {
          value: 'record-extractor',
          label: 'Database Record Extractor(记录提取器)',
        },
        {
          value: 'error',
          label: '故意让它错误',
        },
      ],
    },
    BUILDER: {
      display: 'HIDDEN',
      prompts: searchPrompts,
      label: 'AI Apps Builder',
      description: 'Let AI help you build your own AI automation apps and templates',
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/template.png' }],
      options: [
        {
          value: 'quick',
          label: locale.t.ai_consultant.quick,
          tips: 'In this mode, it will quickly generate a workflow based on your description.',
        },
        {
          value: 'deep',
          label: locale.t.ai_consultant.deep_think,
          tips: 'In this mode, it will deeply think about your description and generate a workflow, it would take more times and more credits',
          disabled: true,
        },
      ],
    },
    SUPERVISOR: {
      display: 'HIDDEN',
      prompts: [],
      label: 'Space Super Agent',
      description: 'Super Agent for your space, it can help you to do everything',
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/template.png' }],
      options: [],
    },
    COPILOT: {
      display: 'HIDDEN',
      prompts: [],
      label: 'AI Apps Builder',
      description: 'Let AI help you build your own AI automation apps and templates',
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/template.png' }],
    },
    AI_NODE: {
      display: 'HIDDEN',
      prompts: [],
      label: 'AI Apps Builder',
      description: 'Let AI help you build your own AI automation apps and templates',
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/template.png' }],
      options: [
        {
          label: 'Ask',
          value: 'ask',
        },
        {
          label: 'Edit',
          value: 'edit',
          disabled: true,
        },
      ],
    },
    PAGE: {
      display: 'SHOW',
      prompts: [],
      label: 'AI Apps Builder',
      description: 'Let AI help you build your own AI automation apps and templates',
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/template.png' }],
    },
    CHAT: {
      display: 'COMING_SOON',
      prompts: [],
      label: locale.t.resource.type.chat,
      description: locale.t.resource.type.chat_description,
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/chat.png' }],
    },
    SEARCH: {
      display: 'COMING_SOON',
      prompts: [],
      label: 'AI Search',
      description: 'AI Search',
      iconPath: '/assets/icons/automation/bika.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/search.png' }],
    },
    CREATE_RECORD: {
      display: 'COMING_SOON',
      prompts: [],
      label: locale.t.resource.type.create_record,
      description: locale.t.resource.type.create_record_description,
      iconPath: '/assets/icons/ai-wizard/create-record.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/create-record.png' }],
    },
    CREATE_REMINDER: {
      display: 'COMING_SOON',
      label: locale.t.resource.type.create_reminder,
      prompts: [],
      description: locale.t.resource.type.create_reminder_description,
      iconPath: '/assets/icons/ai-wizard/create-reminder.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/create-remind.png' }],
    },
    CREATE_NODE_RESOURCE: {
      display: 'COMING_SOON',
      label: locale.t.resource.type.create_node_resource,
      prompts: [],
      description: locale.t.resource.type.create_node_resource_description,
      iconPath: '/assets/icons/ai-wizard/create-node-resource.png',
      screenshots: [{ url: '/assets/screenshot/stories/ai-wizard/create-node-resource.png' }],
    },
    STEP_WIZARD: {
      display: 'HIDDEN',
      prompts: [],
      label: '',
      description: '',
      iconPath: '',
    },
    DEBUGGER: {
      display: 'HIDDEN',
      prompts: [],
      label: '',
      description: '',
      iconPath: '',
      // options: [
      //   {
      //     label: 'Weather',
      //     value: 'weather',
      //   },
      //   {
      //     label: 'Create AI Agent',
      //     value: 'create-ai-agent',
      //   },
      //   {
      //     label: 'Create Database',
      //     value: 'create-database',
      //   },
      //   {
      //     label: 'Create Automation',
      //     value: 'create-automation',
      //   },
      // ],
    },
  };
}
