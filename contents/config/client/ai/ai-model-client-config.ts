import type { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { type iString } from '@bika/types/system';
import { AvatarLogo } from '@bika/types/system';

export type IAIModelClientConfigTag = 'agent' | 'automation' | 'database-ai-field';
export type IAIModelClientConfig = {
  name: iString;
  description: iString;
  logo?: AvatarLogo;

  kind?: 'chat' | 'image';

  display?: 'HIDDEN' | 'DISPLAY' | 'COMING_SOON';

  // 分类识别
  tags?: IAIModelClientConfigTag[];
};

/**
 * 配置AI Model 客户端显示配置，独立前端配置给前端编译打包
 */
export const AILanguageModelClientConfig: Record<PresetLanguageAIModelDef, IAIModelClientConfig> = {
  // deprecated
  // 'openai/gpt-3.5': {
  //   name: 'GPT-3.5 Turbo',
  //   description: "OpenAI's GPT-3.5 Turbo model for general-purpose conversations",
  //   tags: [],
  //   logo: {
  //     type: 'URL',
  //     url: '/assets/ai/model/gpt-3.5.png',
  //   },
  // },
  'openai/gpt-4o-mini': {
    name: 'GPT-4o Mini',
    description: 'Compact version of GPT-4o with faster response times',
    tags: ['automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/gpt-3.5.png',
    },
  },
  'openai/gpt-4o': {
    name: 'GPT-4o',
    description: "OpenAI's most advanced multimodal model",
    tags: ['automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/gpt-3.5.png',
    },
  },
  'openai/gpt-4.1': {
    name: 'GPT-4.1',
    description: 'Latest GPT-4.1 model with enhanced capabilities',
    tags: ['agent', 'automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/gpt-3.5.png',
    },
  },
  'openai/gpt-4.1-mini': {
    name: 'GPT-4.1 Mini',
    description: 'Lightweight version of GPT-4.1 for faster processing',
    tags: ['agent', 'automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/gpt-3.5.png',
    },
  },
  // 'gpt-image-1': {
  //   name: 'GPT Image-1',
  //   description: 'Specialized model for image generation and processing',
  // },
  // 'azure/gpt-4o': {
  //   name: 'Azure GPT-4o',
  //   description: 'GPT-4o model hosted on Azure OpenAI Service',
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  // 'azure/gpt-4o-mini': {
  //   name: 'Azure GPT-4o Mini',
  //   description: 'GPT-4o Mini model hosted on Azure OpenAI Service',
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  // 'azure/gpt-4.1': {
  //   name: 'Azure GPT-4.1',
  //   description: 'GPT-4.1 model hosted on Azure OpenAI Service',
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  // 'azure/gpt-image-1': {
  //   name: 'Azure GPT Image-1',
  //   description: 'Image generation model hosted on Azure OpenAI Service',
  // },
  // 'bytedance/doubao': {
  //   name: 'Doubao Lite',
  //   description: "ByteDance's Doubao 1.5 Lite model with 32k context",
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  'bytedance/doubao-pro-32k': {
    name: 'Doubao Pro 32K',
    description: "ByteDance's Doubao 1.5 Pro model with 32k context window",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'bytedance/doubao-pro-256k': {
    name: 'Doubao Pro 256K',
    description: "ByteDance's Doubao 1.5 Pro model with 256k context window",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'deepseek/deepseek-r1': {
    name: 'DeepSeek R1',
    description: "DeepSeek's reasoning-focused R1 model",
    tags: ['automation', 'database-ai-field'],
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'deepseek/deepseek-v3': {
    name: 'DeepSeek V3',
    description: "DeepSeek's latest V3 model with advanced capabilities",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'qwen/qwen3-coder': {
    name: 'Qwen3 Coder Plus',
    description: "Alibaba's Qwen 3 Coder Plus model for coding tasks",
    tags: ['agent', 'automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/qwen.png',
    },
  },
  'qwen/qwen-plus': {
    name: 'Qwen3 Plus',
    description: "Alibaba's Qwen Plus model for enhanced performance",
    tags: ['agent', 'automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/qwen.png',
    },
  },
  'qwen/qwen-turbo': {
    name: 'Qwen Turbo',
    description: "Alibaba's Qwen Turbo model optimized for speed",
    logo: {
      type: 'URL',
      url: '/assets/ai/model/qwen.png',
    },
  },
  // 'siliconflow/DeepSeek-V3': {
  //   name: 'SiliconFlow DeepSeek V3',
  //   description: 'DeepSeek V3 model via SiliconFlow platform',
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  // 'siliconflow/DeepSeek-R1': {
  //   name: 'SiliconFlow DeepSeek R1',
  //   description: 'DeepSeek R1 reasoning model via SiliconFlow platform',
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  // 'siliconflow/DeepSeek-R1-Distill-Qwen-7B': {
  //   name: 'SiliconFlow DeepSeek R1 Distill',
  //   description: 'Distilled 7B version of DeepSeek R1 via SiliconFlow',
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  'google/gemini-2.5-pro': {
    name: 'Gemini Pro',
    description: "Google's Gemini 1.5 Pro multimodal model",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'google/gemini-2.5-flash': {
    name: 'Gemini Flash',
    description: "Google's Gemini 1.5 Flash model optimized for speed",
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  // gptproto: {
  //   name: 'GPTProto GPT-4o',
  //   description: 'GPT-4o model via GPTProto service',
  //   logo: {
  //     type: 'COLOR',
  //     color: 'red',
  //   },
  // },
  // 'claude-3-sonnet': {
  //   name: 'Claude 3 Sonnet',
  //   description: "Anthropic's Claude 3 Sonnet model via AWS Bedrock",
  //   logo: {
  //     type: 'URL',
  //     url: '/assets/ai/model/claude.png',
  //   },
  // },
  'anthropic/claude-sonnet-3.7': {
    name: 'Claude 3.7 Sonnet',
    description: "Anthropic's Claude 3.7 Sonnet model via AWS Bedrock",
    tags: ['agent', 'automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/claude.png',
    },
  },
  'anthropic/claude-opus-4': {
    name: 'Claude Opus 4',
    description: "Anthropic's most powerful Claude Opus 4 model via AWS Bedrock",
    tags: [],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/claude.png',
    },
  },
  'anthropic/claude-sonnet-4': {
    name: 'Claude Sonnet 4',
    description: "Anthropic's Claude Sonnet 4 model via AWS Bedrock",
    tags: ['agent', 'automation', 'database-ai-field'],
    logo: {
      type: 'URL',
      url: '/assets/ai/model/claude.png',
    },
  },
  mock: {
    name: '',
    description: '',
    kind: undefined,
    display: 'HIDDEN',
    tags: undefined,
    logo: {
      type: 'COLOR',
      color: 'red',
    },
  },
  'openai/o4-mini': {
    name: '',
    description: '',
    logo: undefined,
    kind: undefined,
    display: undefined,
    tags: undefined,
  },
  'openai/o3': {
    name: '',
    description: '',
    logo: undefined,
    kind: undefined,
    display: undefined,
    tags: undefined,
  },
  'bytedance/doubao-lite-128k': {
    name: '',
    description: '',
    logo: undefined,
    kind: undefined,
    display: undefined,
    tags: undefined,
  },
  'qwen/qwen-pro': {
    name: '',
    description: '',
    logo: undefined,
    kind: undefined,
    display: undefined,
    tags: undefined,
  },
  'anthropic/claude-haiku-3.5': {
    name: '',
    description: '',
    logo: undefined,
    kind: undefined,
    display: undefined,
    tags: undefined,
  },
};

export function getAILanguageModelClientConfigs(
  tag: IAIModelClientConfigTag,
): { key: PresetLanguageAIModelDef; config: IAIModelClientConfig }[] {
  return Object.entries(AILanguageModelClientConfig)
    .filter(([, config]) => config.tags?.includes(tag))
    .map(([key, config]) => ({ key: key as PresetLanguageAIModelDef, config }));
}
