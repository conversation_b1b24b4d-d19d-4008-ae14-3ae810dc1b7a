'use client';

import { type Locale } from 'basenext/i18n/config';
import { iString, iStringParse } from 'basenext/i18n/i-string';
import React, { useState, ReactNode, useContext, useEffect } from 'react';
// import { iString, iStringParse, type Locale } from '@bika/types/i18n/bo';
import { matchLocale } from './config';
import {
  getTranslationDataByLocale,
  getTranslations,
  type Dictionary,
  type Translate,
  FormAppDictionary,
} from './translate';

/**
 * 常用多语言接口
 */
export interface ILocaleContext {
  /**
   * 当前语言
   */
  lang: Locale;

  /**
   * t函数，从中央字典里找到对应的翻译，一般为“系统文字”
   */
  t: Translate;

  /**
   * i函数，解析iString字符串，一般用于“配置文字”
   *
   * @param iStr
   * @returns
   */
  i: (_iStr: iString | undefined) => string;
}

export interface LocaleContextProps extends ILocaleContext {
  setLang: (_lang: Locale) => void;

  // 获取原始的翻译字典
  dictionary: Dictionary;
}

export const LocaleContext = React.createContext<LocaleContextProps | null>(null);

interface Props {
  defaultLocale: Locale;
  // 通常从服务端(server component)透传
  defaultDictionary: Dictionary;
  extensions?: string[];
}

export function LocaleProvider(
  props: Props & {
    children: ReactNode;
  },
) {
  const { children, defaultLocale, defaultDictionary, extensions } = props;
  const [lang, _setLang] = useState<Locale>(defaultLocale);
  const [dictionary, setDit] = useState<Dictionary>(defaultDictionary);
  const [t, setT] = useState<Translate>(() => getTranslations(defaultDictionary));

  function setLang(val: Locale) {
    // 当切换语言时
    // 这是客户端动态加载，
    // 初始化函数的defaultDictionary是从服务端传递的初始化加载，以支持服务端渲染
    const loadDictionary = async () => {
      const { translate, dictionary: _dictionary } = await getTranslationDataByLocale(matchLocale([val]), extensions);
      setDit(() => _dictionary);
      // const clerkL = await utils.getClerkLocale(val);
      setT(() => translate);
      // setClerkLocale(clerkL);
      const html = document.querySelector('html');
      if (html) html.setAttribute('lang', val);
      _setLang(val);
    };
    loadDictionary();
  }

  useEffect(() => {
    setLang(defaultLocale);
  }, [defaultLocale]);

  const iFunc = (str: iString | undefined) => iStringParse(str, lang);

  const localeContext = {
    dictionary,
    t,
    lang,
    setLang,
    i: iFunc,
  };
  // LocaleContextProps
  return <LocaleContext.Provider value={localeContext}>{children}</LocaleContext.Provider>;
}

export const useLocale = (): LocaleContextProps => useContext(LocaleContext)!;

export const useFormappLocale = (): LocaleContextProps & {
  t: Translate<FormAppDictionary>;
} => {
  const t = useLocale();
  return t as LocaleContextProps & { t: Translate<FormAppDictionary> };
};
