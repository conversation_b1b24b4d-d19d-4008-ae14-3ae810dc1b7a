services:
  redis:
    profiles:
      - oltp
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/redis/redis-stack:7.2.0-v11
    restart: always
    ports:
      - '6379:6379'
      # Redis Insight管理
      - '8001:8001'
    healthcheck:
      test: ['CMD-SHELL', 'redis-cli ping']
      interval: 5s
      timeout: 5s
      retries: 30
    environment:
      # 默认用户名default，密码bikabika
      REDIS_ARGS: --appendonly yes
    networks:
      - bika

  fileview:
    profiles:
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/vikadata/kkfileview:4.1.0
    restart: always
    ports:
      - '8012:8012'
    mem_limit: ${MEM_LIMIT:-2g}
    networks:
      - bika

  postgres:
    profiles:
      - oltp
      - all
    # image: ${IMAGE_REGISTRY:-docker.io}/library/postgres:16
    image: ${IMAGE_REGISTRY:-docker.io}/percona/percona-distribution-postgresql:17.5-2
    restart: always
    environment:
      POSTGRES_USER: bika
      POSTGRES_PASSWORD: bikabika
      POSTGRES_DB: ${POSTGRES_DB:-bikadev}
    ports:
      - '5432:5432'
    volumes:
      - ./.data/postgres:/var/lib/postgresql/data/
    command: -c max_connections=200
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 30
    networks:
      - bika

  mongo:
    profiles:
      - oltp
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/library/mongo:7.0.8
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: bika
      MONGO_INITDB_ROOT_PASSWORD: bikabika
    entrypoint:
      - bash
      - -c
      - |
        if [ ! -f /data/config/keyfile.pem ];then
          mkdir -p /data/config/ && \
          openssl rand -base64 756 > /data/config/keyfile.pem && \
          chmod 400 /data/config/keyfile.pem && \
          chown 999:999 -R /data/config
        fi
        exec docker-entrypoint.sh $$@
    command: mongod --replSet rs0 --keyFile /data/config/keyfile.pem --bind_ip_all --auth
    volumes:
      - ./.data/mongo/config:/data/config
      - ./.data/mongo/db:/data/db
    ports:
      - '27017:27017'
    mem_limit: ${MEM_LIMIT:-2g}
    networks:
      - bika

  mongo-init-replica:
    profiles:
      - oltp
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/library/mongo:7.0.8
    depends_on:
      - mongo
    environment:
      MONGO_INITDB_ROOT_USERNAME: bika
      MONGO_INITDB_ROOT_PASSWORD: bikabika
    command: >
      /bin/bash -c "
        echo 'Waiting for MongoDB to start...';
        while ! mongosh --host mongo --username bika --password bikabika --authenticationDatabase admin --eval 'print(\"waited for connection\")'; do
          sleep 2;
        done;
        echo 'Initializing replica set...';
        mongosh --host mongo --username bika --password bikabika --authenticationDatabase admin --eval '
          rs.initiate({
            _id: \"rs0\",
            members: [{ _id: 0, host: \"localhost:27017\" }]
          });
          rs.status();
        '
      "
    networks:
      - bika

  openobserve:
    profiles:
      - oltp
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/openobserve/openobserve:v0.14.0
    restart: unless-stopped
    ports:
      - '5080:5080'
    volumes:
      - ./.data/openobserve:/data/
    environment:
      ZO_DATA_DIR: /data
      ZO_ROOT_USER_EMAIL: <EMAIL>
      ZO_ROOT_USER_PASSWORD: bikabika
    mem_limit: ${MEM_LIMIT:-2g}
    networks:
      - bika

  elasticsearch:
    profiles:
      - oltp
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/vikadata/elasticsearch:8.12.2
    restart: always
    environment:
      #     bootstrap.memory_lock: true
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1024m -Xmx1024m
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - xpack.license.self_generated.type=trial
      - ELASTIC_USERNAME=bika
      - ELASTIC_PASSWORD=bikabika
    ulimits:
      memlock:
        soft: 65535
        hard: 65535
    # volumes:
    #   - es_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ['CMD-SHELL', "curl -sS 'http://localhost:9200' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60
    ports:
      - '9200:9200'
      - '9300:9300'
    mem_limit: ${MEM_LIMIT:-2g}
    networks:
      - bika

  minio:
    ## default username and password is `minioadmin`
    profiles:
      - oltp
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/minio/minio:RELEASE.2024-02-26T09-33-48Z
    restart: always
    ports:
      - '9000:9000'
      - '9001:9001'
    environment:
      MINIO_ROOT_USER: bika
      MINIO_ROOT_PASSWORD: bikabika
      MINIO_ACCESS_KEY: bika
      MINIO_SECRET_KEY: bikabika
    volumes:
      - ./.data/minio:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ['CMD-SHELL', 'mc ready local']
      interval: 5s
      timeout: 5s
      retries: 30
    networks:
      - bika

  ## application
  bika-init:
    profiles:
      - app
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/${IMAGE_BIKA}
    env_file:
      - '${ENV_FILE:-.env}'
    command: ['/bin/sh', '-c', 'cd /app/packages/bika-server-orm  && npm run db:deploy && npm run db:seed']
    depends_on:
      postgres:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - bika

  bika-web:
    profiles:
      - app
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/${IMAGE_BIKA}
    restart: always
    expose:
      - '3000'
    env_file:
      - '${ENV_FILE:-.env}'
    command: ['/bin/sh', '-c', 'node apps/web/server.js']
    healthcheck:
      test: ['CMD-SHELL', "curl -sS 'http://localhost:3000' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60
    depends_on:
      bika-init:
        condition: service_completed_successfully
    networks:
      - bika

  bika-server:
    profiles:
      - app
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/${IMAGE_BIKA}
    restart: always
    expose:
      - '3333'
    env_file:
      - '${ENV_FILE:-.env}'
    command: ['/bin/sh', '-c', 'node server.js']
    healthcheck:
      test: ['CMD-SHELL', "curl -sS 'http://localhost:3333' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60
    depends_on:
      bika-init:
        condition: service_completed_successfully
    networks:
      - bika

  bika-server-doc:
    profiles:
      - app
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/${IMAGE_BIKA}
    restart: always
    expose:
      - '3366'
    env_file:
      - '${ENV_FILE:-.env}'
    command: ['/bin/sh', '-c', 'node server-doc.js']
    healthcheck:
      test: ['CMD-SHELL', "curl -sS 'http://localhost:3366' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60
    depends_on:
      bika-init:
        condition: service_completed_successfully
    networks:
      - bika

  toolsdk-init:
    profiles:
      - app
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/${IMAGE_TOOLSDK}
    environment:
      PG_DATABASE_URL: ${PG_DATABASE_URL}&schema=toolsdk
    env_file:
      - '${ENV_FILE:-.env}'
    command: ['/bin/sh', '-c', 'cd packages/@toolsdk.ai/orm && pnpm run db:deploy']
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - bika

  toolsdk-web:
    profiles:
      - app
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/${IMAGE_TOOLSDK}
    restart: always
    expose:
      - '3000'
    environment:
      PG_DATABASE_URL: ${PG_DATABASE_URL}&schema=toolsdk
    env_file:
      - '${ENV_FILE:-.env}'
    healthcheck:
      test: ['CMD-SHELL', "curl -sS 'http://localhost:3000' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60
    depends_on:
      toolsdk-init:
        condition: service_completed_successfully
    networks:
      - bika

  # Third Party Dockers
  gateway:
    profiles:
      - app
      - all
    image: ${IMAGE_REGISTRY:-docker.io}/${IMAGE_GATEWAY}
    restart: always
    ports:
      - ${NGINX_HTTP_PORT:-80}:80
      - ${NGINX_HTTPS_PORT:-443}:443
    volumes:
      - ./gateway/conf.d:/etc/nginx/conf.d
    networks:
      - bika
    depends_on:
      bika-web:
        condition: service_started

networks:
  bika:
    name: bika
    driver: bridge

volumes:
  es_data:
    driver: local
    driver_opts:
      type: 'none'
      o: 'bind'
      device: '.data/elasticsearch'
