# Developer Guide 开发指南

- [Developer Guide 开发指南](#developer-guide-开发指南)
  - [领域驱动开发文件结构与包依赖 Domain-Driven Development](#领域驱动开发文件结构与包依赖-domain-driven-development)
  - [文件结构与包依赖 File Structure](#文件结构与包依赖-file-structure)
  - [技术栈 Tech Stacks](#技术栈-tech-stacks)
  - [数据治理 Data Governance](#数据治理-data-governance)
    - [功能和能力 Feature \& Abiltiy](#功能和能力-feature--abiltiy)
  - [AI开发指南 AI Development](#ai开发指南-ai-development)
    - [后端：AI Agent功能与 AI Tools 对应](#后端ai-agent功能与-ai-tools-对应)
    - [前端: AI功能的 UI 组件关系](#前端-ai功能的-ui-组件关系)
  - [服务端开发指南 Backend Development](#服务端开发指南-backend-development)
    - [日常开发的标准动作](#日常开发的标准动作)
    - [究竟有多少个服务器？](#究竟有多少个服务器)
    - [数据结构ER图](#数据结构er图)
    - [模块数据流图 Module Data Flow](#模块数据流图-module-data-flow)
    - [Template模板的数据结构](#template模板的数据结构)
    - [Data Storage数据存储](#data-storage数据存储)
    - [Data Layers 数据分层](#data-layers-数据分层)
    - [Service Objects业务对象关系](#service-objects业务对象关系)
    - [SO (Service Objects)代码快速实例](#so-service-objects代码快速实例)
    - [ID类型](#id类型)
    - [如何新建一种Node Resource？](#如何新建一种node-resource)
    - [模板Template业务逻辑关系](#模板template业务逻辑关系)
    - [模板Template TypeScript编译](#模板template-typescript编译)
    - [tRPC和Prisma如何做到全自动、无代码的？](#trpc和prisma如何做到全自动无代码的)
    - [Cron Scheduler 定时器方案](#cron-scheduler-定时器方案)
    - [服务端缓存 Server ORM Caching](#服务端缓存-server-orm-caching)
    - [关于软删除 soft delete](#关于软删除-soft-delete)
    - [日志与搜索 Log \& Search](#日志与搜索-log--search)
    - [消息推送 Push Notification](#消息推送-push-notification)
    - [Bikafile](#bikafile)
    - [Websocket服务器](#websocket服务器)
  - [Stripe本地webhook调试](#stripe本地webhook调试)
  - [前端开发指南](#前端开发指南)
    - [前端代码约定](#前端代码约定)
    - [目录结构 Files Structure](#目录结构-files-structure)
    - [全局状态管理 Context Provider](#全局状态管理-context-provider)
      - [UIFramework框架Provider](#uiframework框架provider)
      - [全局Provider](#全局provider)
      - [子Provider](#子provider)
    - [全局Layout](#全局layout)
    - [Middleware路由中转器](#middleware路由中转器)
    - [Mutation UX 编辑型UX](#mutation-ux-编辑型ux)
    - [组件库 UI Theme](#组件库-ui-theme)
    - [Form表单types-form](#form表单types-form)
    - [编辑器Editor的增删改查组件/API代码规范](#编辑器editor的增删改查组件api代码规范)
      - [1. 理解Bika.ai的编辑器中的整体数据结构抽象元素](#1-理解bikaai的编辑器中的整体数据结构抽象元素)
      - [2. 编辑器接口](#2-编辑器接口)
      - [3. Form组件状态管理](#3-form组件状态管理)
    - [PC Web Workbench组件结构](#pc-web-workbench组件结构)
    - [Mobile 组件结构](#mobile-组件结构)
    - [Pages 网页设计](#pages-网页设计)
    - [渲染 Rendering](#渲染-rendering)
    - [编译方式 Next JS Build](#编译方式-next-js-build)
    - [内容加载 Contents Loading](#内容加载-contents-loading)
    - [Web前端StackNavigator使用指南](#web前端stacknavigator使用指南)
    - [客户端tRPC离线缓存 RPC Caching](#客户端trpc离线缓存-rpc-caching)
    - [Webpack打包分析](#webpack打包分析)
  - [DevOps 运维部署](#devops-运维部署)
    - [DevOps \& Deployment部署](#devops--deployment部署)
    - [中国域名及备案问题](#中国域名及备案问题)
    - [版本号与部署自动化](#版本号与部署自动化)
    - [Edge边缘函数服务](#edge边缘函数服务)
    - [共享开发机 \& CI机 \& 肉鸡](#共享开发机--ci机--肉鸡)
    - [Mac CI机初始化装机](#mac-ci机初始化装机)
  - [Big Data大数据开发指南](#big-data大数据开发指南)
    - [OLAP数据迁移消费者](#olap数据迁移消费者)
    - [大数据功能及开发 OLAP \& Big Data \& Search](#大数据功能及开发-olap--big-data--search)
    - [AI搜索引擎](#ai搜索引擎)
    - [Airbyte本地开发环境](#airbyte本地开发环境)
    - [用量统计 Usages](#用量统计-usages)
  - [自动化Storybook / 帮助中心文档Autodocs](#自动化storybook--帮助中心文档autodocs)
  - [压力测试 Autocannon](#压力测试-autocannon)
  - [What's more...](#whats-more)
    - [Zen](#zen)
    - [参考文章:](#参考文章)

## 领域驱动开发文件结构与包依赖 Domain-Driven Development

遵循领域驱动开发，三大区域：

- Application: 应用层，server、web、api等对外部署的服务
- Domains: 领域层，业务逻辑，这个目录一定是「产品都看得懂改得动」的
- Infrastructure: 基础设施、技术模块，与业务无关的”技术设施"，如Job、Scheduler等不显露给用户的

```mermaid
classDiagram

    class `@bika/cli` {
      命令行程序
    }
    class `@bika/desktop` {
      桌面端
    }
    class `@bika/web` {
      NextJS+Hono寄居服务器
    }
    class `@bika/mobile` {
      移动端
    }
    class `@bika/server` {
      Bun+Hono框架独立服务器
    }
    class `@bika/ui-storybook` {
      UI组件故事板
    }
    class `@bika/types` {
      聚合Domains们的types对外发布
    }

    class `@bika/api-caller` {
      前端API调用器
    }
    class `@bika/domains` {
      所有业务逻辑模块，前后端区分
      + client: Web端组件
      + services: 服务器SO
      + mobile: 移动端空间
      + apis: tRPC接口、Controller、HTTP API定义
      + types: 跨端共享的VO、BO、Zod Schema
    }
    class `@bika/server-orm` {
      数据库ORM，只允许服务器调用
    }
    class `@bika/ui` {
      UI控件库
    }

    `@bika/desktop`  --> `@bika/web`
    `@bika/mobile`  --> `@bika/domains`
    `@bika/types`  --> `@bika/domains`
    `@bika/cli`  --> `@bika/api-caller`
    `@bika/web`  --> `@bika/server`: Hono服务路由寄居NextJS
    `@bika/api-caller`  --> `@bika/web`: 网络调用客户端
    `@bika/api-caller`  --> `@bika/server`: 网络调用客户端
    `@bika/web`  --> `@bika/domains`: 服务端渲染组件无需网络接口，直接调用服务端的Domain Services
    `@bika/server`  --> `@bika/domains`: APIs路由调用Domains业务逻辑
    `@bika/domains` --> `@bika/server-orm`: 领域模型调用数据库，仅限服务器调用
    `@bika/domains` --> `@bika/ui`: 领域模型调用数据库，仅限服务器调用
    `@bika/domains` --> `@bika/api-caller`: Domain里的客户端组件，调用tRPC和API
```

## 文件结构与包依赖 File Structure

- `apps`:
  - @bika/web: NextJS服务器，前端UI+服务端API路由
  - @bika/mobile: 移动端
  - @bika/desktop: 桌面端
  - @bika/cli: 开发者命令行工具
  - @bika/server: Standalone 独立RPC/API服务器，抽离tRPC独立成纯API的服务器，没有React网页和Webpack，开发环境用不到，生产运维用途；
  - @bika/edge: 边缘函数，每个函数独立服务，部署Vercel Serverless Function和Edge Network，应用于Telegram Bots等独立服务
- `domains`: domain是业务模块的聚合，apps只做路由，通过webpack exports的方式引入domain模块。
  - apis: tRPC路由、HTTP接口
  - client: Web客户端有状态组件(如Context、useState)
  - ui: UI组件，通常放服务端渲染、客户端渲染都有需要的UI组件
  - server: 服务端业务逻辑
  - mobile: 手机UI
  - types: 共享类型、ZodSchema
    - ~~service.ts~~: 不使用service类，保持代码中的业务可读性，避免沟通鸿沟
- `packages`: Infrastructure，基础设置
  - packages/bika-server-infra: 服务器基础设置，如tRPC初始化等技术要素组件
  - packages/ui: 通用UI控件，通常是对@mui/ui的封装
  - packages/bika-server-orm: MongoDB和Prisma，`@bika/server-orm`，通常不直接暴露PO和ORM到web层，通过SO做桥接
- `contents`: 内容配置包

## 技术栈 Tech Stacks

- App
  - NextJS React Framework
    - NextJS REST API
    - @mui/joy
    - tRPC
  - Expo
  - Prisma: PostgreSQL ORM，存储所有业务逻辑，除了Records数据记录
  - Mongoose: MongoDB ODM框架，存储、查询、过滤Records数据行
  - React Native: Mobile apps
  - Tauri: Desktop apps
- Infrastructure
  - pnpm
  - lerna
  - MongoDB Atlas
  - Neon PostgreSQL
  - Vercel Deploy
  - Clerk Auth
  - Stripe
  - Firebase Cloud Messaging

## 数据治理 Data Governance

数据治理对 AI 效果起到重要作用，确保数据质量：高质量的数据是 AI 模型训练的基础。数据治理可以对数据进行清洗、去重、标注等处理，减少数据中的噪声、错误和不一致性，从而提高模型的准确性和稳定性。例如，在图像识别任务中，如果训练数据存在大量标注错误，模型可能会学习到错误的特征，导致识别效果不佳。

### 功能和能力 Feature & Abiltiy

Bika.ai 中，每个功能，都被描述化，既能把所有功能自动生成文档，同时每一个功能都可被 AI 识别、阅读、调度。

- Feature 功能: 像 Node Resources, Automation Actions 等;
- Ability 能力: 像 Database Fields 里的各种字段，都统称 Ability；可快速理解 Ability 是 Feature 的子集

同时也匹配 SDK 的面向对象编程，比如 database.fields等；

在 domains 目录中，大型模块会采取如何下的结构，前后端分离，比如：

- domains
  - automation
    - client
    - server
  - database
    - client
    - server

而 Feature Ability，为了功能多态，会采取如下的目录结构，前后端混合，比如：

- domains
  - automation-nodes
    - A-trigger
      - bo-input.tsx
      - server.ts
      - vo-renderer
    - toolsdk-action
      - ....
  - dashboard-widgets
    - B-wiget
      - bo-input.tsx
      - server.ts
      - vo-renderer.tsx

之所以这样做，原因：

1. 化普通水平程序员的开发门槛，一个 feature ability 目录，搞定前后端，新建文件夹就可以扩展；
2. 可以通过 Registry，动态 register 外部的MCP，成为一个 action
3. 构建知识库 (llms-full.txt)

## AI开发指南 AI Development

![Bika AI UI](./images/dev-ai-agent.drawio.svg)

理解 Bika.ai，我们分开前端和后端来看。
前端UI 部分是UI 组件组成的，不做业务逻辑，所有业务逻辑由服务端推过来，前端匹配 Tools 进行渲染；

服务端基于 AI Intent， 调用 LLM + Tools 的方式实现 AI Agent，也可以手写 AI Intent 逻辑代码。

### 后端：AI Agent功能与 AI Tools 对应

AI Intent 是 Bika 特有的意图管理方式。每个 AI 聊天都围绕一个特定的意图展开。通过手写代码，可以实现整个对话流程，并手动返回聊天流。也可以通过我们的 AI Agent 进行处理。

我们采用 AI Agent 的开发方式，采用工具驱动的方法。使用 AI SDK 和 maxSteps 设置，AI 可以自动展开对话。

除了手工创建的 AI Intent 之外，大部分 AI 功能都是通过 AI Tools 实现的，支持智能体的各种模式。这些功能依赖于大语言模型，让 AI 自主发挥。

以下列出各种 AI Agent 功能及其对应的 Skillsets：

- AI Consulting Team
  - AI Consulting Skillset
- AI Copilot

  - Database Ask Skillset
    - `search_records`:
    - `get_database_info`
  - Database Edit Skillset
    - `mutate_database`
  - Automation Ask Skillset
    - `get_automation_info`: 返回 automation info
    - `get_automation_vo`
  - Automation Edit Skillset
    - `mutate_automation`
    - `search_automation_nodes`: 查询详情

- AI Agent Node
  - 用户自选 Skillset 和 tools
- AI Page Node
  - 制作HTML页面: AI Page Skillset

也提供了 Weather 这个工具集示例。

### 前端: AI功能的 UI 组件关系

主要几个组件：

- AI Chat
  - AI Message
    - Parts
      - Source
      - Reasoning
      - Text
      - Tool
    - Artifact
  - AI Prompt: 从 Streaming 重 Message Annotation 返回

我们使用 Streaming Protocol 协议: https://ai-sdk.dev/docs/ai-sdk-ui/stream-protocol。

UI 组件做好后，业务逻辑全部从服务端返回 streaming protocol 来渲染，因此 UI 组件的核心是解析 Streaming 协议。

注意的是，一个Message 里面，除了 Source 搜索来源是顺序第一的，其它组件都是根据 AI Tool 调用无序返回，比如可以先一段 Text、接着 Tool、再接着 Text。

![Bika AI UI](./images/prd-ai-ui.drawio.svg)

Source, Reasoning, Text 和 Tool 都是Sreaming Protocol 内置的流，其余的能力，使用 Message Annotation 和 Chat Data 的自定义返回数据流。

比如:

- AI Prompts 界面，是从 Message Annotation 中返回, {type: "prompts", prompts: []}
- 前端启用什么 Skillset UI, 从 Chat Data中返回，{type:"skillsets", skillsets: []}
- Artifact，也是从 Message Annotation 返回， {type:"artifact", artifact: data}

## 服务端开发指南 Backend Development

### 日常开发的标准动作

> 我们严格执行BDD(业务驱动开发)、TDD(测试驱动开发)、DDD(领域驱动开发)
> 因此，合格成熟、脱离菜鸟的开发标准动作，从domain、test开始：

1. 先准备环境，执行一些系列make命令（参考首页[README Quickstart](../README.md))
2. 想想自己是要改动哪个domain？
3. 打开这个`domains`的文件夹 (比如:你是要对openapi或database模块进行改动)
4. 打开`tests`文件夹，看看目前模块的进度（注意！！！TDD！测试驱动！测试代码即用户需求，测试代码则接口定义！）
5. 如果这个domain，还有`docs`目录，打开看看里面的BDD故事；
6. 先写`tests`代码，模拟我的业务故事；
7. 打开`apis`和`server`目录，补充tests对应的业务逻辑
8. 执行以下命令进行集成测试(连数据库实测)

```bash
# 进入domain目录
cd domains

# 只跑openapi这个domain的test
pnpm run test:openapi
pnpm run test:reminder
pnpm run test:mission
pnpm run test:automation

# 没命令？新domain？你可以在domains/package.json的scripts那边加一下指定模块筛选

```

9. 跑通！开始下一个test！

### 究竟有多少个服务器？

由于工程的包按照领域模型进行代码切割，因此，在实际应用场景中，我们会基于同一套代码，分别利用单体服务、微服务(容器)、云函数、边缘计算等不同部署方式进行部署。

一套代码，随时切割服务部署方式，并且会应用Webpack 的tree-shaking，只打包用到的代码，剥离无用的模块。

真实使用的服务器有如下：

![How many Bika Servers](./images/dev-servers.drawio.svg)

除了主服务器API服务器，由于使用云函数会有Cold Start的问题外，我们推崇边缘计算Edge Functions、云函数Serverless Functions的部署方式，运维简便。

### 数据结构ER图

Prisma生成:

![Prisma ERD](./images/ERD.svg)

Markdown文档: [Prisma Markdown ERD](./ERD.md)

HTML文档: [Prisma HTML ERD](./erd/index.html)

### 模块数据流图 Module Data Flow

![Backend Development Layers](./images/backend-structure.drawio.svg)

### Template模板的数据结构

模板是串联整个系统的关键，模板的数据结构，有本地编辑的CustomTemplate、数据库的（PO）等等。关系如下图：

![Template Data Structure](./images/dev-template-data-struct.drawio.svg)

### Data Storage数据存储

区分小业务数据库(PostgreSQL 存取)、大业务数据库(MongoDB)。
除了「_Record_」，其余都是小业务数据库里。

之所以这样分离，是因为直接利用 MongoDB 的`$filter`能力，做数据的复杂查询、图关联，无需再操心自己去实现数据库运算方面。

业务模型 Template 固化+杠杆 MongoDB 查询能力+移除实时协同，技术工作量比 vika 减少 10 倍，同时增加客户价值，解决客户真正的问题。

绝大部分表，都会放在PostgreSQL，什么场景下适用mongodb？

- 不用操心事务一致性的
- 大数据：数据量是海量的、庞大的，亿级以上数据量
- AI和自动化会生产的
- 关系简单：只有一个或零个关联的
- 消息类：用户不停产生的数据
- 结构不固定：灵活表结构
- 高并发读，高并发写
- 聚合计算：需要地理位置查询、复杂JSON联查

常见就是以下表了

- Database Records 数据记录
- Comments 评论
- AI message 聊天消息
- Notification 通知
- Report 报告(如Email发送)
- ~~AuditLog~~
- ~~Automation Run History~~

我们会根据不同的场景，分工使用不同的数据库技术，示意图如下：

![How many Bika Servers](./images/dev-database-infra.drawio.svg)

### Data Layers 数据分层

| 分层                       | 命名文件       | 调用函数      | 对应模型          | 特征                                                                                                                           |
| -------------------------- | -------------- | ------------- | ----------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| FO (Form Object)           | fo.ts          | 前端.tsx      | React Hooks Forms | 经VO转换，用于前端表单React Hooks Forms，对应RJSF的formData, 前端表单的Controller                                              |
| RO (Render Object)         | ro.ts          | 前端.tsx      | 渲染数据结构      | VO兜不住时出现，经VO转换，部分前端渲染用数据独享，放在AG Grid、UI中，对应RJSF的UISchema，前端表单的View                        |
| VO (Value Object)          | vo.ts          | controller.ts | OpenAPI           | 通常为服务端传给客户端时，用于UI值显示，对应RJSF的JSONSchema, 前端的Model                                                      |
| ⭐️BO (Business Object)      | template/types | Template      | Template          | 没有id，有templateID，落盘存储                                                                                                 |
| ⭐️AIO (AI Object)           | types/aio      | 所有增删改查  | AI Wizard         | AI意图参数、UI、Resolve类型                                                                                                    |
| SO (Service Object)        | so.ts          | service.ts    | Service Objects   | 用在Service                                                                                                                    |
| DAO/PO (Persistent Object) | models.ts      | dao.ts        | Prisma, Mongoose  | 匹配数据库，会落盘存储                                                                                                         |
| DTO (Data Transfer Object) | dto.ts         | rpc.ts        | tRPC, REST API    | 一些放在tRPC/API接口内的传参，都是只停留在网络传输，不依赖UI、不依赖数据库如增删改查接口，如CreateRecordDTO, UpdateRecordDTO等 |

### Service Objects业务对象关系

大体来说，Bika的数据模型，跟Vika 100%一致，PO层（数据库）、SO层（业务逻辑）、BO层（Template模板结构）也100%相同。

Service Object(业务对象)是Bika后端工程最重要的理念，「重SO，轻DAO和PO」。

我们都知道后端开发通常使用”Service“类做业务逻辑的，它是”一堆函数“组成的，平铺了所有业务逻辑，逐步变得阅读困难、工程师自己对业务理解也变得晕头转向，只知道前端要一个API，但不理解业务的深入场景和层次是什么。

因此，我们不使用 `service.ts`的函数集合，而是使用 `XXX.so.ts` 面向业务对象的编程方式。

「Service Object业务对象」跟「Service业务逻辑类」差不多，唯一区别是打散成「面向对象的Service」，把「PO(Models)」进行包裹，让业务逻辑书写变得有层次。

下图是Bika常见业务对象(SO)的关系。

```mermaid
classDiagram
    class User {
      +Space
    }

    class Space{
        +Unit
        +Node
    }
    class Unit{
        +Member
        +Team
        +Parent
        +Children
    }

    class Node {
        +Database
        +Mirror
        +Automation
        +...
    }

    class Automation {
      +Trigger[]
      +Action[]
    }
    class Database{
        +View[]
    }
    class Mirror {
      +View
    }
    class Report {
      +ReportTemplate

    }
    class ReportTemplate {

    }
    class Campaign {
      +CampaignSequence
    }
    class CampaignSequence {
      +CampaignSequenceStep[]
    }
    class CampaignSequenceStep {

    }

    class Mission {
      +type: MissionType
    }

    User --> Space
    Space --> Unit
    Unit --> Member
    Unit --> Team
    Member --> Guest
    Space --> Node
    Node --> Database
    Database --> View
    Mirror --> View
    Node --> Mirror
    Node --> Automation
    Node --> Campaign

    Campaign --> CampaignSequence
    CampaignSequence --> CampaignSequenceStep
    Report-->ReportTemplate

    class Reminder {

    }
    class Scheduler {

    }
    class Job {

    }
    Automation --> Action
    Action --> SendReportAction
    SendReportAction --> Report

    Automation --> Trigger

    Trigger --> Scheduler
    Reminder --> Scheduler
    Scheduler --> Job
    Todo --> Mission
    Todo --> Task
    Database --> Record
    Record --> Task
    Record --> Datum
    Mission --> Reminder
```

关于SO的一些开发约定：

- SO对象，都是private constructor，使用`init`进行对象初始化
  - SO对象中，没有对外使用的，都标记private，避免编辑器的Auto Complete出现太多杂物和函数被滥用；
  - SO通常包裹着PO，为了避免是增删改查的误会，初始化对象使用`initXXX`静态函数（参考Objective-C)，不得使用`new`和`newXXX`；
  - SO对象，public的函数，通常不会返回PO对象，只会返回SO或VO对象；
  - SO对象通常有`get model()`属性，仅仅为了方便取值，禁止通过SO对象修改Model（PO）对象；
  - 禁止对SO对象的实例变量进行操作修改，错误示范:databaseSO.fields.push(XXX)，正确示范是: databaseSO.getView(XXX).addField(XXX)
  - NodeSO的Resource，是对应节点“内馅”的抽象，比如: nodeSO.getResourceSO() -> DatabaseSO | AutomationSO |...
- 禁止SO类以外写具体业务逻辑，其它地方(tRPC/Controller)禁止出现业务逻辑；
  - Controller禁止直接调用Models(PO)，只能调用SO(Service Object)；禁止出现独立的`service`函数集合类；
  - tRPC直接调用Controller，唯一作用是路由参数处理；
  - Controller直接调用SO，唯一作用是VO转换处理；
  - 服务端代码工作，95%的具体工作在SO对象；
- 禁用反射(如@annotation注解)、事件(EventEmitter)等非”显示函数调用“的语法，除非是某个体系化的功能模块，用最朴素的语法
- 少花时间意淫语法，多花时间理解业务，因为任何技术是基于某个业务需求而诞生的（没有之一），不理解业务根本就无法理解技术内涵，搞清楚WHY而不是WHAT；
- 模板类型(Template Types)，是一种BO，Business Objects，通常是能给到用户配置、写模板的；

不理解为什么这么做？

- 参考阅读：[Java使用MVC开发模式开发了这么多年的项目，才知道一直在面向过程（面向数据库）开发，面向对象并没有想象中那么简单](https://blog.csdn.net/yangxiao_hui/article/details/107413248)
- 减少因低水平工程师对一些新潮语法的自嗨，但又无能力进行结构化工程，导致代码混乱
- 业务语言导向，统一沟通语言
- 其实，都是抓住一些非常朴素但知易行难的道理：**MVC、OOP、工厂模式、多态**；

**最后警告：约定了就约定了，文件结构严格遵循，不要自己发挥，有改进建议大群协商，否则后果很严重。**

### SO (Service Objects)代码快速实例

由于服务端开发围绕SO面向对象编程进行，SO的接口又都差不多，这里举个示例代码：

```typescript
// ================ vika.test.ts，集成测试，模拟用户操作
// 新注册一个用户！
const userSO = UserSO.createUser();
// 用户创建一个空间站
const spaceSO = userSO.createSpace();
// 创建一个文件夹
const folderNodeTreeSO = spaceSO.createFolder();
// 模拟用户，假设我要新建一个表
const databaseNodeSO = folderNodeTreeSO.createDatabase();
const databaseSO = databaseNodeSO.resource;
// 数据行记录是VO，VO是纯interface值，没有函数，很简单，没必要；
// SO是包括VO和PO(Model)，有操作函数；
const recordVO = databaseSO.createRecord({
  Field1: 'A',
  Field2: 'B',
});

// =================  trpc.ts tRPC接口
function getAllSpaces() {
  SpaceController.getAllSapces();
}
// ================= tRPC和OpenAPI接口的具体实现，写在Controller
// file: space/controller.ts
export function createRecord(spaceId: number, databaseId: number, recordData: any) {
  const userSO = response.currentUser;
  const spaceSO = userSO.getSpace(spaceId);
  const databaseSO = spaceSO.getDatabase(databaseID);
  return databaseSO.createRecord(recordData);
}
```

怎样，是不是很”人性化“？ 用户操作=测试代码=业务对象。
只要你懂业务，你就基本会写SO面向对象代码；反过来则不行，由于Bika的UI端很轻，你不会业务，就只能乱写捣乱了...

这种模式，业界总结叫[BDD行为驱动开发](https://www.51cto.com/article/573796.html)，可点击文章参考

### ID类型

数据记录，通常有2种ID，要记住他们的使用：

1. `id`: 数据库主键，是字符串实例ID，NanoID生成，带3位前戳，可根据情景生成不同带不同的前戳，被索引；
2. `templateId`: 是模板ID，通常是由template模板配置的，便于人肉眼就能看出是干什么的，在引用模板时会重复覆盖；

通常使用了模板之后，创建的Folder文件夹，会绑定Template，实际上，就是指新建的Node节点们，会绑定templateId。

就像Figma一样，必须`Detach`，才能实现解绑一个模板，变成自由编辑的数据表。

### 如何新建一种Node Resource？

根据业务需要，有时候需要新增一些Node Resource，参考这些checklist，看看是否都做完了：

- [ ] @bika/types，新增Node Resource的DTO、BO、VO了吗？
- [ ] NodeResourceSO做好了吗？
- [ ] 单元测试的增删改查做好了吗？
- [ ] FolderSO.createChild 函数写了吗？
- [ ] 有模板Template用起来了吗？
- [ ] 模板升级支持了吗？
- [ ] 所有集成NodeResourceSO的接口写了吗？

### 模板Template业务逻辑关系

```mermaid
classDiagram
    class Template {
        +AttachedInstance
    }
    class AttachedInstance {
        +Node
        +TemplateFolder
    }
    class DetachInstance {
        +Node
        +Folder
    }
    Template --> AttachedInstance
    DetachInstance --> Node
    DetachInstance --> Folder
    AttachedInstance --> TemplateFolder
    TemplateFolder --> Node
    Node --> Folder
```

### 模板Template TypeScript编译

模板是TypeScript脚本，会被打包成js文件，过程如下：

![Template Script](./images/template-script.drawio.svg)

### tRPC和Prisma如何做到全自动、无代码的？

服务端的同学肯定好奇，为什么像过去Model/PO/DAO、API客户端调用这些需要大量手写代码的工作，在Prisma和tRPC中，都做到完全自动没有代码？

原因是，这两个工具，都使用了同一个奇淫技巧，就是：直接修改`node_modules/`目录中自己的代码，把生成的Model和RPC函数，直接写到他们自己库的代码里。

比如，你看看Prisma的[这个index.d.ts文件](../node_modules/.pnpm/@prisma+client@5.7.1_prisma@5.7.1/node_modules/.prisma/client/index.d.ts)

### Cron Scheduler 定时器方案

![Cron Scheduler](./images/cron-scheduler.drawio.svg)

### 服务端缓存 Server ORM Caching

为了加快服务端响应速度，减少数据库的压力，部分性能热点的数据库请求，我们进行服务端数据库缓存，我们称作Server ORM Caching。

![RPC Caching](./images/cache-server-orm.drawio.svg)

代码写在ORM package。

[BDD故事](./bdd/cache.md)

### 关于软删除 soft delete

Bika系统不再使用软删除能力。
取消所有硬删除，根据业务需求再判断哪里需要，避免增加业务无关的技术复杂度、处理软删除的代码。
尽量不使用“软删除(soft delete)”，而使用“归档”（archive），以业务语言推动数据库结构，如Record 归档，User 归档。

### 日志与搜索 Log & Search

| 日志类型                  | 写入方式    | 描述                                                                   |
| ------------------------- | ----------- | ---------------------------------------------------------------------- |
| 系统日志 System Log       | console.log | 程序里的日志，K8S运维里另外进行采集                                    |
| 高频业务日志 Business Log | db.mongo    | 高频业务日志，用户会用到的，放MongoDB，比如计费用量等                  |
| 低频业务日志 App Log      | db.log      | 比如审计、trpc、可观测指标等等，业务上不常调用，写入OpenObserve        |
| 搜索索引 Search           | db.search   | 全文索引，写到Elastic Search，包括模板、节点资源都会写写进去做全文搜索 |

### 消息推送 Push Notification

Bika重要的能力，就是通知了。
通过各种第三方服务来实现：

![](./images/push-notification.drawio.svg)

### Bikafile

Bikafile是一种Bika的文件格式，是一切功能开发、最后的胜利果实：它能够把Bika内部所有的资源、模板导出，也可以把完整的资源、模板导入，保留关联、图片等所有关系，是一种与外界沟通的重要中间格式。

它代表了Bika的功能、资源都完善、融汇贯通。

文件本质：

- 一个zip压缩包
- 带有Bikafile的meta文件，是个json，描述元信息
- 根据meta类型，处理zip包内的其它格式

文件格式：

- template格式，内含文件
  - Bikafile
  - template.json
  - README.xxx
  - release/文件夹
  - assets/文件夹
- resources格式，内含文件
  - Bikafile
  - resources.json
  - assets/文件夹

使用场景：

- 备份/转移：导出Bikafile，导入Bikafile，所有资源的关联关系保留；
- 模板：导出模板到Bikafile
- 导入的中间格式：Excel、MySQL等等外部格式，先转换成Bikafile（实际上就是BO），再像普通Bikafile那样导入；

模式：

- 内存模式：支持纯内存读字符串模式，不需要磁盘读写；
- 文件模式: 加载文件到内存，变成内存模式；

### Websocket服务器

我们使用一体化工程，NextJS，包裹住了Hono Web Framework -> tRPC，把API服务器整合在NextJS、web工程中。

但是，NextJS，支持HTTP和SSE协议，不支持Websocket协议。

要使用Websocket模式， 使用命令启动独立服务器（NodeJS + Hono WebFramework -> tRPC）:

```bash
make dev-server
```

启动后，mac上可以测试websocket是否生效

```bash
brew install websocat
websocat ws://localhost:3333/api/ws
```

尽量使用SSE完成服务端事件发送，但一些场景没办法必须用到WebSocket，如协同文档（Document）。
开发模式中，使用TipTapCloud，单体服务器启动即可。

## Stripe本地webhook调试

执行命令模拟:

```bash
stripe login
stripe listen --forward-to localhost:4242/webhook
stripe trigger payment_intent.succeeded
```

## 前端开发指南

### 前端代码约定

- NextJS仅仅是一个路由，`/apps/web`目录仅可存放page, layout等NextJS官方约定式文件，其余文件禁止出现；
- **前端不要“定义”新的 interface 和 type！！** 复用(reuse)后端的types，如 VO、BO、DTO，有新的 types，也是再@bika/types 中定义；
  - 如果万不得已需要改 types，但又不想自己改，可以联系到后端，讨论 schema结构如何改；
  - 不用随意定义新的 UI Component，尽量找到已的复用，且由于 types 是 reuse 的，UI Component 的数量基本跟 types 数量保持相近；
  - UI Component 的 inter Props，通常都试用 VO/DTO/BO 等，而不是零散的字段（如重复定义 name, description)等；
- 前端业务模块化主要在`packages/domains/client/xxx.tsx`，按业务模块(Domain)区分文件夹，根目录禁止出现技术术语（如hook,component,context)等，藏在domains/XXX/client目录中；
- 禁止引入Redux等第三方状态管理，使用状态管理：Provider Context、useState、URL路由控制；
  - 所有的UI界面，尽可能地能用「URL路由(pathname)」控制，便于用户复制网址给别人，别人迅速进入界面；
  - Modal窗口，统一使用`spaceContext.showUIModal`中央控制，能在任意界面弹出Modal，网址也记录了UI状态；
  - 服务端控制的UI界面，建议使用SpaceContext的state控制；
  - 部分场景试用 zustand进行 全局状态(Global State)，但依然尽可能避免；
- 能使用async / await的地方，就尽量不使用 .then();
- 第三方UI库在尽量封装，如`@mui/joy`等，必须在`@bika/ui`中进行封装，方便后续随意替换UI库，分离视觉定制；
- 不要用\<form\>等表单标签，而是对务端 VO 或 DTO 的封装，详情查看 [Form表单(Types Form)](#form表单types-form), 数据结构通常有 value 和 onChange
- 前端业务模块遵循MVC开发方式：
  - M(Model)，通常就是`xxx-fo.ts`的前端表单对象、渲染对象类型定义，通常是第三方组件的数据结构；
  - C(Controller)，通常就是`xxx-view.tsx`，的react组件
    - `*-view`里，通常使用tRPCQuery，完整view通常同时使用 {isLoading, error, data}
  - V(View)，通常就是`xxx-component.tsx`、`xxx-renderer`的React组件
  - 多用**运行时类型**检查：ZodSchema.parse / assert / if throw new Error
    - 这些仅仅是编译期类型检查和IDE编辑期类型检查：非空断言!、TypeScript等；
    - 不要将**运行时类型**检查故意抹掉、跳过检查，来掩盖代码缺陷，而是从外部寻找、重构或修复导致类型检查错误的真实原因；


**最后警告：约定了就约定了，文件结构严格遵循，不要自己发挥，有改进建议大群协商，否则后果很严重。**

### 目录结构 Files Structure

> 以下是工程约定，请按照约定放置services

- apps/web/src: NextJS工程
  - `app`: NextJS路由routers；
- packages/domains/client/XXX.tsx: 前端Service，业务模块；简单来说就是和后端Service匹配调度的；
  - `业务模块名`: 如database、space、automation等
    - `xxx-context` | `context`: 处理tRPC调用和状态管理
    - `xxx-view.ts` | `views`: UI控件
      - View和Component什么区别？view是带有Context调用的Component，而Component通常是纯UI组件
      - 常见嵌套关系:
        - page -> view -> component
        - page -> modal -> view -> component
        - page -> modal -> stack -> view -> component
        - page -> stack -> view -> component
    - `xxx-component` | `components`: UI组件，同模块多个view共同享用的UI组件
    - `xxx-fo.ts` 用户表单的数据结构，Form Object，编辑表单统一放这里，通常匹配服务端的VO
    - `xxx-ro.ts` 渲染用户的数据结构，Render Object，通常由VO转换
    - `xxx-hook` | `hooks`: Hook
    - `utils.ts`: 工具类，通常用来转数据结构， VO到FO，RO到VO等等
    - 其它UI词参考: slot, part, view, widget...
- packages/sharelib: 共享工具类
- packages/domains/server/XXX.ts: 服务端业务逻辑，请不要放tsx或react的东西在这里
- packages/ui: React组件，通常服务端、客户端组件通杀
  - 服务端渲染组件，建议web里第一个page，然后调用server-service的SO，赋值@bika/ui组件，无Context

就像服务端的Service Object，按照业务模块进行代码文件组织；前端则则使用Service Context，组织代码文件。

![Backend Development Layers](./images/backend-structure.drawio.svg)

### 全局状态管理 Context Provider

Bika开发中，会有一些“全局React hooks"，这里介绍他们的层次，避免重复引用。

代码层次示例，仅仅示例，不是实际嵌套代码：

```tsx

{ /* Next.js middleware */ }
<MiddlewareRouting>
  { /* Next.js App Router 或 Vite的React Router的Layout */ }
  <Layout>
    { /* 不同的UI框架引入接口*/ }
    <ViteUIFrameworkProvider_OR_NextUIFrameworkProvider>
      <LocaleProvider>
        <APICallerProvider> {/* APICaller，依赖useLocale */}
            {/* 全局Provider状态管理 */ }
            <GlobalProvider>
              {/* Node Resource增删改查接口，都在这里*/}
              <NodeResourceApiProvider />

              {/* 这些通常被Global Provider里就被包住，各模块具体使用时不需要自己引入 */ }
              <SnackbarProvider /> {/* useSnackbar() */}


              {/* 其它，这些是其它模块内部，各自使用的，各模块需要自己显式调用*/ }
              <SpaceProvider />
              <ResourceEditorProvider /> {/* useResourceEditorContext() */}
              <DatePickerProvider /> {/* useDatePicker() */}
              {/* ... 各模块可以有自己的Context和API，封装trpc，不一一列举了*/ }

            </GlobalProvider>
        </APICallerProvider>
      </LocaleProvider>
    </ViteUIFramework>
  </Layout>
</MiddlewareRouting>

```

实际常用的使用：

```tsx
export function XUIComponent() {
  const frameworkContext = useUIFrameworkContext();
  const localeContext = useLocale();
  const apiContext = useApiCaller();
  const globalContext = useGlobalContext();

  // {/* Node Resource增删改查接口，都在这里*/}
  const nodeResourceApiContext = useNodeResourceApiContext();

  const spaceContext = useSpaceContext();
  if (spaceContext) {
    // ... 这个可能为null，除非使用 useSpaceContextForce() (不建议，影响独立ui剥离);
  }

  // 如果在编辑器里，内嵌NodeResourceApiContext
  const editorContext = useResourceEditorContext();
}
```

#### UIFramework框架Provider

框架Provider用于同时支持Vite和NextJS的抽象Provider，将import Link from 'next/link', useRouter等显式导入，变成useContext使用，实现多框架同时使用，它通常包住全局Provider。

#### 全局Provider

使用**全局Provider**进行全局状态管理，它们通常承载一些全局的业务逻辑，区分业务层次。
全局Provider只允许在@bika/domains和@bika/web出中先，@bika/ui中是不能使用的。

| Provider类型                          | 特征                                                                                                   |
| ------------------------------------- | ------------------------------------------------------------------------------------------------------ |
| WebFrameworkProvider                  | 存取Web框架特有的组件，如next/navigation, router, theme等，剥离Web框架，既可以用Vite，也刻可以用NextJS |
| LocaleProvider                        | Locale和APICaller，比Global的地位高                                                                    |
| APICallerProvider                     | tRPC调取                                                                                               |
| GlobalProvider(Next.js website模式）) | 网站使用，特点是没有cookie，即没有auth/user，通常带有headers和trpc                                     |
| GlobalProvider(Next.js cookie模式)    | 有状态的应用使用，比如template模板中心，有登录状态显示，通常带有user, session, auth                    |
| GlobalProvider(Vite SPA模式)          | SPA单页应用                                                                                            |
| GlobalProvider(Storybook模式)         | Storybook使用                                                                                          |
| SpaceProvider                         | 进入空间站后，通常带有space                                                                            |
| EditorAppProvider                     | 资源编辑器专用，多态于SpaceProvider和@bika/editor中                                                    |

我们有多个next工程，全局Provider层次为：

- **@bika/web**: Locale -> APICaller -> GlobalProvider(auth) -> SpaceProvider -> EditorAppProvider
- **@bika/desktop**: Locale -> APICaller -> GlobalProvider(auth) -> SpaceProvider -> EditorAppProvider
- **@bika/mobile**: Locale -> APICaller -> GlobalProvider(mobile) -> SpaceProvider (手机端暂时没编辑器)
- **@bika/editor**: Locale -> APICaller -> GlobalProvide(无auth) -> EditorAppProvider

#### 子Provider

通常，全局Provider下方，都包含以下常用的Provider，供业务使用

### 全局Layout

- WebPageLayout: 网页
- AppPageLayout: 独立应用页面
- SpaceLayout: 空间站

### Middleware路由中转器

- web-page-routing
- app-page-routing
- space-routing

### Mutation UX 编辑型UX

Bika 中的所有交互我们区分成两种交互模式：

- Query：查询式，即普通展示给用户看，用户只看不动手；
- Mutation：改变式、编辑式，即用户是要交互操作的；

Query没什么好说的，各种展示形式。

软件工程中，编辑型UIMutation的UX占据最大量的精力和时间。

对于用户的编辑需求，Mutation UX我们常用三种UI组件的制作方式：

- AI Wizard: UI AI向导的方式，结合意图
  - AI Intent UI
- Zod Form: 提供一个Zod结构化，直接数据渲染表单
  - Zod Types
- Custom Form: 自由用React定制表单
  - 自由定制

| Mutation编辑交互类型 | 数据结构   | 应用场景                                    |
| -------------------- | ---------- | ------------------------------------------- |
| AI Wizard            | Intent意图 | 新建reminder，新建一行数据等，新手引导      |
| Zod Form             | Zod结构    | User Settings, Space Settings规范化表单场景 |
| Custom Form          | 自由       | Record Detail数据编辑                       |

### 组件库 UI Theme

> 使用了以下组件库，约定好，不随便弄新的或混用

- [mui](https://mui.com/)

### Form表单types-form

我们不用`<form>`标签，不用`<form>`框架，而是把表单里的一个个控件看成原子组件，针对服务端传回来的数据对象(VO/DTO)，面向对象表单控件 (OOF, Object-oriented Form Components)。

比如，服务端trpc接口要求input个CustomVO，你要对它做“增删改查”，不要另外创建一个"CreateCustomVO"做控件，而是直接对最终的CustomVO对象进行组件封装。

```tsx

interface CustomVO {
  a: string;
  b: string;
  c: string;
}

interface CustomVOInputProps {
  value: CustomVO;
  onChange: (newVal: CustomVO): void;
}

export function CustomVOInput(props: CustomVOInputProps) {
  return(
    <Input value={props.value.a} onChange={(newVal) => props.onChange({...props.value, a: newVal }) />
    <Select value={props.value.b} onChange={(newVal) => props.onChange({...props.value, b: newVal }) />
    <Input value={props.value.c} onChange={(newVal) => props.onChange({...props.value, c: newVal }) />
  )
}


```

这里要注意的，一定要「同进同出」一个类型，不要觉得里面「更方便的封装」，输出了与输入不同的 type。

由于“服务端”要求的数据结构，是一致的，如服务端trpc要求input CustomVO，那么表单就必然是处理CustomVO。

这里会有「能量守恒」： 由于服务端要求的数据结构是恒定的，Input组件要同进同出，在Input内部就满足服务端要求的数据结构，不要为了简化抛到外面，外面处理反而更加复杂。

里面不封装，等于把锅扔到外面。

### 编辑器Editor的增删改查组件/API代码规范

学会如何优雅地做好资源编辑器（Resource Editor），首先，严格遵循types-form的原则：服务端返回什么数据结构、前端就基于这个数据结构做XXInput组件，必定有`value`和`onChange`。

Input是组件，那么完整的增删改查表单form怎么弄？

#### 1. 理解Bika.ai的编辑器中的整体数据结构抽象元素

Bika中的「抽象元素」如下：

- **NodeTree**
  - **Folder**: Folder, TemplateFolder
  - **Node Resource**: Automation, Database, Mirror, Form, Folder, Dashboard, AI Agent, Doc
- **Features**: Automation Action, Automation Trigger, Database Field, Database View, Dashboard Widget, Doc Block
- **Unit**: Role / Team / User
- **Integrations**: Telegram, Slack.....

理解以上元素后，表单的增删改查，围绕上面的元素进行，比如会提供：

- useFolder
- useFolderMutation
- useFeature
- useFeatureMutation
- useUnits
- useUnit
- ....
- ....

#### 2. 编辑器接口

基于「抽象元素」，编辑器会提供如下的这些接口：

```typescript
interface INodeResourceApi {
  useRootNode: () => { data: NodeTree; isLoading: boolean; refetch: () => void };
  // 可能是Template，也可能是FolderBO
  useFolder: () => { data: EditorNodeFolderDTO; isLoading: boolean; refetch: () => void };
  useNodeResource: { data: NodeResource; isLoading: boolean; refetch: () => void };
  createNodeResource: (newData: NodeResource) => Promise<void>;
  useUnits: () => { data: UnitsBO; isLoading: boolean; refetch: () => void };
  // .....
  // .....
}
```

请尽可能确保一个form中，只会发起一次tRPC和网络请求。如果你要实现的功能，需要2个以上的trpc接口，组合才能完成，请考虑让服务端改schema和tRPC

#### 3. Form组件状态管理

增删改查，围绕上面的EditorApi获取的数据结构进行，大致代码结构如下：

```typescript
export function NodeResourceEditorView() {

  const nodeId = ...;

  // isLoading，正在加载中，refetch，重新抓取
  const { data: fetchedNodeResource, isLoading, refetch  } = api.useNodeResource(nodeId);
  // isMutating，是否已经发起了修改请求，网络请求状态
  const mutate = api.useNodeResourceMutation(nodeId);
  const { isMutating  } = mutate;

  // 保存正在编辑的数据值
  const [value, setValue] = useState<NodeResource | undefined>(undefined)

  // 交互层，用于是否显示保存按钮
  const [changed, setChanged] = useState<NodeResource | undefined>(undefined)

  // 把远程获取的存起来
  useEffect(() => {
    // 当远程抓取到数据后，赋值本地状态，用于”增删改查“
    setValue(fetchedNodeResource);
  }, [fetchedNodeResource])


  // 一些数据层次较深，需要做数据变换才拿到的数据结构，但又想拿到第一层UI展示的
  // 通过useMemo和useCallback，自定义getter和setter
  const cover = useMemo(() => {
    return value.folder.cover;
  }, [value])
  const setCover = useCallback((newCover: AvatarLogo) => {
    // 赋值
    setValue({
      ...value,
      folder: {
        ...value.folder,
        cover: newCover,
      }
    })
    // 激活保存状态
    setChanged(true);
  })

  return (
    <NameDescriptionBOInput
      value={{
        name: value.name,
        description: value.description // 实际上直接传value即可，这里展开name和description为了方便理解
      }}
      onChange=((newVal) => {
        setValue({ // 整个value重新set，激活react的state更新，UI也实现变化
          ...value,
          name: newVal.name,
          description: newVal.description
        })
        // 激活保存状态
        setChanged(true);
      })
    >
    // 其它types-form
    <SelectInput
      options={[]}
      value={value.type}
      onChange={() => {
        setValue({
          ...
        })
        setChanged(true);
      }}/>

    // 直接放setter和getter
    <AvatarLogoBOInput
      value={cover}
      onChange={(newVal) => {
        setCover(newVal);
        setChanged(true);
      }}
    />

    <Button disabled={!isChanged} />

  )
}
```

**留意了**，不要滥用useState，理论上一个组件，只会定义如下setState：

- **[value, setValue]**: 存放服务器抓取到的数据，放在value里，进行增删改查；
- **[changed, setChanged]**: 存放表单修改状态;
- 其它：待补充，[errors, setErrors]??
- ...其它有必要的补充，如无必要，不要增加useState，容易脱离数据结构

### PC Web Workbench组件结构

![Workbench Component](./images/ui-workbench-structure.drawio.svg)

### Mobile 组件结构

![Mobile Component](./images/ui-mobile-structure.drawio.svg)

### Pages 网页设计

只需要定好几个网页布局，营销内容由AI多语言生成。

![Web Pages Design](./images/ui-web-pages.drawio.svg)

### 渲染 Rendering

- Client Rendering 客户端渲染，React MVVM，把JS加载到浏览器之后，再通过API获取数据，动态渲染，场景是交互比较多、体验要求比较高的程序；
- Server Dynamic Rendering 服务端渲染，React在服务端执行，每次请求变成HTML，传到客户端，类似JSP，场景是SEO；[点击详情](https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-rendering)
- Server Static Rendering 服务端静态渲染，Build的时候生成HTMl文件了，我们尽量不使用static rendering, (但Desktop程序用到)

- `app/(lang)`: 这个目录均为服务端渲染的组件(use server)，包括blog、落地页、模板页等，用于营销内容生成
- `space`: 这个目录都是客户端渲染组件(use client)， [/src/app/space/page.tsx](../src/app/space/page.tsx) 这个SPA（单页应用程序）入口是'use client'的客户端渲染外，其余全部都是服务端渲染组件。

| -(Docker模式) | CSR客户端渲染    | SSR 动态服务端渲染     | SSG 静态服务端预编译       |
| ------------- | ---------------- | ---------------------- | -------------------------- |
| 应用场景      | 单页应用, /space | 内容网页, /[lang]/\*\* | ❌ 不使用                   |
| NextJS组件    | Client Component | Server Component       | generateStaticParams       |
| 渲染缓存      | 无需，交给CDN    | 无需，交给CDN          | -                          |
| 数据抓取      | tRPC             | dynamic import         | 编译期generateStaticParams |
| 数据缓存      | tRPC Query缓存   | 无                     | -                          |
| CDN缓存       | 有，10分钟       | 有，1小时              | -                          |

### 编译方式 Next JS Build

NextJS有多重编译模式，我们全部涉猎，均有不同的应用场景。

| -        | vercel            | standalone          | export          | edge           |
| -------- | ----------------- | ------------------- | --------------- | -------------- |
| 可用状态 | ✅                 | ✅                   | ❌               | ❌              |
| 应用场景 | Vercel Serverless | Docker              | Desktop         | 独立服务器     |
| 编译产出 | Vercel内部        | NodeJS:server.js    | HTML静态文件    | tsup服务器only |
| 配置     | vercel build      | output:'standalone' | output:'export' | apps/edge      |

### 内容加载 Contents Loading

在`contents`这个文件夹有各种配置、内容文件，他们需要被「遍历(list)」和「抓取(fetch)」，通常有几种策略：

1. import：这个简单，手写，就是当成一个package，直接import使用，需要遍历的List？直接写一个const map = {}手写即可，适合config内容文件。
2. dynamic import: 使用dynamic import惰式加载package单个内容，适合i18n；
3. Static API route: 区分开发环境和编译后环境，利用一个API Route遍历列表，API Route在开发环境，使用fs读本地环境，在编译期间静态内容编译，适合templates, pages, blogs等可能上万量级的场景，资源类型复杂 (如txt文本文件等，非webpack不方便import)
4. Static Page: 编译后环境，静态页面，如page、blog等在编译期，直接变成静态网页了，运行时无需import和fs
5. tRPC + DB: 通过Prisma的Seeding将本地内容灌到数据库里，再通过tRPC加载；

![Dynamic Content Loading](./images/dev-content-loading.drawio.svg)

> 为什么唯独template通过Static API，而不是直接编译Static Page？考虑到像template这个场景，不单有“官方模板”(package)，还有“用户模板”(放在数据库里)，因此使用SSR服务端渲染，而不是SSG服务端提前生成
> 此外，目前仅有的SSG应用场景是编译Desktop桌面客户端，减少Static Page减少其包大小

| 内容包                  | 量级 | 配置方式   | 加载方式                  | 开发环境保存方式 | 生产环境保存方式 |
| ----------------------- | ---- | ---------- | ------------------------- | ---------------- | ---------------- |
| @bika/pages             | 千   | TypeScript | server:dynamic import     | package          | DB               |
| @bika/blogs             | 千万 | Markdown   | server:Prisma             | FS               | DB               |
| @bika/xxx-configs       | 十   | TypeScript | import                    | package          | webpack          |
| @bika/i18n              | 十   | TypeScript | dynamic import            | package          | webpack(lazy)    |
| @bika/templates(TS)     | 百万 | TypeScript | client:tRPC,server:Prisma | package          | DB               |
| @bika/templates(README) | 百万 | Markdown   | client:tRPC,server:Prisma | FS               | DB               |

### Web前端StackNavigator使用指南

在Expo手机开发中，经常需要Modal、Stack多种UI的「同页导航」，在有限的手机界面大小中，通过StackNavigator很好地进行页面切换管理。

由于在Bika UI交互设计中，我们采取「手机端优先、Web端镜像模仿」的理念，尽可能把Web端的交互，跟手机端对齐、一致，因此，在Web端我们同样大量地方需要用到Stack Navigator来提升交互体验。

但是，Web端Next JS不像Expo的Expo Router那样原生支持「Stack Navigator」，我们需要模仿其接口，尽可能地保持一致。

创建Stack Navigator，接口举例如下：

```tsx

const Stack = createStackNavigator<RecordDetailStackScreenType>();

export function RecordDetailStack(props: Props) {
  return (
    {/* initialRouteName设置，即打开这个页面，首先出现哪个Screen */}
    <Stack.Navigator initialRouteName='RECORD_DETAIL'>
      <Stack.Screen route="RECORD_DETAIL" component={<StackScreenViewExample />} />
      <Stack.Screen route="SELECT" component={<SelectStackScreenView />} />
    </Stack.Navigator>
  );
}

```

Stack Screen，举例使用如下：

```tsx
export function StackScreenViewExample() {
  const ctx = useStackNavigatorContext();

  return (
    <>
      {/* push下一页 */}
      <button
        onClick={() =>
          ctx.router.push('SELECT', {
            dstId: '1',
          })
        }
      >
        Go Select Year{' '}
      </button>

      {/* 返回、后退 */}
      {!ctx.router.isTop() && <button onClick={() => ctx.router.pop()}>Go Back</button>}
    </>
  );
}
```

特别注意，目前PC Web端，只有2种「UI容器」能支持Stack Navigator，分别是：SpaceUIModal、SpaceUIDrawer、SpaceUIStandalone。

其它地方尽量不使用Stack Navigator。

像手机端一样，「导航栏」（HeaderBar）是分！离！，是存在于SpaceUIModal和SpaceUIDrawer的UI里。

Stack.Screen里仅仅设置标题参数，类似这样：

```tsx
export function StackScreenViewExample() {
  const ctx = useStackNavigatorContext();

  return (
    <>
      {/* Stack配置，实际上是改变了Modal Status Bar */}
      <StackConfig title={'标题'}>

      {/* ... */}

    </>
  );
}
```

StatusBar，会出现在以下三个地方，因此，也只有以下三个地方，支持Stack Navigator：

- Modal中央模态窗
- Drawer Web端右侧的抽屉
- AppStandalone独立页：如/mission/XXX, /record/XXX这些不需要空间站的独立页

### 客户端tRPC离线缓存 RPC Caching

❌ 未实现

为了保证用户丝滑的体验，在手机端，一些高频操控GUI界面用户体验，我们希望帮助用户做到「离线感」。

因此，一些客户端对服务器的远程请求，要缓存到本地，实现「飞秒级别」加载。

![RPC Caching](./images/cache-client-rpc.drawio.svg)
[cache-client-rpc.drawio.svg](./images/cache-client-rpc.drawio.svg)

[BDD故事](./bdd/cache.md)

RPC Caching是发生在客户端tRPC时候的缓存，服务端缓存请移步浏览服务端数据缓存。

### Webpack打包分析

进入apps/web，执行`pnpm run build-analyze`，会生成edge、client、nodejs三份webpack打包报告。

## DevOps 运维部署

用了Vercel部署：

```bash
# 部署到线上staging环境
npm run staging
```

### DevOps & Deployment部署

![Vercel](./images/deployment.drawio.svg)

### 中国域名及备案问题

中国域名备案问题查看Issue：https://github.com/vikadata/bika/issues/633

由于我们使用国外的云服务，中国的访问速度会变慢，用动态DNS、境内动态CDN来加速境内用户访问速度，同时也要备案。

```gherkin
Feature: 使用阿里云全球加速器提高访问速度

  Scenario: 用户在中国访问bika.ai
    Given 用户在中国
    When 用户输入bika.ai
    Then 基于地理位置的Geo DDNS，解析IP成*******，是境内的动态加速器
    Then 用户的请求将通过阿里云的全球网络进行反向代理，转发到国际（bika-ai.vercel.com)
    And 访问延迟将会减少
    And 用户可以更快地访问bika.ai

  Scenario: 用户在其他国家使用全球加速器
    Given 用户在其他国家
    When 用户输入bika.ai
    Then 基于地理位置的Geo DDNS，解析IP成bika-ai.vercel.com，即国际站

    Give Vercel自带全球动态加速和边缘计算
    When 用户使用所在国家的主要IP来访问全球加速器
    Then 用户可以直接连接到全球加速器
    And 访问速度将会提高

```

### 版本号与部署自动化

Bika会根据分支、版本号，进行运维、部署等，有以下约定：

```gherkin
Feature: 自动更新packages版本号

  # 首先，我们遵循Git Flow标准工作流
  # 假设现在你是v0.1.1-alpha.1

  Scenario: PR、合并分支时自动更新版本号
    When 当你的develop分支合并后 (merged)
    Then CI会对你的版本号进行pre-release修改 （即alpha后的数字）
      # 如v0.1.1-alpha.1 -> v0.1.1-alpha.2
    Then 版本号v0.1.1-alpha.2部署到了dev.bika.ai (集成环境)
      # 详情查看.github/workflows/deploy-dev.yaml

  Scenario: 从develop分支发版
    # 三位版本号不变，alpha改beta

    When 你在develop分支，执行make release
    Then 根据指示操作
    Then 我创建了一个新的release分支，叫release/0.1.1
      # release/0.1.1版本号为0.1.1-beta.0
      # develop的分支，命令会提醒，是否升级成0.1.2或0.2.0

    When release/0.1.1分支创建后
    Then 自动部署staging.bika.ai

    Then 你收到了2个新的PR
          A PR是develop-pr/release/0.1.1 -> develop，主要是更新升级过的版本号
          B PR是release/0.1.1 -> main，准备发布生产环境，谨慎审阅

    When 你处理PR B，合并到main
    Then main分支自动部署bika.ai

  Scenario: 从非develop分支发版
    # 会提升第三位版本号

    When 你不在develop分支，执行make release
    Then 根据提示，会修改patch版本号（第三位），并标记beta
      # 比如，你在分支release/0.1.1，则会创建分支release/0.1.2
      # 比如，你在分支chore/something，库版本号是0.1.1-alpha.2，则会创建分支release/0.1.2

    When 你创建了新的release/0.1.2分支
    Then 部署staging.bika.ai，版本号提升为0.1.2-beta.1
    Then 收到新的PR，提示develop/release/0.1.2 -> develop更新代码(通常会发生版本号冲突)

```

### Edge边缘函数服务

通常放置一些，与第三方整合的、需要联调、需要独立化的云服务，如一些需要callback、telegram bots。
使用Vercel Function，执行以下命令：

```
make deploy-edge
```

一般用你自己的账号就行了，因为只用于测试，如需部署到集成环境部署，请合并到develop。

  <!-- Scenario: 从release/*分支发新版
    When 你在release分支，执行make release
    Then 版本号变成 0.1.2-beta.0 -->
<!--

    When 当你从staging分支向main分支合并后
    Then CI会修改你的pre-release
      # Example 如v0.1.2-beta.2 -> v0.1.2-release.0
    Then 当你把PR合并到main后，版本号v0.1.2-release.0部署到bika.ai
    Then 当你把PR合并到main后，会自动打tag v0.1.2-release.0 -->

### 共享开发机 & CI机 & 肉鸡

> 一些内网的机器，经过内网穿透，公网可访问。

| 机器         | 架构             | 穿透方式          | 命令                        |
| ------------ | ---------------- | ----------------- | --------------------------- |
| mac-1        | X64/Macbook      | cloudflare tunnel | ssh <EMAIL>   |
| mac-2        | X64/Macbook      | cloudflare tunnel | ssh <EMAIL>   |
| mac-3        | X64/70GBMem/iMac | cloudflare tunnel | ssh <EMAIL>   |
| linux-1      | X64/PC/Debian    | cloudflare tunnel | ssh <EMAIL> |
| linux-2      | X64/PC/Debian    | cloudflare tunnel | ssh <EMAIL> |
| linux-sentry | ?                | ?                 | ?                           |
| linux-elk    | ?                | ?                 | ?                           |

ngrok的端口会服务器重启后，不定期变更。

Cloudflare Tunnel稳定且速度更快，但是使用前，需安装cloudflared代理和配置~/.ssh/config，[查看配置文档](https://developers.cloudflare.com/cloudflare-one/connections/connect-networks/use-cases/ssh/#2-connect-as-a-user)

参考`~/.ssh/config`的配置:

```bash
Host mac-1.aitable.ai
ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h

Host mac-2.aitable.ai
ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h

Host linux-1.aitable.ai
ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h

Host linux-2.aitable.ai
ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h

```

### Mac CI机初始化装机

Mac下执行：

```bash

# Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 同时会安装xcode工具集
brew install node
# docker环境+linux环境
brew install colima

# 设置成Github Runner
# ...

# 启动容器, 以支持 docker-compose volume
colima delete
colima start --vm-type=vz --mount-type=virtiofs
# 进入Linux
colima ssh
```

`colima ssh`后，在Linux执行：

```bash
# 安装一些基本工具，确保Github Runner能正常运行
sudo apt-get install curl zip unzip tar bzip2 make docker-compose rsync

# Docker Machine
# Go ->>> https://docs.docker.com/engine/install/debian/#install-using-the-repository

# Github Runner
# Go ->>> https://docs.github.com/en/actions/hosting-your-own-runners/managing-self-hosted-runners/configuring-the-self-hosted-runner-application-as-a-service

```

## Big Data大数据开发指南

大数据部分属于运维和架构的交汇点，在日常开发中，服务端开发可以完全忽略大数据部分的存在。

因为了方便功能迭代的和保持大数据部分的简单易调试，在架构上做了很多考虑。

- OLTP开发环境: 开发环境下，`make db`命令只会启动OLTP业务数据库（MongoDB和PostgreSQL)，所有东西都在OLTP运行；
- OLAP开发环境: 在开发环境下，使用`make db-olap`激活大数据的开发环境，本地会启动Doris、Elastic Search等大数据库，再本地执行`make dev-cron`命令，会调用http://localhost:3000/api/cron/olap，激活大数据消费者，对OLTP数据消费，并挪到OLAP；
- 生产环境: 线上环境会使用更多的运维组件，如Flink CDC等，传到Kafka，最后环节依然是/api/olap的代码进行消费处理；

![Big Data Search Engine](./images/dev-bigdata-search-engine.drawio.svg)

### OLAP数据迁移消费者

考虑到高可用的大数据环境的复杂度，最终的消费者环节不用复杂的工具，用本身的业务代码能承接

执行命令:

```bash
make dev-cron
```

该命令实质就行 `curl http://localhost:3000/api/cron/olap` 再开发模式下不需要依赖基于CDC的工具，是纯OLTP（MongoDB、PostgreSQL）的消费者，根据updatedAt字段，进行消费。

生产环境时考虑变为Kafka消费者，如Airbyte、Debezium、Flink CDC、Kafka。

### 大数据功能及开发 OLAP & Big Data & Search

OLTP事务数据库，会通过数据流同步到OLAP大数据库（Elastic Search、ClickHouse），
再根据不同的场景功能，实施不同的策略:

![](./images/dev-bigdata-search-engine.drawio.svg)

数据同步/写入策略:

| Source OLTP | Table       | Target OLAP    | Index               |
| ----------- | ----------- | -------------- | ------------------- |
| MongoDB     | SearchIndex | Elastic Search | bika-{indexName}-\* |

双查，只既查OLAP，也查OLTP，并对两种PO进行合并，形成VO，返回客户端。
之所以要双查，有如下考虑：

- 成本：最终生产环境，大数据不那么“热”，是放到私有云中的，避免超高昂的云存储费用，冷热分离；
- 规格：低级别的付费计划，如免费等，由于其数据量限制，因此是不会用到大数据库的能力，如免费版审计日志只保留7天、数据行限制、没有全局搜索功能等；
- 私有化部署：最终的一些私有化部署客户，是不带有大数据功能的，只有OLTP，要保证没有OLAP也可以运行；

数据调用策略:

| 功能/业务        | 策略              | OLTP       | Table                                | OLAP           | Table                   |
| ---------------- | ----------------- | ---------- | ------------------------------------ | -------------- | ----------------------- |
| 审计日志         | 双搜              | MongoDB    | SearchIndex with Index Name auditLog | Elastic Search | bika-{indexName}-\*     |
| 用户追踪         | 双搜              | MongoDB    | SearchIndex with Index Name track    | Elastic Search | bika-{indexName}-\*     |
| 空间站搜索       | OLAP搜            | -          | -                                    | Elastic Search | bika-space-{spaceId}-\* |
| 数据表搜索       | OLTP搜            | MongoDB    | 内部搜索                             | -              | -                       |
| Dashbord图表     | 看情况            | MongoDB    | DatabaseRecords                      | Doris          | DatabaseRecords         |
| API用量统计      | OLTP汇总,OLAP统计 | PostgreSQL | 汇总                                 | OLAP           | ClickHouse              |
| 用户本地快速搜索 | OLTP返回IndexDB   | IndexDB    | 根据客户端拥有索引，从服务端推送     | -              | -                       |

### AI搜索引擎

通过定时的消费者(/api/cron/olap)，将MongoDB、PostgresSQL数据，索引到Elastic Search中，实现空间站搜索、单表搜索、审计搜索、日志追踪等；
索引Elastic Search过程中，调用私有化部署的Ollama Emedding API，实现AI语义化搜索的支持；

一些分析型数据，如API调用次数等，挪到Doris中，实现用量查询；

开发环境中，TypeScript定时函数，直接消费MongoDB、PostgreSQL，迁移到Elastic Search和Doris。
生产环境中，加上Flink CDC + Kafka，同样的TypeScript定时函数，消费Kafka。

### Airbyte本地开发环境

使用Airbyte作为数据ETL迁移工具，适合「整体数据迁移」的场景，如PostgreSQL完整迁移到Doris。

本地开发环境运行方法如下：

```bash
# 自动抓取其工程和docker-compose.yaml文件
make airbyte-install

# 本地启动，打开http://localhost:8000，网址上有个Workspace ID，复制它
make airbyte-start

# 进入variables文件，把workspace Id复制进来，或env传递 terrafrom
vi scripts/airbyte/variables.tf

# 本地使用将数据源、数据目的地配置好
make airbyte-migrate

```

请提前安装Terraform，需要点Terraform知识，如本地的tfstate文件增删等。

### 用量统计 Usages

用量统计，根据账单日进行计算，

举个例子，以API调用为例，在MongoDB中涉及两个表

- UsageResult表：统计的结果；
- UsageAPI：记录每一次API调用；
- UsageAttachment：记录每次附件的引用；
- ...

UsageResult表，在统计完成以后生成记录；
UsageAPI原始数据在统计完成之后，会被挪移到Doris大数据库。

因此，我们会有一共有以下统计方法：

- 即时结算(Immediate Processing)：当发生事务之后，立刻对“余额”进行修改，如API用量在OLTP里加减特定数，这个数通常放在如space usages表里
- 日终结算（Day-End Processing)：每日统计一下UsageAPI，记到一个UsageResult表里；
- 月终结算(Month-End Processing)：分两步
  - 对这个月的日终结算，进行统计；
  - 进入OLAP，对整个月进行对数；

以上机制，适用场景：

1. API用量
2. 附件存储用量
3. BKC余额
4. ..等等

如果我还是免费用户，也要有个月份账单日，就是空间站创建日。

## 自动化Storybook / 帮助中心文档Autodocs

帮助中心，会读取代码配置，并生成所有的文档，比如：https://dev.bika.ai/en/help/guide/automation-trigger/scheduler

1. **帮助中心组件配置**: 配置feature配置：Database Field, Automation Action, Automation Trigger等的配置，放在domains/story-components和config/client目录下；
1. **Storybook展示**: storybook的\_features栏目，会穷举config，渲染所有的组件；
1. **Story Component**: 一个`没有参数`的组件，注意，没有参数，直接渲染的，内置对应value的state，供Storybook和Help帮助中心进行读取展示；
1. **默认值函数**：每个BO提供一个默认值，如`defaultDatabaseFieldBO`，来为上述的Story Component提供默认状态值；
1. **Types Form Input**: 细节表单组件，把XXXInput组件放到Story Component里面，确保正确；
1. **BO Schema**: 业务组件，也是用来配置模板的资源，在上述整个过程中，梳理BO Schema是正确的；
1. **Editor编辑器**：上面流程正确的话，Storybook就能检查所有的前端交互是否正确，前端的Editor和前端组件基本都正确，就剩后端接口是否对的问题了；
1. **整体进度**：打开Storybook或帮助中心，就知道哪个没做，哪个做了，整体进度是怎样的，前端哪里有BUG；

其实，只有以下代码：

```typescript

// 这个是Story Component组件，没有参数，这里通常由产品配置好
export function FindMembersActionStoryComponent() {
  const localeContext = useLocale();
  const defaultValue: FindMembersAction = useMemo(
    () => defaultAutomationBO({type: 'FIND_MEMBERS'}), // 默认值生成函数，这里通常由后端提供
    [localeContext],
  );
  // 这里是Types Form组件，这里通常由前端做好组件
  return <FindMembersActionBOInput value={defaultValue} onChange={helpOnClick()} />;
}

// 配置帮助中心、Storybook，这个类型，用哪个组件显示，这个通常由产品配置好
export const actionsStoryComponentsConfig: Record<ActionType, React.FC | undefined> = {
  FIND_MEMBERS: FindMembersActionStoryComponent,
  FIND_RECORDS: undefined,
  CREATE_RECORD: undefined,
  UPDATE_RECORD: undefined,
  FIND_DASHBOARD: undefined,
  FIND_WIDGET: undefined,
  FIND_MISSIONS: undefined,
  CREATE_MISSION: undefined,
  RUN_SCRIPT: undefined,
  WEBHOOK: undefined,
  WECOM_WEBHOOK: undefined,
}
```

## 压力测试 Autocannon

执行命令：

```bash
HOSTNAME=https://dev.bika.ai make bench-api
```

就开始压力测试了，通过修改环境变量，能测试不同的网址。

你可以本地启动项目进行压测：

```bash
make dev
make bench-api
```

这个直接测试http://localhost:3000

压力测试、API测试脚本在：`scripts/autocannon`目录。

## What's more...

### Zen

![Zen](./images/zen.drawio.svg)

### 参考文章:

https://firebase.google.com/docs/cloud-messaging?hl=zh-tw
https://docs.expo.dev/push-notifications/sending-notifications/
